{"root": {"type": "decision", "feature": {"name": "amount", "type": "numeric", "column_number": 4, "values": [], "numeric_range": {"min": 0, "max": 450}, "max": 1000}, "threshold": 104.75, "left": {"type": "decision", "feature": {"name": "event_type", "type": "categorical", "column_number": 3, "values": [], "categorical_values": ["purchase", "logout", "view", "login"]}, "threshold": 0, "categories": {"login": {"type": "leaf", "threshold": 0, "prediction": "0", "class_distribution": {"0": 2}, "samples": 2, "confidence": 1, "impurity": 0}, "logout": {"type": "leaf", "threshold": 0, "prediction": "0", "class_distribution": {"0": 3}, "samples": 3, "confidence": 1, "impurity": 0}, "view": {"type": "leaf", "threshold": 0, "prediction": "1", "class_distribution": {"1": 3}, "samples": 3, "confidence": 1, "impurity": 0}}, "class_distribution": {"0": 5, "1": 3}, "samples": 8, "confidence": 0.625, "impurity": 0.9544340029249649}, "right": {"type": "leaf", "threshold": 0, "prediction": "1", "class_distribution": {"1": 7}, "samples": 7, "confidence": 1, "impurity": 0}, "class_distribution": {"0": 5, "1": 10}, "samples": 15, "confidence": 0.6666666666666666, "impurity": 0.9182958340544896}, "features": {"amount": {"name": "amount", "type": "numeric", "column_number": 4, "values": []}, "event_date": {"name": "event_date", "type": "datetime", "column_number": 1, "values": []}, "event_time": {"name": "event_time", "type": "datetime", "column_number": 2, "values": []}, "event_timestamp": {"name": "event_timestamp", "type": "datetime", "column_number": 0, "values": []}, "event_type": {"name": "event_type", "type": "categorical", "column_number": 3, "values": []}}, "features_by_index": [{"name": "event_timestamp", "type": "datetime", "column_number": 0, "values": []}, {"name": "event_date", "type": "datetime", "column_number": 1, "values": []}, {"name": "event_time", "type": "datetime", "column_number": 2, "values": []}, {"name": "event_type", "type": "categorical", "column_number": 3, "values": []}, {"name": "amount", "type": "numeric", "column_number": 4, "values": []}], "target_type": "categorical", "target_column": "status", "config": {"max_depth": 10, "min_samples": 2, "target_type": "categorical", "criterion": "entropy", "max_features": -1}, "node_count": 6, "leaf_count": 4, "depth": 3}
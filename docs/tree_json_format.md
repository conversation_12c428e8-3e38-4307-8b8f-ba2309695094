# Decision Tree JSON Format Documentation

This document describes the JSON format used by <PERSON><PERSON><PERSON><PERSON> for serializing decision trees. This format is essential for the prediction component, as it contains both the tree structure and feature validation information.

## Overview

The decision tree is serialized as a JSON object containing:
- **Tree structure**: Nodes, splits, and predictions
- **Feature definitions**: Types, ranges, and validation rules
- **Metadata**: Configuration and statistics
- **Validation data**: Required for input validation during prediction

## Top-Level Structure

```json
{
  "root": { /* Root node of the tree */ },
  "features": { /* Feature definitions by name */ },
  "features_by_index": [ /* Features ordered by column index */ ],
  "target_type": "categorical|numeric",
  "target_column": "category",
  "config": { /* Tree configuration */ },
  "node_count": 4,
  "leaf_count": 3,
  "depth": 2
}
```

## Node Structure

### Decision Nodes (Internal Nodes)

Decision nodes split the data based on feature values:

```json
{
  "type": "decision",
  "feature": { /* Feature used for splitting */ },
  "threshold": 30.0,           // For numeric features
  "categories": { /* For categorical features */ },
  "left": { /* Left child (≤ threshold) */ },
  "right": { /* Right child (> threshold) */ },
  "prediction": null,          // Always null for decision nodes
  "class_distribution": { /* Class counts at this node */ },
  "samples": 52,               // Total samples at this node
  "confidence": 0.442,         // Prediction confidence (0-1)
  "impurity": 0.650           // Node impurity measure
}
```

### Leaf Nodes (Terminal Nodes)

Leaf nodes contain the final predictions:

```json
{
  "type": "leaf",
  "feature": null,             // Always null for leaf nodes
  "threshold": 0,              // Always 0 for leaf nodes
  "categories": null,          // Always null for leaf nodes
  "left": null,                // Always null for leaf nodes
  "right": null,               // Always null for leaf nodes
  "prediction": "young",       // Final prediction value
  "class_distribution": {      // Class distribution
    "young": 15,
    "old": 2
  },
  "samples": 17,               // Total samples in this leaf
  "confidence": 0.882,         // Prediction confidence
  "impurity": 0.194           // Node impurity
}
```

## Target Column Information

The `target_column` field specifies which column was used as the target during training. This is crucial for prediction because:

1. **Input Validation**: Ensures prediction input excludes the target column
2. **Column Mapping**: Helps map input columns correctly to features
3. **Data Preparation**: Guides users on how to prepare prediction data

```json
{
  "target_type": "categorical",
  "target_column": "species",
  // ... other fields
}
```

**Important**: When preparing data for prediction, the input CSV should contain all feature columns but **exclude** the target column.

## Feature Definitions

Features contain validation information crucial for prediction:

### Numeric Features

```json
{
  "name": "age",
  "type": "numeric",
  "column_number": 0,
  "values": [],                // Deprecated, always empty
  "categorical_values": null,  // Always null for numeric
  "numeric_range": {
    "min": 18.0,              // Minimum valid value
    "max": 65.0               // Maximum valid value
  },
  "date_range": null          // Always null for numeric
}
```

### Categorical Features

```json
{
  "name": "weather",
  "type": "categorical",
  "column_number": 0,
  "values": [],                // Deprecated, always empty
  "categorical_values": [      // Valid categorical values
    "sunny",
    "rainy", 
    "cloudy"
  ],
  "numeric_range": null,       // Always null for categorical
  "date_range": null          // Always null for categorical
}
```

## Splitting Logic

### Numeric Features
- **Left child**: samples where `value ≤ threshold`
- **Right child**: samples where `value > threshold`
- **Threshold**: The split point determined during training

### Categorical Features
- **Categories map**: Each valid category maps to a child node
- **No threshold**: Categorical splits don't use thresholds
- **Direct mapping**: Input category directly determines the path

## Prediction Validation Requirements

For robust prediction, validate input data against the tree structure:

### 1. Feature Validation
```javascript
// Validate numeric feature
if (feature.type === "numeric") {
  const value = parseFloat(inputValue);
  if (value < feature.numeric_range.min || value > feature.numeric_range.max) {
    throw new Error(`Value ${value} outside valid range [${feature.numeric_range.min}, ${feature.numeric_range.max}]`);
  }
}

// Validate categorical feature
if (feature.type === "categorical") {
  if (!feature.categorical_values.includes(inputValue)) {
    throw new Error(`Invalid category '${inputValue}'. Valid values: ${feature.categorical_values.join(', ')}`);
  }
}
```

### 2. Column Mapping and Target Validation
```javascript
// Validate input structure - should NOT include target column
const requiredColumns = tree.features_by_index.length;
if (inputRow.length !== requiredColumns) {
  throw new Error(`Input has ${inputRow.length} columns, expected ${requiredColumns} (excluding target column '${tree.target_column}')`);
}

// Ensure target column is not included in input
if (tree.target_column && inputHeaders && inputHeaders.includes(tree.target_column)) {
  throw new Error(`Input should not include target column '${tree.target_column}'. Remove it for prediction.`);
}

// Map input columns to features
const featureValues = {};
tree.features_by_index.forEach((feature, index) => {
  featureValues[feature.name] = inputRow[index];
});
```

### 3. Tree Traversal
```javascript
function predict(node, featureValues) {
  if (node.type === "leaf") {
    return {
      prediction: node.prediction,
      confidence: node.confidence,
      distribution: node.class_distribution
    };
  }
  
  const feature = node.feature;
  const value = featureValues[feature.name];
  
  if (feature.type === "numeric") {
    const numValue = parseFloat(value);
    const nextNode = numValue <= node.threshold ? node.left : node.right;
    return predict(nextNode, featureValues);
  } else {
    const nextNode = node.categories[value];
    if (!nextNode) {
      throw new Error(`No path for category '${value}' in feature '${feature.name}'`);
    }
    return predict(nextNode, featureValues);
  }
}
```

## Example Files

See the example files for complete tree structures:
- **[minimal_tree_example.json](examples/minimal_tree_example.json)** - Simple single-feature tree (good starting point)
- **[simple_tree_example.json](examples/simple_tree_example.json)** - Multi-level tree with numeric features
- **[categorical_tree_example.json](examples/categorical_tree_example.json)** - Mixed numeric and categorical features

## Data Preparation for Prediction

When preparing data for prediction, follow these guidelines:

### Training vs Prediction Data Structure

**Training Data** (example):
```csv
age,income,education,category
25,45000,bachelor,young
35,75000,master,middle
55,95000,phd,old
```

**Prediction Data** (example):
```csv
age,income,education
30,60000,bachelor
40,80000,master
```

**Key Points**:
- Remove the target column (`category` in this example) from prediction data
- Keep all feature columns in the same order
- Ensure feature values are within the trained ranges/categories

### Column Validation Process

1. **Check column count**: `input_columns = feature_count` (excluding target)
2. **Validate column names**: Ensure no target column is present
3. **Map to features**: Use `features_by_index` for correct mapping
4. **Validate values**: Check ranges for numeric, categories for categorical

## Key Points for Implementation

1. **Target column exclusion is mandatory** - Input must NOT include the target column
2. **Feature validation is mandatory** - Always validate input against feature ranges/categories
3. **Column order matters** - Use `features_by_index` to map input columns correctly
4. **Handle missing paths** - Categorical features may not have paths for all possible values
5. **Confidence scores** - Use leaf confidence for prediction reliability assessment
6. **Class distributions** - Available for probability estimates in classification tasks

## Common Validation Errors

- **Out of range values**: Numeric values outside the trained range
- **Unknown categories**: Categorical values not seen during training
- **Missing columns**: Input with fewer columns than expected
- **Type mismatches**: Non-numeric values for numeric features
- **Missing paths**: Categorical values without corresponding tree paths

# Logger Configuration
# This file contains configuration for the Mulberri logging system.
# These settings can be changed at runtime without recompilation.

logger:
  # Directory where log files will be stored
  log_folder: "logs"

  # Maximum file size in MB before rotation
  max_size: 10

  # Environment: "development" or "production"
  # development: console format with colors, more verbose
  # production: JSON format, structured logging
  environment: "development"

  # Whether to output logs to console (stdout/stderr)
  enable_console: true

  # Application name used as prefix for log files
  app_name: "mulberri"

  # Log rotation settings
  Max_backups: 7
  Max_age: 5

  # Whether to use colored output in console (development only)
  enable_colors: true

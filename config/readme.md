# Config Folder

## Contents

- **`train.yaml`**  
  Configuration for model training. Includes hyperparameters such as max depth, pruning thresholds, and feature selection options.

- **`benchmark.json`**  
  Parameters used during performance testing and benchmarking, such as dataset paths and expected latency thresholds.

- **`lint.yml`**  
  Configuration for linting and code quality tools (e.g., golangci-lint).

## Purpose

The `config/` directory helps separate runtime and build-time configuration from the application logic. This promotes:

- Consistency across environments (dev, test, CI)
- Easier updates without touching core logic
- Better organization of project metadata and tooling options

## Guidelines

- Keep environment-agnostic configs in this folder.
- Sensitive information (e.g., credentials) **should not** be stored here.
- Follow naming conventions: use lowercase with dashes or underscores.

## Related

- [cmd/mulberri](../cmd/mulberri) — Entry point using these configs
- [scripts](../scripts) — May reference configs for linting or testing automation

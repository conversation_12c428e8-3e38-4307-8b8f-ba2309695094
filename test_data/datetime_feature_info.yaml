# Feature info file for datetime testing
# This file defines the feature types and constraints for the datetime test dataset

# DateTime feature - full timestamp with range validation
event_timestamp:
  type: datetime
  min: 20230101000000  # January 1, 2023 00:00:00
  max: 20241231235959  # December 31, 2024 23:59:59

# DateTime feature - date only (no time component)
event_date:
  type: datetime
  min: 20230101        # January 1, 2023 (date only)
  max: 20241231        # December 31, 2024 (date only)

# DateTime feature - time only (no date component)
event_time:
  type: datetime
  min: 000000          # 00:00:00 (time only)
  max: 235959          # 23:59:59 (time only)

# Categorical feature - event types
event_type:
  type: nominal
  values:
    - "login"
    - "logout"
    - "purchase"
    - "view"

# Numeric feature - transaction amount
amount:
  type: numeric
  min: 0.0
  max: 1000.0

# Categorical feature - user status
status:
  type: nominal
  values:
    - "active"
    - "inactive"
    - "pending"

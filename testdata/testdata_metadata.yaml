age:
  type: numeric
  min: 18.0
  max: 79.0
income:
  type: numeric
  min: 20055.0
  max: 149948.0
customer_id:
  type: numeric
  min: 106154.0
  max: 999187.0
height_cm:
  type: numeric
  min: 124.7
  max: 217.9
weight_kg:
  type: numeric
  min: 50.0
  max: 100.0
score_0_1:
  type: numeric
  min: 0.0
  max: 1.0
score_0_100:
  type: numeric
  min: 0.1
  max: 99.9
price_dollars:
  type: numeric
  min: 24.27
  max: 9994.61
color:
  type: nominal
  values:
  - Blue
  - Green
  - Red
  - Yellow
department:
  type: nominal
  values:
  - Customer_Service
  - Engineering
  - Finance
  - HR
  - IT
  - Legal
  - Marketing
  - Operations
  - Research
  - Sales
city:
  type: nominal
  values:
  - Austin
  - Boston
  - Charlotte
  - Chicago
  - Columbus
  - Dallas
  - Denver
  - Detroit
  - El_Paso
  - Fort_Worth
  - Houston
  - Indianapolis
  - Jacksonville
  - Los_Angeles
  - Nashville
  - New_York
  - Philadelphia
  - Phoenix
  - San_Antonio
  - San_Diego
  - San_Francisco
  - San_Jose
  - Seattle
  - Washington
country:
  type: nominal
  values:
  - Australia
  - Canada
  - France
  - Germany
  - Japan
  - Mexico
  - UK
  - USA
zip_code:
  type: nominal
  values:
  - '10001'
  - '19101'
  - '60601'
  - '77001'
  - '78201'
  - '85001'
  - '90210'
  - '92101'
is_premium:
  type: nominal
  values:
  - '0'
  - '1'
has_subscription:
  type: nominal
  values:
  - 'False'
  - 'True'
owns_car:
  type: nominal
  values:
  - 'No'
  - 'Yes'
gender:
  type: nominal
  values:
  - Female
  - Male
employment_status:
  type: nominal
  values:
  - Employed
  - Unemployed
education:
  type: nominal
  values:
  - Bachelor
  - High_School
  - Master
  - PhD
satisfaction_rating:
  type: numeric
  min: 1.0
  max: 5.0
shirt_size:
  type: nominal
  values:
  - L
  - M
  - S
  - XL
  - XS
income_bracket:
  type: nominal
  values:
  - High
  - Low
  - Medium
  - Very_High
performance_grade:
  type: nominal
  values:
  - A
  - B
  - C
  - D
  - F
signup_date:
  type: datetime
  min: '2019-01-02T00:00:00.000Z'
  max: '2024-12-27T00:00:00.000Z'
last_login:
  type: datetime
  min: '2019-01-06T01:24:28.000Z'
  max: '2024-12-26T09:56:54.000Z'
preferred_time:
  type: datetime
  min: '00:02:15.000'
  max: '23:59:52.000'
y:
  type: nominal
  values:
  - 'No'
  - 'Yes'

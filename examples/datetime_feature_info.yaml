# Example feature info file demonstrating datetime feature type support
# This file shows how to define different types of features including datetime

# Datetime feature - for timestamp data with range validation
event_timestamp:
  type: datetime
  min: 20230101000000  # January 1, 2023 00:00:00
  max: 20231231235959  # December 31, 2023 23:59:59

# Categorical feature - for discrete values
event_type:
  type: nominal
  values:
    - "login"
    - "logout"
    - "purchase"
    - "view"

# Numeric feature - for continuous values
amount:
  type: numeric
  min: 0.0
  max: 10000.0

# Another datetime feature - for date-only data (no range constraints)
created_date:
  type: datetime

# Another categorical feature
status:
  type: nominal
  values:
    - "active"
    - "inactive"
    - "pending"

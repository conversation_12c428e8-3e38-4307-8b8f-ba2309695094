package main

import (
	"context"
	"fmt"
	"log"

	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/pkg/models"
)

// MockDataset implements the Dataset interface for demonstration
type MockDataset struct {
	features map[string][]interface{}
	targets  []string
	indices  []int
	size     int
}

func (m *MockDataset) GetSize() int {
	return m.size
}

func (m *MockDataset) GetFeatureValue(sampleIdx int, feature *models.Feature) (interface{}, error) {
	if values, exists := m.features[feature.Name]; exists {
		if sampleIdx >= 0 && sampleIdx < len(values) {
			return values[sampleIdx], nil
		}
	}
	return nil, fmt.Errorf("feature value not found for sample %d, feature %s", sampleIdx, feature.Name)
}

func (m *MockDataset) GetTarget(sampleIdx int) (string, error) {
	if sampleIdx >= 0 && sampleIdx < len(m.targets) {
		return m.targets[sampleIdx], nil
	}
	return "", fmt.Errorf("target not found for sample %d", sampleIdx)
}

func (m *MockDataset) GetIndices() []int {
	return m.indices
}

func (m *MockDataset) Subset(indices []int) training.Dataset[string] {
	return &MockDataset{
		features: m.features,
		targets:  m.targets,
		indices:  indices,
		size:     len(indices),
	}
}

func main() {
	fmt.Println("DateTime Feature Type Example")
	fmt.Println("=============================")

	// Create a datetime feature
	datetimeFeature, err := models.NewFeature("event_timestamp", models.DateTimeFeature, 0)
	if err != nil {
		log.Fatalf("Failed to create datetime feature: %v", err)
	}

	// Set a range for the datetime feature (optional)
	err = datetimeFeature.SetRange(20230101000000, 20231231235959)
	if err != nil {
		log.Fatalf("Failed to set range for datetime feature: %v", err)
	}

	fmt.Printf("Created datetime feature: %s (type: %s)\n", datetimeFeature.Name, datetimeFeature.Type)
	fmt.Printf("Range: %.0f to %.0f\n", datetimeFeature.Min, datetimeFeature.Max)

	// Create sample data with datetime strings
	dataset := &MockDataset{
		features: map[string][]interface{}{
			"event_timestamp": {
				"2023-01-15T09:30:00Z", // Early morning event
				"2023-06-20T14:45:30Z", // Afternoon event
				"2023-12-25T18:00:00Z", // Evening event
				"2023-03-10T11:15:45Z", // Mid-morning event
				"2023-09-05T16:30:15Z", // Late afternoon event
			},
		},
		targets: []string{"low", "high", "high", "medium", "high"},
		indices: []int{0, 1, 2, 3, 4},
		size:    5,
	}

	// Create a splitter
	splitter, err := training.NewC45Splitter[string]()
	if err != nil {
		log.Fatalf("Failed to create splitter: %v", err)
	}

	fmt.Println("\nTesting datetime feature conversion:")

	// Test the conversion function directly
	testValues := []interface{}{
		"2023-01-15T09:30:00Z",
		"2023-06-20T14:45:30Z",
		"2023-12-25",
		"18:00:00Z",
	}

	for _, value := range testValues {
		// Note: This is accessing an internal method for demonstration
		// In practice, the conversion happens automatically during splitting
		fmt.Printf("  %v -> [converted automatically during splitting]\n", value)
	}

	// Test splitting with the datetime feature
	fmt.Println("\nTesting datetime feature splitting:")

	ctx := context.Background()
	baseImpurity, err := splitter.CalculateImpurity(dataset)
	if err != nil {
		log.Fatalf("Failed to calculate base impurity: %v", err)
	}

	fmt.Printf("Base impurity: %.6f\n", baseImpurity)

	// Find the best split using the datetime feature
	split, err := splitter.FindBestSplit(ctx, dataset, []*models.Feature{datetimeFeature})
	if err != nil {
		log.Fatalf("Failed to find best split: %v", err)
	}

	if split != nil {
		fmt.Printf("Found split on feature '%s':\n", split.Feature.Name)
		fmt.Printf("  Threshold: %.0f\n", split.Threshold)
		fmt.Printf("  Information Gain: %.6f\n", split.InfoGain)
		fmt.Printf("  Gain Ratio: %.6f\n", split.GainRatio)
		fmt.Printf("  Left indices: %v\n", split.LeftIndices)
		fmt.Printf("  Right indices: %v\n", split.RightIndices)

		// Show what the threshold means in datetime terms
		fmt.Printf("  Threshold represents approximately: %s\n", formatThreshold(split.Threshold))
	} else {
		fmt.Println("No beneficial split found")
	}

	fmt.Println("\nDateTime feature type successfully integrated!")
}

// Helper function to format the threshold back to a readable datetime
func formatThreshold(threshold float64) string {
	// This is a simplified conversion for demonstration
	// In practice, you might want to convert back to a proper datetime
	thresholdStr := fmt.Sprintf("%.0f", threshold)
	if len(thresholdStr) >= 14 {
		year := thresholdStr[0:4]
		month := thresholdStr[4:6]
		day := thresholdStr[6:8]
		hour := thresholdStr[8:10]
		minute := thresholdStr[10:12]
		second := thresholdStr[12:14]
		return fmt.Sprintf("%s-%s-%sT%s:%s:%sZ", year, month, day, hour, minute, second)
	}
	return thresholdStr
}

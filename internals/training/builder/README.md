# TreeBuilder Package

The `builder` package provides decision tree construction using the C4.5 algorithm with interface-based architecture, error handling, and production features.

## Overview

The TreeBuilder component implements a modular, extensible architecture for recursive decision tree construction. It integrates the training package's C45Splitter with the models package's tree structures while providing pluggable components, validation, and error handling.

## Key Features

- Automatic type inference from generic parameters
- Interface-based architecture with pluggable components
- Structured logging with configurable levels and rich context
- Error handling with cross-parameter validation
- Unified configuration system with validation
- Extensive testing including integration and performance tests
- Thread safety with support for concurrent tree building
- Context cancellation for production environments

## Architecture

```
TreeBuilder[T] (Interface-Based Design)
├── NodeBuilder[T]          → Node construction (leaf/decision nodes)
├── StatisticsCalculator[T] → Class distribution & impurity calculation  
├── StoppingCriteria[T]     → Configurable stopping logic
├── ConfigValidator         → Cross-parameter validation
├── SplittingStrategy[T]    → C4.5 algorithm integration
├── Logger                  → Structured logging with levels
└── UnifiedTreeConfig       → Single source of configuration truth
```

## Core Components

### TreeBuilder[T] - Main Interface

```go
type TreeBuilder[T comparable] struct {
    // Interface-based components (pluggable & testable)
    nodeBuilder          NodeBuilder[T]
    statisticsCalculator StatisticsCalculator[T]
    stoppingCriteria     StoppingCriteria[T]
    configValidator      ConfigValidator
    splittingStrategy    SplittingStrategy[T]
    logger               Logger

    // Core components
    splitter *training.C45Splitter[T]
    config   BuilderConfig
}
```

### Interface-Based Design

```go
// Pluggable node construction
type NodeBuilder[T comparable] interface {
    BuildLeafNode(stats *NodeStatistics[T]) (*models.TreeNode, error)
    BuildDecisionNode(split *training.SplitResult[T], leftChild, rightChild *models.TreeNode) (*models.TreeNode, error)
}

// Configurable statistics calculation
type StatisticsCalculator[T comparable] interface {
    CalculateNodeStatistics(dataset training.Dataset[T]) (*NodeStatistics[T], error)
}

// Flexible stopping criteria
type StoppingCriteria[T comparable] interface {
    ShouldCreateLeaf(dataset training.Dataset[T], depth int, impurity float64) bool
}
```

### UnifiedTreeConfig - Single Source of Truth

```go
type UnifiedTreeConfig struct {
    MaxDepth            int                   `json:"max_depth"`
    MinSamplesSplit     int                   `json:"min_samples_split"`
    MinSamplesLeaf      int                   `json:"min_samples_leaf"`
    MinImpurityDecrease float64               `json:"min_impurity_decrease"`
    Criterion           models.SplitCriterion `json:"criterion"`
    TargetType          models.FeatureType    `json:"target_type"`
    EnableLogging       bool                  `json:"enable_logging"`
}
```

### Error Handling

```go
type BuilderError struct {
    Op     string // Operation that failed
    Field  string // Field that caused the error (optional)
    Value  string // Value that caused the error
    Reason string // Human-readable reason
    Err    error  // Underlying error (optional)
}

func (e *BuilderError) Error() string {
    if e.Field != "" && e.Value != "" {
        return fmt.Sprintf("builder %s failed for field '%s' (value: %s): %s",
            e.Op, e.Field, e.Value, e.Reason)
    }
    // ... contextual error formatting
}
```

## Usage

### Basic Usage with Type Inference

```go
package main

import (
    "context"
    "log"

    "github.com/berrijam/mulberri/internals/training"
    "github.com/berrijam/mulberri/internals/training/builder"
)

func main() {
    // Create C4.5 splitter
    splitter, err := training.NewC45Splitter[string]()
    if err != nil {
        log.Fatal(err)
    }

    // Automatic type inference
    builder, err := builder.NewTreeBuilderWithInference(splitter,
        builder.WithMaxDepth(8),
        builder.WithMinSamplesSplit(10),
        builder.WithLogging(true),
    )
    if err != nil {
        log.Fatal(err)
    }

    // Build with context cancellation
    ctx := context.Background()
    tree, err := builder.BuildTree(ctx, dataset, features)
    if err != nil {
        log.Fatal(err)
    }

    log.Printf("Built tree: %d nodes, %d leaves, depth %d",
        tree.NodeCount, tree.LeafCount, tree.Depth)
}
```

### Advanced Configuration with Cross-Parameter Validation

```go
// Validation prevents configuration errors
builder, err := builder.NewTreeBuilder(splitter,
    builder.WithMaxDepth(15),
    builder.WithMinSamplesSplit(20),    // Must be >= MinSamplesLeaf
    builder.WithMinSamplesLeaf(5),      // Cross-validated with MinSamplesSplit
    builder.WithMinImpurityDecrease(0.01),
    builder.WithCriterion(models.EntropyCriterion),
    builder.WithTargetType(models.CategoricalFeature),
    builder.WithLogging(true),
)
if err != nil {
    // Detailed error with field-level context
    if builderErr, ok := err.(*builder.BuilderError); ok {
        log.Printf("Configuration error in %s: %s", builderErr.Field, builderErr.Reason)
    }
    log.Fatal(err)
}
```

### Production Error Handling

```go
// Error handling
tree, err := builder.BuildTree(ctx, dataset, features)
if err != nil {
    if builderErr, ok := err.(*builder.BuilderError); ok {
        // Structured error information
        log.Printf("Operation: %s", builderErr.Op)
        log.Printf("Field: %s", builderErr.Field)
        log.Printf("Value: %s", builderErr.Value)
        log.Printf("Reason: %s", builderErr.Reason)

        // Error chaining support
        if builderErr.Err != nil {
            log.Printf("Root cause: %v", builderErr.Err)
        }
    }
    return
}
```

## Configuration Options with Validation

### Functional Options with Cross-Parameter Validation

```go
// Cross-parameter validation
func WithMinSamplesSplit(samples int) BuilderOption {
    return func(config *BuilderConfig) error {
        if samples < 2 {
            return &BuilderError{
                Op:     "configure_builder",
                Field:  "min_samples_split",
                Value:  fmt.Sprintf("%d", samples),
                Reason: "minimum samples split must be >= 2",
            }
        }

        // Cross-parameter validation
        if config.MinSamplesLeaf > 0 && samples < config.MinSamplesLeaf {
            return &BuilderError{
                Op:    "configure_builder",
                Field: "min_samples_split",
                Value: fmt.Sprintf("%d", samples),
                Reason: fmt.Sprintf("min_samples_split (%d) cannot be less than min_samples_leaf (%d)",
                    samples, config.MinSamplesLeaf),
            }
        }

        config.MinSamplesSplit = samples
        return nil
    }
}
```

### Type-Safe Configuration

```go
// Type inference with validation
func WithTargetType(targetType models.FeatureType) BuilderOption {
    return func(config *BuilderConfig) error {
        // Validates compatibility with generic type T
        config.TargetType = targetType
        return nil
    }
}

// Default configurations for common types
config := builder.DefaultUnifiedTreeConfigForType[string]()  // → CategoricalFeature
config := builder.DefaultUnifiedTreeConfigForType[int]()     // → NumericFeature
config := builder.DefaultUnifiedTreeConfigForType[float64]() // → NumericFeature
```

## Tree Building Process

### 1. Input Validation

```go
func (b *TreeBuilder[T]) validateBuildInputs(dataset training.Dataset[T], features []*models.Feature) error {
    var errors training.MultiError

    // Dataset size validation against configuration
    if dataset.GetSize() < b.config.GetMinSamplesSplit() {
        errors.Add(&BuilderError{
            Op:    "validate_build_inputs",
            Field: "dataset_size",
            Value: fmt.Sprintf("%d", dataset.GetSize()),
            Reason: fmt.Sprintf("dataset size (%d) is smaller than min_samples_split (%d)",
                dataset.GetSize(), b.config.GetMinSamplesSplit()),
        })
    }

    // Feature name uniqueness validation
    featureNames := make(map[string]int)
    for i, feature := range features {
        if prevIndex, exists := featureNames[feature.Name]; exists {
            errors.Add(&BuilderError{
                Op:    "validate_build_inputs",
                Field: "feature_names",
                Value: feature.Name,
                Reason: fmt.Sprintf("duplicate feature name '%s' found at indices %d and %d",
                    feature.Name, prevIndex, i),
            })
        }
        featureNames[feature.Name] = i
    }

    // Individual feature validation
    for i, feature := range features {
        if err := feature.Validate(); err != nil {
            errors.Add(&BuilderError{
                Op:     "validate_build_inputs",
                Field:  "feature_validation",
                Value:  feature.Name,
                Reason: fmt.Sprintf("feature '%s' validation failed: %v", feature.Name, err),
                Err:    err,
            })
        }
    }

    if errors.HasErrors() {
        return &BuilderError{
            Op:     "validate_build_inputs",
            Reason: "input validation failed",
            Err:    &errors,
        }
    }
    return nil
}
```

### 2. Node Building

```go
func (b *TreeBuilder[T]) buildNode(ctx context.Context, dataset training.Dataset[T],
    features []*models.Feature, depth int, nodeID string) (*models.TreeNode, error) {

    // Context cancellation check
    select {
    case <-ctx.Done():
        return nil, ctx.Err()
    default:
    }

    // Statistics calculation
    nodeStats, err := b.statisticsCalculator.CalculateNodeStatistics(dataset)
    if err != nil {
        return nil, &BuilderError{
            Op:     "build_node",
            Field:  "node_statistics",
            Reason: fmt.Sprintf("failed to calculate node statistics: %v", err),
            Err:    err,
        }
    }

    // Stopping criteria
    if b.stoppingCriteria.ShouldCreateLeaf(dataset, depth, nodeStats.Impurity) {
        return b.nodeBuilder.BuildLeafNode(nodeStats)
    }

    // Continue with split finding and decision node creation...
}
```

### 3. Numerical Stability

```go
// Numerical stability checks
func (sc *DefaultStatisticsCalculator[T]) calculateEntropyImpurity(
    classDistribution map[T]int, totalSamples int) float64 {

    if totalSamples == 0 {
        return 0.0
    }

    entropy := 0.0
    for _, count := range classDistribution {
        if count > 0 {
            prob := float64(count) / float64(totalSamples)
            entropy -= prob * math.Log2(prob)
        }
    }

    // Numerical stability validation
    if math.IsNaN(entropy) || math.IsInf(entropy, 0) {
        return 0.0 // Safe fallback for invalid calculations
    }

    return entropy
}
```

## Testing

### Test Coverage

```go
// Type safety testing
func TestTypeSafetyImprovements(t *testing.T) {
    // Tests automatic type inference
    // Tests type-safe conversion helpers
    // Tests compatibility validation
}

// Error handling testing
func TestErrorHandlingAndValidationImprovements(t *testing.T) {
    // Tests cross-parameter validation in options
    // Tests input validation
    // Tests consistent error wrapping
    // Tests configuration-dataset compatibility
}

// Integration testing with real components
func TestIntegrationWithRealSplitter(t *testing.T) {
    // Tests with actual C45Splitter instead of mocks
    // Tests algorithmic correctness properties
    // Tests performance with concurrent operations
}
```

### Property-Based Testing

```go
func TestDecisionTreeProperties(t *testing.T) {
    quick.Check(func(maxDepth int, samples int) bool {
        if maxDepth <= 0 || samples < 2 {
            return true // Skip invalid inputs
        }

        // Property: Tree depth should respect constraints
        tree, err := builder.BuildTree(ctx, dataset, features)
        actualDepth := calculateTreeDepth(tree.Root)
        return err == nil && actualDepth <= maxDepth
    }, nil)
}
```

## Performance Considerations

### Memory Efficiency

```go
// Efficient dataset subsetting (avoids data copying)
leftDataset := dataset.Subset(split.LeftIndices)   // Index-based, no copy
rightDataset := dataset.Subset(split.RightIndices) // Index-based, no copy
```

### Context Cancellation

```go
// Context handling throughout tree building
func (b *TreeBuilder[T]) buildBinaryChildren(ctx context.Context, ...) error {
    select {
    case <-ctx.Done():
        return ctx.Err()
    default:
    }

    // Check context before expensive operations
    leftChild, err := b.buildNode(ctx, leftDataset, features, depth+1, nodeID+"_L")
    // ...
}
```

### Structured Logging

```go
// Rich contextual logging
b.logger.Info("Starting tree construction",
    IntField("samples", dataset.GetSize()),
    IntField("features", len(features)),
    IntField("max_depth", b.config.GetMaxDepth()))

b.logger.Debug("Building node",
    StringField("node_id", nodeID),
    IntField("depth", depth),
    IntField("samples", dataset.GetSize()))
```

## Advanced Features

### Type Inference and Compatibility

```go
// Automatic type inference
func inferTargetType[T comparable]() models.FeatureType {
    var zero T
    switch any(zero).(type) {
    case string:
        return models.CategoricalFeature
    case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
        return models.NumericFeature
    default:
        return models.CategoricalFeature // Safe default
    }
}

// Type compatibility validation
func (b *TreeBuilder[T]) validateTypeCompatibility() error {
    var zero T
    targetType := b.config.GetTargetType()

    if targetType == models.NumericFeature {
        switch any(zero).(type) {
        case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
            return nil
        default:
            return &BuilderError{
                Op:     "validate_type_compatibility",
                Reason: fmt.Sprintf("generic type %T is not compatible with numeric target type", zero),
            }
        }
    }
    return nil
}
```

### Pluggable Components

```go
// Custom statistics calculator
type CustomStatisticsCalculator[T comparable] struct {
    // Custom implementation
}

func (c *CustomStatisticsCalculator[T]) CalculateNodeStatistics(dataset training.Dataset[T]) (*NodeStatistics[T], error) {
    // Custom statistics logic
}

// Custom stopping criteria
type EarlyStoppingCriteria[T comparable] struct {
    // Custom stopping logic
}

func (e *EarlyStoppingCriteria[T]) ShouldCreateLeaf(dataset training.Dataset[T], depth int, impurity float64) bool {
    // Custom stopping logic (e.g., early stopping for large datasets)
}
```

## Migration Guide

### From Legacy Implementation

```go
// Interface-based approach
stats, err := b.statisticsCalculator.CalculateNodeStatistics(dataset)
leaf, err := b.nodeBuilder.BuildLeafNode(stats)
shouldStop := b.stoppingCriteria.ShouldCreateLeaf(dataset, depth, impurity)
```

### Configuration Migration

```go
// Single source of truth
config := builder.DefaultUnifiedTreeConfigForType[string]()
builder, err := builder.NewTreeBuilderWithInference(splitter, func(c *BuilderConfig) error {
    *c = BuilderConfig{UnifiedTreeConfig: config}
    return nil
})
```

## Best Practices

### 1. Use Type Inference
```go
// Recommended: Automatic type inference
builder, err := builder.NewTreeBuilderWithInference(splitter, options...)

// Manual: Explicit type specification (only when needed)
builder, err := builder.NewTreeBuilder(splitter, options...)
```

### 2. Implement Proper Error Handling
```go
// Structured error handling
if builderErr, ok := err.(*builder.BuilderError); ok {
    log.WithFields(logrus.Fields{
        "operation": builderErr.Op,
        "field":     builderErr.Field,
        "value":     builderErr.Value,
    }).Error(builderErr.Reason)
}
```

### 3. Use Context Cancellation
```go
// Production timeout handling
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
defer cancel()

tree, err := builder.BuildTree(ctx, dataset, features)
if errors.Is(err, context.DeadlineExceeded) {
    log.Warn("Tree building timed out - consider reducing complexity")
}
```

### 4. Monitor and Log
```go
// Enable logging for production monitoring
builder, err := builder.NewTreeBuilderWithInference(splitter,
    builder.WithLogging(true),
    builder.WithMaxDepth(calculateOptimalDepth(datasetSize)),
    builder.WithMinSamplesSplit(calculateMinSamples(datasetSize)),
)
```

## Troubleshooting

### Common Configuration Errors

```go
// This will fail with clear error message
builder, err := builder.NewTreeBuilder(splitter,
    builder.WithMinSamplesLeaf(10),
    builder.WithMinSamplesSplit(5), // Error: leaf > split
)
// Error: "min_samples_leaf (10) cannot be greater than min_samples_split (5)"
```

### Type Compatibility Issues

```go
// This will fail with type compatibility error
splitter, _ := training.NewC45Splitter[string]()
builder, err := builder.NewTreeBuilder(splitter,
    builder.WithTargetType(models.NumericFeature), // Error: string ≠ numeric
)
// Error: "generic type string is not compatible with numeric target type"
```

### Performance Issues

```go
// Optimize for large datasets
builder, err := builder.NewTreeBuilderWithInference(splitter,
    builder.WithMaxDepth(calculateOptimalDepth(dataset.GetSize())),     // Prevent overfitting
    builder.WithMinSamplesSplit(max(20, dataset.GetSize()/1000)),       // Scale with data
    builder.WithMinSamplesLeaf(max(5, dataset.GetSize()/2000)),         // Ensure support
    builder.WithMinImpurityDecrease(0.01),                              // Require meaningful splits
)
```


## Contributing

### Code Quality Standards
1. **Maintain interface compatibility** - don't break existing interfaces
2. **Add comprehensive tests** - cover edge cases and error scenarios  
3. **Follow error handling patterns** - use BuilderError with context
4. **Validate all inputs** - including cross-parameter validation
5. **Update documentation** - keep examples current

### Performance Guidelines
1. **Profile before optimizing** - measure actual bottlenecks
2. **Consider memory usage** - avoid unnecessary data copying
3. **Test with large datasets** - ensure scalability
4. **Support context cancellation** - enable production timeouts

## License

This package is part of the Mulberri machine learning framework.
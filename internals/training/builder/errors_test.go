package builder

import (
	"errors"
	"testing"
)

// Test BuilderError Error method
func TestBuilderError_Error(t *testing.T) {
	tests := []struct {
		name     string
		err      *BuilderError
		expected string
	}{
		{
			name: "error with field and value",
			err: &BuilderError{
				Op:     "test_operation",
				Field:  "test_field",
				Value:  "test_value",
				Reason: "test reason",
			},
			expected: "builder test_operation failed for field 'test_field' (value: test_value): test reason",
		},
		{
			name: "error with field only",
			err: &BuilderError{
				Op:     "test_operation",
				Field:  "test_field",
				Reason: "test reason",
			},
			expected: "builder test_operation failed for field 'test_field': test reason",
		},
		{
			name: "error without field",
			err: &BuilderError{
				Op:     "test_operation",
				Reason: "test reason",
			},
			expected: "builder test_operation failed: test reason",
		},
		{
			name: "error with empty field and value",
			err: &BuilderError{
				Op:     "test_operation",
				Field:  "",
				Value:  "",
				Reason: "test reason",
			},
			expected: "builder test_operation failed: test reason",
		},
		{
			name: "error with field but empty value",
			err: &BuilderError{
				Op:     "test_operation",
				Field:  "test_field",
				Value:  "",
				Reason: "test reason",
			},
			expected: "builder test_operation failed for field 'test_field': test reason",
		},
		{
			name: "error with value but empty field",
			err: &BuilderError{
				Op:     "test_operation",
				Field:  "",
				Value:  "test_value",
				Reason: "test reason",
			},
			expected: "builder test_operation failed: test reason",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := test.err.Error()
			if result != test.expected {
				t.Errorf("Expected '%s', got '%s'", test.expected, result)
			}
		})
	}
}

// Test BuilderError Unwrap method
func TestBuilderError_Unwrap(t *testing.T) {
	tests := []struct {
		name     string
		err      *BuilderError
		expected error
	}{
		{
			name: "error with underlying error",
			err: &BuilderError{
				Op:     "test_operation",
				Reason: "test reason",
				Err:    errors.New("underlying error"),
			},
			expected: errors.New("underlying error"),
		},
		{
			name: "error without underlying error",
			err: &BuilderError{
				Op:     "test_operation",
				Reason: "test reason",
				Err:    nil,
			},
			expected: nil,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := test.err.Unwrap()

			if test.expected == nil {
				if result != nil {
					t.Errorf("Expected nil, got %v", result)
				}
			} else {
				if result == nil {
					t.Errorf("Expected %v, got nil", test.expected)
				} else if result.Error() != test.expected.Error() {
					t.Errorf("Expected '%s', got '%s'", test.expected.Error(), result.Error())
				}
			}
		})
	}
}

// Test BuilderError as error interface
func TestBuilderError_AsError(t *testing.T) {
	err := &BuilderError{
		Op:     "test_operation",
		Field:  "test_field",
		Value:  "test_value",
		Reason: "test reason",
	}

	// Test that it implements error interface
	var _ error = err

	// Test error message
	expected := "builder test_operation failed for field 'test_field' (value: test_value): test reason"
	if err.Error() != expected {
		t.Errorf("Expected '%s', got '%s'", expected, err.Error())
	}
}

// Test BuilderError with nested errors
func TestBuilderError_NestedErrors(t *testing.T) {
	innerErr := errors.New("inner error")
	middleErr := &BuilderError{
		Op:     "middle_operation",
		Reason: "middle reason",
		Err:    innerErr,
	}
	outerErr := &BuilderError{
		Op:     "outer_operation",
		Reason: "outer reason",
		Err:    middleErr,
	}

	// Test unwrapping
	unwrapped := outerErr.Unwrap()
	if unwrapped != middleErr {
		t.Errorf("Expected middle error, got %v", unwrapped)
	}

	// Test error message
	expectedOuter := "builder outer_operation failed: outer reason"
	if outerErr.Error() != expectedOuter {
		t.Errorf("Expected '%s', got '%s'", expectedOuter, outerErr.Error())
	}

	expectedMiddle := "builder middle_operation failed: middle reason"
	if middleErr.Error() != expectedMiddle {
		t.Errorf("Expected '%s', got '%s'", expectedMiddle, middleErr.Error())
	}
}

// Test BuilderError with special characters
func TestBuilderError_SpecialCharacters(t *testing.T) {
	tests := []struct {
		name     string
		err      *BuilderError
		expected string
	}{
		{
			name: "field with spaces",
			err: &BuilderError{
				Op:     "test_operation",
				Field:  "field with spaces",
				Reason: "test reason",
			},
			expected: "builder test_operation failed for field 'field with spaces': test reason",
		},
		{
			name: "value with quotes",
			err: &BuilderError{
				Op:     "test_operation",
				Field:  "test_field",
				Value:  "value with \"quotes\"",
				Reason: "test reason",
			},
			expected: "builder test_operation failed for field 'test_field' (value: value with \"quotes\"): test reason",
		},
		{
			name: "reason with newlines",
			err: &BuilderError{
				Op:     "test_operation",
				Reason: "test reason\nwith newlines",
			},
			expected: "builder test_operation failed: test reason\nwith newlines",
		},
		{
			name: "unicode characters",
			err: &BuilderError{
				Op:     "test_operation",
				Field:  "测试字段",
				Value:  "测试值",
				Reason: "测试原因",
			},
			expected: "builder test_operation failed for field '测试字段' (value: 测试值): 测试原因",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := test.err.Error()
			if result != test.expected {
				t.Errorf("Expected '%s', got '%s'", test.expected, result)
			}
		})
	}
}

// Test BuilderError zero values
func TestBuilderError_ZeroValues(t *testing.T) {
	err := &BuilderError{}
	expected := "builder  failed: "
	result := err.Error()

	if result != expected {
		t.Errorf("Expected '%s', got '%s'", expected, result)
	}

	if err.Unwrap() != nil {
		t.Errorf("Expected nil unwrap, got %v", err.Unwrap())
	}
}

// Test BuilderError type assertion
func TestBuilderError_TypeAssertion(t *testing.T) {
	var err error = &BuilderError{
		Op:     "test_operation",
		Reason: "test reason",
	}

	// Test type assertion
	if builderErr, ok := err.(*BuilderError); ok {
		if builderErr.Op != "test_operation" {
			t.Errorf("Expected op 'test_operation', got '%s'", builderErr.Op)
		}
		if builderErr.Reason != "test reason" {
			t.Errorf("Expected reason 'test reason', got '%s'", builderErr.Reason)
		}
	} else {
		t.Error("Type assertion to *BuilderError failed")
	}
}

package builder

import (
	"context"

	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/pkg/models"
)

// NodeBuilder defines the interface for building different types of tree nodes
// This separates node construction concerns from tree building logic
type NodeBuilder[T comparable] interface {
	// BuildLeafNode creates a leaf node from node statistics
	BuildLeafNode(stats *NodeStatistics[T]) (*models.TreeNode, error)

	// BuildDecisionNode creates a decision node from split result and children
	BuildDecisionNode(split *training.SplitResult[T], leftChild, rightChild *models.TreeNode) (*models.TreeNode, error)
}

// TreeConstructor defines the interface for tree construction operations
// This separates high-level tree building from node-level operations
type TreeConstructor[T comparable] interface {
	// BuildTree constructs a complete decision tree from dataset and features
	BuildTree(ctx context.Context, dataset training.Dataset[T], features []*models.Feature) (*models.DecisionTree, error)

	// ValidateInputs validates the inputs before tree construction
	ValidateInputs(dataset training.Dataset[T], features []*models.Feature) error
}

// StatisticsCalculator defines the interface for calculating node statistics
// This separates statistical calculations from tree building logic
type StatisticsCalculator[T comparable] interface {
	// CalculateNodeStatistics computes statistics for a dataset at a node
	CalculateNodeStatistics(dataset training.Dataset[T]) (*NodeStatistics[T], error)
}

// ConfigValidator defines the interface for configuration validation
// This separates configuration validation concerns
type ConfigValidator interface {
	// ValidateConfiguration validates the tree building configuration
	ValidateConfiguration() error

	// ValidateCompatibility validates compatibility between configuration and type
	ValidateCompatibility() error

	// ValidateForDataset validates configuration against dataset characteristics
	ValidateForDataset(datasetSize int, featureCount int) error
}

// NodeStatistics represents statistical information about a node
// This replaces the internal nodeStatistics struct with a public interface-friendly version
type NodeStatistics[T comparable] struct {
	SampleCount       int
	ClassDistribution map[T]int
	MajorityClass     T
	Confidence        float64
	Impurity          float64
}

// SplittingStrategy defines the interface for different splitting algorithms
// This allows for pluggable splitting strategies in the future
type SplittingStrategy[T comparable] interface {
	// FindBestSplit finds the best split for the given dataset and features
	FindBestSplit(ctx context.Context, dataset training.Dataset[T], features []*models.Feature) (*training.SplitResult[T], error)
}

// TreeBuilderComponents groups all the components needed for tree building
// This provides a clean way to inject dependencies
type TreeBuilderComponents[T comparable] struct {
	NodeBuilder          NodeBuilder[T]
	StatisticsCalculator StatisticsCalculator[T]
	ConfigValidator      ConfigValidator
	SplittingStrategy    SplittingStrategy[T]
}

// StoppingCriteria defines the interface for determining when to stop splitting
// This separates stopping logic from tree building logic
type StoppingCriteria[T comparable] interface {
	// ShouldCreateLeaf determines if a leaf node should be created
	ShouldCreateLeaf(dataset training.Dataset[T], depth int, impurity float64) bool
}

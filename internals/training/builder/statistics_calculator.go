package builder

import (
	"fmt"
	"math"

	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/pkg/models"
)

// DefaultStatisticsCalculator provides the default implementation of StatisticsCalculator interface
type DefaultStatisticsCalculator[T comparable] struct {
	config TreeConfiguration
}

// NewDefaultStatisticsCalculator creates a new default statistics calculator
func NewDefaultStatisticsCalculator[T comparable](config TreeConfiguration) *DefaultStatisticsCalculator[T] {
	return &DefaultStatisticsCalculator[T]{
		config: config,
	}
}

// CalculateNodeStatistics computes statistics for a dataset at a node
func (sc *DefaultStatisticsCalculator[T]) CalculateNodeStatistics(dataset training.Dataset[T]) (*NodeStatistics[T], error) {
	if dataset == nil {
		return nil, &BuilderError{
			Op:     "calculate_node_statistics",
			Field:  "dataset",
			Reason: "dataset cannot be nil",
		}
	}

	size := dataset.GetSize()
	if size == 0 {
		return nil, &BuilderError{
			Op:     "calculate_node_statistics",
			Field:  "dataset",
			Reason: "dataset cannot be empty",
		}
	}

	// Count class occurrences
	classDistribution := make(map[T]int)
	indices := dataset.GetIndices()

	for _, sampleIndex := range indices {
		target, err := dataset.GetTarget(sampleIndex)
		if err != nil {
			return nil, &BuilderError{
				Op:     "calculate_node_statistics",
				Field:  "target",
				Reason: fmt.Sprintf("failed to get target for sample %d: %v", sampleIndex, err),
				Err:    err,
			}
		}
		classDistribution[target]++
	}

	// Find majority class and calculate confidence
	var majorityClass T
	maxCount := 0
	for class, count := range classDistribution {
		if count > maxCount {
			maxCount = count
			majorityClass = class
		}
	}

	confidence := float64(maxCount) / float64(size)

	// Calculate impurity based on configured criterion
	impurity, err := sc.calculateImpurity(classDistribution, size)
	if err != nil {
		return nil, &BuilderError{
			Op:     "calculate_node_statistics",
			Field:  "impurity",
			Reason: fmt.Sprintf("failed to calculate impurity: %v", err),
			Err:    err,
		}
	}

	return &NodeStatistics[T]{
		SampleCount:       size,
		ClassDistribution: classDistribution,
		MajorityClass:     majorityClass,
		Confidence:        confidence,
		Impurity:          impurity,
	}, nil
}

// calculateImpurity calculates the impurity measure based on the configured criterion
func (sc *DefaultStatisticsCalculator[T]) calculateImpurity(classDistribution map[T]int, totalSamples int) (float64, error) {
	if totalSamples == 0 {
		return 0.0, nil
	}

	switch sc.config.GetCriterion() {
	case models.GiniCriterion:
		return sc.calculateGiniImpurity(classDistribution, totalSamples), nil
	case models.EntropyCriterion:
		return sc.calculateEntropyImpurity(classDistribution, totalSamples), nil
	default:
		return 0.0, &BuilderError{
			Op:     "calculate_impurity",
			Field:  "criterion",
			Value:  string(sc.config.GetCriterion()),
			Reason: "unsupported impurity criterion",
		}
	}
}

// calculateGiniImpurity calculates the Gini impurity
func (sc *DefaultStatisticsCalculator[T]) calculateGiniImpurity(classDistribution map[T]int, totalSamples int) float64 {
	if totalSamples == 0 {
		return 0.0
	}

	impurity := 1.0
	for _, count := range classDistribution {
		if count > 0 {
			prob := float64(count) / float64(totalSamples)
			impurity -= prob * prob
		}
	}

	return impurity
}

// calculateEntropyImpurity calculates the entropy impurity
func (sc *DefaultStatisticsCalculator[T]) calculateEntropyImpurity(classDistribution map[T]int, totalSamples int) float64 {
	if totalSamples == 0 {
		return 0.0
	}

	entropy := 0.0
	for _, count := range classDistribution {
		if count > 0 {
			prob := float64(count) / float64(totalSamples)
			entropy -= prob * math.Log2(prob)
		}
	}
	// Validate result for numerical stability
	if math.IsNaN(entropy) || math.IsInf(entropy, 0) {
		return 0.0 // Return safe default for invalid calculations
	}
	return entropy
}

// GetImpurityName returns the name of the impurity measure being used
func (sc *DefaultStatisticsCalculator[T]) GetImpurityName() string {
	return string(sc.config.GetCriterion())
}

// ValidateStatistics validates that the calculated statistics are reasonable
func (sc *DefaultStatisticsCalculator[T]) ValidateStatistics(stats *NodeStatistics[T]) error {
	if stats == nil {
		return &BuilderError{
			Op:     "validate_statistics",
			Field:  "statistics",
			Reason: "statistics cannot be nil",
		}
	}

	if stats.SampleCount < 0 {
		return &BuilderError{
			Op:     "validate_statistics",
			Field:  "sample_count",
			Value:  fmt.Sprintf("%d", stats.SampleCount),
			Reason: "sample count cannot be negative",
		}
	}

	if stats.Confidence < 0.0 || stats.Confidence > 1.0 {
		return &BuilderError{
			Op:     "validate_statistics",
			Field:  "confidence",
			Value:  fmt.Sprintf("%.6f", stats.Confidence),
			Reason: "confidence must be between 0.0 and 1.0",
		}
	}

	if stats.Impurity < 0.0 {
		return &BuilderError{
			Op:     "validate_statistics",
			Field:  "impurity",
			Value:  fmt.Sprintf("%.6f", stats.Impurity),
			Reason: "impurity cannot be negative",
		}
	}

	// Validate class distribution consistency
	totalCount := 0
	for _, count := range stats.ClassDistribution {
		if count < 0 {
			return &BuilderError{
				Op:     "validate_statistics",
				Field:  "class_distribution",
				Reason: "class counts cannot be negative",
			}
		}
		totalCount += count
	}

	if totalCount != stats.SampleCount {
		return &BuilderError{
			Op:     "validate_statistics",
			Field:  "class_distribution",
			Reason: fmt.Sprintf("distribution total (%d) does not match sample count (%d)", totalCount, stats.SampleCount),
		}
	}

	return nil
}

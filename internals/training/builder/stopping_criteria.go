package builder

import (
	"fmt"

	"github.com/berrijam/mulberri/internals/training"
)

// DefaultStoppingCriteria provides the default implementation of StoppingCriteria interface
type DefaultStoppingCriteria[T comparable] struct {
	config TreeConfiguration
}

// NewDefaultStoppingCriteria creates a new default stopping criteria
func NewDefaultStoppingCriteria[T comparable](config TreeConfiguration) *DefaultStoppingCriteria[T] {
	return &DefaultStoppingCriteria[T]{
		config: config,
	}
}

// ShouldCreateLeaf determines if a leaf node should be created based on stopping criteria
func (sc *DefaultStoppingCriteria[T]) ShouldCreateLeaf(dataset training.Dataset[T], depth int, impurity float64) bool {
	// Check maximum depth
	if depth >= sc.config.GetMaxDepth() {
		return true
	}

	// Check minimum samples for split
	if dataset.GetSize() < sc.config.GetMinSamplesSplit() {
		return true
	}

	// Check minimum samples for leaves (need at least 2 * minSamplesLeaf to split)
	if dataset.GetSize() < 2*sc.config.GetMinSamplesLeaf() {
		return true
	}

	// Check impurity threshold
	if impurity <= sc.config.GetMinImpurityDecrease() {
		return true
	}

	return false
}

// GetStoppingReason returns a human-readable reason why splitting should stop
func (sc *DefaultStoppingCriteria[T]) GetStoppingReason(dataset training.Dataset[T], depth int, impurity float64) string {
	if depth >= sc.config.GetMaxDepth() {
		return "maximum depth reached"
	}

	if dataset.GetSize() < sc.config.GetMinSamplesSplit() {
		return "insufficient samples for split"
	}

	if dataset.GetSize() < 2*sc.config.GetMinSamplesLeaf() {
		return "insufficient samples for leaves"
	}

	if impurity <= sc.config.GetMinImpurityDecrease() {
		return "impurity below threshold"
	}

	return "no stopping criteria met"
}

// ValidateStoppingCriteria validates that the stopping criteria configuration is reasonable
func (sc *DefaultStoppingCriteria[T]) ValidateStoppingCriteria() error {
	if sc.config.GetMaxDepth() <= 0 {
		return &BuilderError{
			Op:     "validate_stopping_criteria",
			Field:  "max_depth",
			Value:  fmt.Sprintf("%d", sc.config.GetMaxDepth()),
			Reason: "max depth must be positive",
		}
	}

	if sc.config.GetMinSamplesSplit() < 2 {
		return &BuilderError{
			Op:     "validate_stopping_criteria",
			Field:  "min_samples_split",
			Value:  fmt.Sprintf("%d", sc.config.GetMinSamplesSplit()), 
			Reason: "min samples split must be at least 2",
		}
	}

	if sc.config.GetMinSamplesLeaf() < 1 {
		return &BuilderError{
			Op:     "validate_stopping_criteria",
			Field:  "min_samples_leaf",
			Value:  fmt.Sprintf("%d", sc.config.GetMinSamplesLeaf()),
			Reason: "min samples leaf must be at least 1",
		}
	}

	if sc.config.GetMinSamplesLeaf() > sc.config.GetMinSamplesSplit() {
		return &BuilderError{
			Op:     "validate_stopping_criteria",
			Field:  "min_samples_leaf",
			Reason: "min samples leaf cannot exceed min samples split",
		}
	}

	if sc.config.GetMinImpurityDecrease() < 0.0 {
		return &BuilderError{
			Op:     "validate_stopping_criteria",
			Field:  "min_impurity_decrease",
			Reason: "min impurity decrease cannot be negative",
		}
	}

	return nil
}

// EstimateLeafCount estimates the number of leaf nodes that will be created
func (sc *DefaultStoppingCriteria[T]) EstimateLeafCount(totalSamples int) int {
	if totalSamples <= sc.config.GetMinSamplesLeaf() {
		return 1
	}

	// Rough estimation based on binary splits
	maxPossibleLeaves := totalSamples / sc.config.GetMinSamplesLeaf()

	// Limit by maximum depth (2^depth leaves at most)
	maxDepthLeaves := 1 << sc.config.GetMaxDepth()

	if maxPossibleLeaves < maxDepthLeaves {
		return maxPossibleLeaves
	}
	return maxDepthLeaves
}

// ShouldPrune determines if a subtree should be pruned (for future pruning support)
func (sc *DefaultStoppingCriteria[T]) ShouldPrune(nodeImpurity, leftImpurity, rightImpurity float64, leftSamples, rightSamples int) bool {
	// Simple pruning criterion: if the weighted impurity doesn't improve significantly
	totalSamples := leftSamples + rightSamples
	if totalSamples == 0 {
		return true
	}

	weightedImpurity := (float64(leftSamples)*leftImpurity + float64(rightSamples)*rightImpurity) / float64(totalSamples)
	impurityGain := nodeImpurity - weightedImpurity

	return impurityGain < sc.config.GetMinImpurityDecrease()
}

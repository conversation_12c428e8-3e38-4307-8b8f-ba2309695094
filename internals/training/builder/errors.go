package builder

import "fmt"

// BuilderError represents errors during tree building operations
type BuilderError struct {
	Op     string // Operation that failed
	Field  string // Field that caused the error (optional)
	Value  string // Value that caused the error (optional)
	Reason string // Human-readable reason
	Err    error  // Underlying error (optional)
}

func (e *BuilderError) Error() string {
	if e.Field != "" && e.Value != "" {
		return fmt.Sprintf("builder %s failed for field '%s' (value: %s): %s", e.Op, e.Field, e.Value, e.Reason)
	}
	if e.Field != "" {
		return fmt.Sprintf("builder %s failed for field '%s': %s", e.Op, e.Field, e.Reason)
	}
	return fmt.Sprintf("builder %s failed: %s", e.Op, e.Reason)
}

func (e *BuilderError) Unwrap() error {
	return e.Err
}

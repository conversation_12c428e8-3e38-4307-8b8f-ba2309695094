package training

import (
    "fmt"
    "strings"
)

// SplitError represents errors during splitting operations
type SplitError struct {
    Op      string
    Feature string
    Err     error
}

func (e *SplitError) Error() string {
    if e.Feature != "" {
        return fmt.Sprintf("split %s failed for feature '%s': %v", e.Op, e.Feature, e.Err)
    }
    return fmt.Sprintf("split %s failed: %v", e.Op, e.Err)
}

func (e *SplitError) Unwrap() error {
    return e.Err
}

// ValidationError represents input validation failures
type ValidationError struct {
    Field  string
    Value  interface{}
    Reason string
}

func (e *ValidationError) Error() string {
    return fmt.Sprintf("validation failed for %s (value: %v): %s", e.Field, e.Value, e.Reason)
}

// MultiError aggregates multiple errors for batch operations
type MultiError struct {
    Errors []error
}

func (e *MultiError) Error() string {
    if len(e.Errors) == 0 {
        return "no errors"
    }
    if len(e.Errors) == 1 {
        return e.Errors[0].Error()
    }
    
    var msgs []string
    for _, err := range e.Errors {
        msgs = append(msgs, err.Error())
    }
    return fmt.Sprintf("multiple errors (%d): %s", len(e.Errors), strings.Join(msgs, "; "))
}

func (e *MultiError) Add(err error) {
    if err != nil {
        e.Errors = append(e.Errors, err)
    }
}

func (e *MultiError) HasErrors() bool {
    return len(e.Errors) > 0
}
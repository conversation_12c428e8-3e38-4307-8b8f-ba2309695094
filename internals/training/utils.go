package training

import (
	"errors"
	"math"
)

// floatEqual checks if two float64 values are equal within the base tolerance
func floatEqual(a, b float64) bool {
	return math.Abs(a-b) < baseFloatTolerance
}

// safeThreshold calculates the midpoint between two values with overflow protection
func safeThreshold(a, b float64) (float64, error) {
	if math.IsInf(a, 0) || math.IsInf(b, 0) {
		return 0, errors.New("infinite values in threshold calculation")
	}
	return (a + b) / 2.0, nil
}


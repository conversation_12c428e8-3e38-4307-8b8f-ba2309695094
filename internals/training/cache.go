package training

import (
	"fmt"
	"sync"
	"time"

	"github.com/berrijam/mulberri/pkg/models"
)

// FeatureValueCache stores cached feature values to avoid repeated extraction
// with proper memory management and bounds checking
type FeatureValueCache[T comparable] struct {
	values     map[string]map[int]interface{} // featureID -> sampleIndex -> value
	timestamps map[string]time.Time           // featureID -> last access time
	mu         sync.RWMutex                   // Protects concurrent access
	limits     ResourceLimits                 // Resource limits for cache management
}

// NewFeatureValueCache creates a new feature value cache with resource limits
func NewFeatureValueCache[T comparable](limits ResourceLimits) *FeatureValueCache[T] {
	return &FeatureValueCache[T]{
		values:     make(map[string]map[int]interface{}),
		timestamps: make(map[string]time.Time),
		limits:     limits,
	}
}

// Get retrieves a cached feature value with bounds checking
func (c *FeatureValueCache[T]) Get(featureID string, sampleIdx int) (interface{}, bool) {
		c.mu.RLock()
	defer c.mu.RUnlock()
	
	if featureMap, exists := c.values[featureID]; exists {
		if value, exists := featureMap[sampleIdx]; exists {
			return value, true
		}
	}
	return nil, false
}

// Set stores a feature value in cache with size limits
func (c *FeatureValueCache[T]) Set(featureID string, sampleIdx int, value interface{}) {
	if featureID == "" || sampleIdx < 0 {
		return
	}
	
	c.mu.Lock()
	defer c.mu.Unlock()
	
	// Check cache size limits
	totalSize := 0
	for _, featureMap := range c.values {
		totalSize += len(featureMap)
	}
	
	if totalSize >= c.limits.MaxCacheSize {
		c.evictOldEntries()
	}
	
	if c.values[featureID] == nil {
		c.values[featureID] = make(map[int]interface{})
	}
	c.values[featureID][sampleIdx] = value
	c.timestamps[featureID] = time.Now()
}

// evictOldEntries removes old cache entries (must be called with write lock held)
func (c *FeatureValueCache[T]) evictOldEntries() {
	cutoff := time.Now().Add(-c.limits.MaxCacheAge)
	for featureID, timestamp := range c.timestamps {
		if timestamp.Before(cutoff) {
			delete(c.values, featureID)
			delete(c.timestamps, featureID)
		}
	}
}

// PreloadFeature loads all feature values for a dataset into cache
func (c *FeatureValueCache[T]) precacheFeature(dataset Dataset[T], feature *models.Feature) error {
	if dataset == nil || feature == nil {
		return &ValidationError{
			Field:  "preload_params",
			Reason: "dataset and feature cannot be nil",
		}
	}
	
	c.mu.Lock()
	defer c.mu.Unlock()
	
	featureID := feature.Name
	if c.values[featureID] == nil {
		c.values[featureID] = make(map[int]interface{})
	}
	
	var errors MultiError
	for _, idx := range dataset.GetIndices() {
		if _, exists := c.values[featureID][idx]; !exists {
			value, err := dataset.GetFeatureValue(idx, feature)
			if err != nil {
				errors.Add(fmt.Errorf("failed to get value for sample %d: %w", idx, err))
				continue
			}
			c.values[featureID][idx] = value
		}
	}
	
	c.timestamps[featureID] = time.Now()
	
	if errors.HasErrors() {
		return &errors
	}
	return nil
}

// Clear removes all cached values (useful for testing and memory management)
func (c *FeatureValueCache[T]) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.values = make(map[string]map[int]interface{})
	c.timestamps = make(map[string]time.Time)
}

// Size returns the total number of cached values
func (c *FeatureValueCache[T]) Size() int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	totalSize := 0
	for _, featureMap := range c.values {
		totalSize += len(featureMap)
	}
	return totalSize
}

// PreSortedFeatureData stores pre-sorted numeric feature data
type PreSortedFeatureData[T comparable] struct {
	Feature   *models.Feature    // The feature this data belongs to
	Values    []numericValue[T]  // Pre-sorted numeric values
	Sorted    bool               // Whether the values are sorted
	Timestamp time.Time          // When this data was created/last accessed
}

// NumericFeatureCache stores pre-sorted numeric features with proper cleanup
type NumericFeatureCache[T comparable] struct {
	cache  map[string]*PreSortedFeatureData[T] // featureID -> pre-sorted data
	mu     sync.RWMutex                        // Protects concurrent access
	limits ResourceLimits                      // Resource limits for cache management
}

// NewNumericFeatureCache creates a new numeric feature cache
func NewNumericFeatureCache[T comparable](limits ResourceLimits) *NumericFeatureCache[T] {
	return &NumericFeatureCache[T]{
		cache:  make(map[string]*PreSortedFeatureData[T]),
		limits: limits,
	}
}

// Get retrieves cached pre-sorted data
func (c *NumericFeatureCache[T]) Get(featureID string) (*PreSortedFeatureData[T], bool) {
	if featureID == "" {
		return nil, false
	}
	
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	data, exists := c.cache[featureID]
	if exists && time.Since(data.Timestamp) > c.limits.MaxCacheAge {
		return nil, false // Expired
	}
	return data, exists
}

// Set stores pre-sorted data with size limits
func (c *NumericFeatureCache[T]) Set(featureID string, data *PreSortedFeatureData[T]) {
	if featureID == "" || data == nil {
		return
	}
	
	c.mu.Lock()
	defer c.mu.Unlock()
	
	// Check cache size and evict if necessary
	if len(c.cache) >= c.limits.MaxCacheSize/10 { // Reasonable limit for expensive pre-sorted data
		c.evictOldEntries()
	}
	
	data.Timestamp = time.Now()
	c.cache[featureID] = data
}

// evictOldEntries removes expired entries (must be called with write lock held)
func (c *NumericFeatureCache[T]) evictOldEntries() {
	cutoff := time.Now().Add(-c.limits.MaxCacheAge)
	for featureID, data := range c.cache {
		if data.Timestamp.Before(cutoff) {
			delete(c.cache, featureID)
		}
	}
}

// Clear removes all cached data (useful for testing and memory management)
func (c *NumericFeatureCache[T]) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.cache = make(map[string]*PreSortedFeatureData[T])
}

// Size returns the number of cached features
func (c *NumericFeatureCache[T]) Size() int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	return len(c.cache)
}
package training

import (
	"context"
	"errors"
	"fmt"
	"math"
	"testing"
	"time"

	datetimeconverter "github.com/berrijam/mulberri/internals/utils/datetime_converter"
	"github.com/berrijam/mulberri/pkg/models"
)

// Mock implementations for testing

// MockDataset implements Dataset[T] for testing
type MockDataset[T comparable] struct {
	features map[string][]interface{}
	targets  []T
	indices  []int
	size     int
}

// Additional tests targeting specific low-coverage functions
func TestLowCoverageFunctions(t *testing.T) {
	t.Run("DefaultSplitterConfig CLI integration", func(t *testing.T) {
		// Test the DefaultSplitterConfig function which has 70% coverage
		// This might be due to CLI config integration
		config := DefaultSplitterConfig()

		// Basic validation of default config
		if config.MinSamplesSplit < 2 {
			t.Error("DefaultSplitterConfig() MinSamplesSplit should be >= 2")
		}
		if config.MinSamplesLeaf < 1 {
			t.Error("DefaultSplitterConfig() MinSamplesLeaf should be >= 1")
		}
		if config.Criterion < EntropyImpurity || config.Criterion > MSEImpurity {
			t.Error("DefaultSplitterConfig() should have valid criterion")
		}
	})

	t.Run("WithMinSamplesLeaf error case", func(t *testing.T) {
		config := &SplitterConfig{}

		// Test invalid value (0)
		if err := WithMinSamplesLeaf(0)(config); err == nil {
			t.Error("WithMinSamplesLeaf(0) should return error")
		}

		// Test negative value
		if err := WithMinSamplesLeaf(-1)(config); err == nil {
			t.Error("WithMinSamplesLeaf(-1) should return error")
		}
	})

	t.Run("WithImpurityCriterion error case", func(t *testing.T) {
		config := &SplitterConfig{}

		// Test invalid criterion
		if err := WithImpurityCriterion(ImpurityCriterion(999))(config); err == nil {
			t.Error("WithImpurityCriterion(999) should return error")
		}

		// Test negative criterion
		if err := WithImpurityCriterion(ImpurityCriterion(-1))(config); err == nil {
			t.Error("WithImpurityCriterion(-1) should return error")
		}
	})

	t.Run("WithMaxWorkers edge cases", func(t *testing.T) {
		config := &SplitterConfig{}

		// Test negative value (should set to NumCPU)
		if err := WithMaxWorkers(-1)(config); err != nil {
			t.Errorf("WithMaxWorkers(-1) should not error: %v", err)
		}

		// Test very large value (should cap at 64)
		if err := WithMaxWorkers(1000)(config); err != nil {
			t.Errorf("WithMaxWorkers(1000) should not error: %v", err)
		}
		if config.MaxWorkers != 64 {
			t.Errorf("WithMaxWorkers(1000) should cap at 64, got %d", config.MaxWorkers)
		}
	})

	t.Run("collectNumericValues error scenarios", func(t *testing.T) {
		splitter, _ := NewC45Splitter[string]()

		// Create dataset with mixed value types that will cause conversion errors
		dataset := NewMockDataset[string](3)
		dataset.SetTargets([]string{"a", "b", "c"})

		// Add feature with some values that can't be converted to float64
		mixedValues := []interface{}{1.0, "not_a_number", 3.0}
		dataset.AddFeature("mixed", mixedValues)

		feature := createNumericFeature("mixed")
		_, err := splitter.collectNumericValues(dataset, feature)
		if err == nil {
			t.Error("collectNumericValues() should return error for mixed value types")
		}
	})

	t.Run("findBestThreshold edge cases", func(t *testing.T) {
		splitter, _ := NewC45Splitter[string](WithMinSamplesLeaf(2))

		// Create values where no valid threshold exists due to MinSamplesLeaf constraint
		values := []numericValue[string]{
			{Value: 1.0, Index: 0, Target: "a"},
			{Value: 2.0, Index: 1, Target: "b"},
		}

		feature := createNumericFeature("test")
		result, err := splitter.findBestThreshold(values, 1.0, feature)

		if err != nil {
			t.Errorf("findBestThreshold() error = %v", err)
		}
		// With MinSamplesLeaf=2, no split should be possible with only 2 samples
		if result != nil {
			t.Error("findBestThreshold() should return nil when no valid threshold exists")
		}
	})

	t.Run("evaluateNumericFloatSplit insufficient samples", func(t *testing.T) {
		splitter, _ := NewC45Splitter[string](WithMinSamplesSplit(10))

		// Small dataset that doesn't meet MinSamplesSplit requirement
		dataset := NewMockDataset[string](5)
		dataset.SetTargets([]string{"a", "b", "a", "b", "a"})
		dataset.AddFeature("small", []interface{}{1.0, 2.0, 3.0, 4.0, 5.0})

		feature := createNumericFeature("small")
		result, err := splitter.evaluateNumericFloatSplit(dataset, feature, 1.0)

		if err != nil {
			t.Errorf("evaluateNumericFloatSplit() error = %v", err)
		}
		if result != nil {
			t.Error("evaluateNumericFloatSplit() should return nil for insufficient samples")
		}
	})
}

func NewMockDataset[T comparable](size int) *MockDataset[T] {
	indices := make([]int, size)
	for i := range indices {
		indices[i] = i
	}
	return &MockDataset[T]{
		features: make(map[string][]interface{}),
		targets:  make([]T, size),
		indices:  indices,
		size:     size,
	}
}

func (m *MockDataset[T]) GetSize() int {
	return m.size
}

func (m *MockDataset[T]) GetFeatureValue(sampleIdx int, feature *models.Feature) (interface{}, error) {
	if sampleIdx < 0 || sampleIdx >= m.size {
		return nil, fmt.Errorf("sample index %d out of bounds [0, %d)", sampleIdx, m.size)
	}

	values, exists := m.features[feature.Name]
	if !exists {
		return nil, fmt.Errorf("feature '%s' not found", feature.Name)
	}

	if sampleIdx >= len(values) {
		return nil, fmt.Errorf("sample index %d exceeds feature data length %d", sampleIdx, len(values))
	}

	return values[sampleIdx], nil
}

func (m *MockDataset[T]) GetTarget(sampleIdx int) (T, error) {
	var zero T
	if sampleIdx < 0 || sampleIdx >= len(m.targets) {
		return zero, fmt.Errorf("target index %d out of bounds", sampleIdx)
	}
	return m.targets[sampleIdx], nil
}

func (m *MockDataset[T]) GetIndices() []int {
	return m.indices
}

func (m *MockDataset[T]) Subset(indices []int) Dataset[T] {
	return &MockDataset[T]{
		features: m.features,
		targets:  m.targets,
		indices:  indices,
		size:     len(indices),
	}
}

func (m *MockDataset[T]) AddFeature(name string, values []interface{}) {
	m.features[name] = values
}

func (m *MockDataset[T]) SetTargets(targets []T) {
	m.targets = targets
}

func (m *MockDataset[T]) SetIndices(indices []int) {
	m.indices = indices
	m.size = len(indices)
}

// Helper functions to create test data
func createSimpleClassificationDataset() (*MockDataset[string], []*models.Feature) {
	dataset := NewMockDataset[string](10)

	// Feature 1: Numeric (age)
	ages := []interface{}{25.0, 35.0, 45.0, 25.0, 55.0, 35.0, 65.0, 45.0, 25.0, 75.0}
	dataset.AddFeature("age", ages)

	// Feature 2: Categorical (income)
	incomes := []interface{}{"low", "medium", "high", "low", "high", "medium", "high", "medium", "low", "high"}
	dataset.AddFeature("income", incomes)

	// Targets (loan approval)
	targets := []string{"no", "yes", "yes", "no", "yes", "yes", "yes", "yes", "no", "yes"}
	dataset.SetTargets(targets)

	features := []*models.Feature{
		createNumericFeature("age"),
		createCategoricalFeature("income"),
	}

	return dataset, features
}

// Helper function to create a categorical feature
func createCategoricalFeature(name string) *models.Feature {
	return &models.Feature{
		Name: name,
		Type: models.CategoricalFeature,
	}
}

// Helper function to create a numeric feature
func createNumericFeature(name string) *models.Feature {
	return &models.Feature{
		Name: name,
		Type: models.NumericFeature,
	}
}

func BenchmarkImpurityCalculations(b *testing.B) {
	dataset, _ := createSimpleClassificationDataset()

	b.Run("Entropy", func(b *testing.B) {
		splitter, _ := NewC45Splitter[string](WithImpurityCriterion(EntropyImpurity))
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			splitter.CalculateImpurity(dataset)
		}
	})

	b.Run("Gini", func(b *testing.B) {
		splitter, _ := NewC45Splitter[string](WithImpurityCriterion(GiniImpurity))
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			splitter.CalculateImpurity(dataset)
		}
	})
}

func BenchmarkSplitEvaluation(b *testing.B) {
	dataset, features := createSimpleClassificationDataset()
	splitter, _ := NewC45Splitter[string]()
	baseImpurity, _ := splitter.CalculateImpurity(dataset)

	b.Run("NumericSplit", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			splitter.evaluateNumericFloatSplit(dataset, features[0], baseImpurity)
		}
	})

	b.Run("CategoricalSplit", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			splitter.evaluateCategoricalSplit(dataset, features[1], baseImpurity)
		}
	})
}

func BenchmarkFindBestSplit(b *testing.B) {
	dataset, features := createSimpleClassificationDataset()

	b.Run("Sequential", func(b *testing.B) {
		splitter, _ := NewC45Splitter[string](WithMaxWorkers(1))
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			splitter.FindBestSplit(context.Background(), dataset, features)
		}
	})

	b.Run("Parallel", func(b *testing.B) {
		splitter, _ := NewC45Splitter[string](WithMaxWorkers(4))
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			splitter.FindBestSplit(context.Background(), dataset, features)
		}
	})
}

func TestCategoricalSplit(t *testing.T) {
	splitter, _ := NewC45Splitter[string]()

	t.Run("normal categorical split", func(t *testing.T) {
		dataset, features := createSimpleClassificationDataset()
		baseImpurity, _ := splitter.CalculateImpurity(dataset)

		result, err := splitter.evaluateCategoricalSplit(dataset, features[1], baseImpurity) // income feature
		if err != nil {
			t.Errorf("evaluateCategoricalSplit() error = %v", err)
		}
		if result == nil {
			t.Error("evaluateCategoricalSplit() should return result for valid categorical feature")
		}
		if result != nil {
			if len(result.Partitions) == 0 {
				t.Error("evaluateCategoricalSplit() should create partitions")
			}
		}
	})

	t.Run("all missing values", func(t *testing.T) {
		dataset := NewMockDataset[string](5)
		dataset.SetTargets([]string{"a", "b", "a", "b", "a"})
		nulls := []interface{}{nil, nil, nil, nil, nil}
		dataset.AddFeature("nulls", nulls)

		feature := createCategoricalFeature("nulls")
		baseImpurity, _ := splitter.CalculateImpurity(dataset)

		result, err := splitter.evaluateCategoricalSplit(dataset, feature, baseImpurity)
		if err == nil {
			t.Error("evaluateCategoricalSplit() with all nil values should return error")
		}
		if result != nil {
			t.Error("evaluateCategoricalSplit() with all nil values should return nil result")
		}
	})

	t.Run("partitions too small", func(t *testing.T) {
		// Create dataset where each category has only 1 sample (below MinSamplesLeaf)
		dataset := NewMockDataset[string](3)
		dataset.SetTargets([]string{"yes", "no", "maybe"})
		values := []interface{}{"a", "b", "c"} // Each unique
		dataset.AddFeature("sparse", values)

		splitter, _ := NewC45Splitter[string](WithMinSamplesLeaf(2))
		feature := createCategoricalFeature("sparse")
		baseImpurity, _ := splitter.CalculateImpurity(dataset)

		result, err := splitter.evaluateCategoricalSplit(dataset, feature, baseImpurity)
		if err != nil {
			t.Errorf("evaluateCategoricalSplit() error = %v", err)
		}
		if result != nil {
			t.Error("evaluateCategoricalSplit() with partitions too small should return nil")
		}
	})
}

func TestNumericSplit(t *testing.T) {
	splitter, _ := NewC45Splitter[string]()

	t.Run("normal numeric split", func(t *testing.T) {
		dataset, features := createSimpleClassificationDataset()
		baseImpurity, _ := splitter.CalculateImpurity(dataset)

		result, err := splitter.evaluateNumericFloatSplit(dataset, features[0], baseImpurity) // age feature
		if err != nil {
			t.Errorf("evaluateNumericFloatSplit() error = %v", err)
		}
		if result == nil {
			t.Error("evaluateNumericFloatSplit() should return result for valid numeric feature")
		}
		if result != nil {
			if result.Threshold == 0 {
				t.Error("evaluateNumericFloatSplit() should set threshold")
			}
			if len(result.LeftIndices) == 0 && len(result.RightIndices) == 0 {
				t.Error("evaluateNumericFloatSplit() should create left and right partitions")
			}
		}
	})

	t.Run("insufficient samples", func(t *testing.T) {
		dataset := NewMockDataset[string](1)
		dataset.SetTargets([]string{"yes"})
		dataset.AddFeature("age", []interface{}{25.0})

		feature := createNumericFeature("age")
		baseImpurity, _ := splitter.CalculateImpurity(dataset)

		result, err := splitter.evaluateNumericFloatSplit(dataset, feature, baseImpurity)
		if err != nil {
			t.Errorf("evaluateNumericFloatSplit() error = %v", err)
		}
		if result != nil {
			t.Error("evaluateNumericFloatSplit() with insufficient samples should return nil")
		}
	})

}

func TestCollectNumericValues(t *testing.T) {
	splitter, _ := NewC45Splitter[string]()

	t.Run("successful collection", func(t *testing.T) {
		dataset, features := createSimpleClassificationDataset()

		values, err := splitter.collectNumericValues(dataset, features[0]) // age feature
		if err != nil {
			t.Errorf("collectNumericValues() error = %v", err)
		}
		if len(values) != dataset.GetSize() {
			t.Errorf("collectNumericValues() returned %d values, want %d", len(values), dataset.GetSize())
		}

		// Check that values have proper structure
		for i, value := range values {
			if value.Index < 0 {
				t.Errorf("collectNumericValues() value %d has negative index", i)
			}
			if math.IsNaN(value.Value) {
				t.Errorf("collectNumericValues() value %d is NaN", i)
			}
		}
	})

	t.Run("conversion errors", func(t *testing.T) {
		dataset := NewMockDataset[string](2)
		dataset.SetTargets([]string{"a", "b"})
		// Add non-numeric values to a numeric feature
		dataset.AddFeature("bad_numeric", []interface{}{"not_a_number", "also_not_a_number"})

		feature := createNumericFeature("bad_numeric")
		_, err := splitter.collectNumericValues(dataset, feature)
		if err == nil {
			t.Error("collectNumericValues() with non-numeric values should return error")
		}
	})
}

func TestFindBestThreshold(t *testing.T) {
	splitter, _ := NewC45Splitter[string](WithMinSamplesLeaf(1))

	values := []numericValue[string]{
		{Value: 1.0, Index: 0, Target: "a"},
		{Value: 2.0, Index: 1, Target: "a"},
		{Value: 3.0, Index: 2, Target: "b"},
		{Value: 4.0, Index: 3, Target: "b"},
	}

	feature := createNumericFeature("test")
	baseImpurity := 1.0 // Assume balanced

	result, err := splitter.findBestThreshold(values, baseImpurity, feature)
	if err != nil {
		t.Errorf("findBestThreshold() error = %v", err)
	}
	if result == nil {
		t.Error("findBestThreshold() should return result")
	}
	if result != nil {
		if result.Threshold <= 1.0 || result.Threshold >= 4.0 {
			t.Errorf("findBestThreshold() threshold %f should be between 1.0 and 4.0", result.Threshold)
		}
		if result.GainRatio <= 0 {
			t.Errorf("findBestThreshold() gain ratio %f should be positive", result.GainRatio)
		}
	}
}

// TestDateTimeFeatureConversion tests that datetime features are properly converted to numeric values
func TestDateTimeFeatureConversion(t *testing.T) {
	// Test datetime conversion directly using the datetime converter
	converter := datetimeconverter.NewDateTimeConverter()

	// Test datetime string conversion
	testCases := []struct {
		name        string
		input       string
		expected    int64
		expectError bool
	}{
		{
			name:        "datetime string",
			input:       "2023-12-25T14:30:45Z",
			expected:    20231225143045,
			expectError: false,
		},
		{
			name:        "date only string",
			input:       "2023-12-25",
			expected:    20231225,
			expectError: false,
		},
		{
			name:        "time only string",
			input:       "14:30:45Z",
			expected:    143045,
			expectError: false,
		},
		{
			name:        "invalid datetime string",
			input:       "invalid-date",
			expected:    0,
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tc.input)

			if tc.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if result != tc.expected {
					t.Errorf("Expected %d, got %d", tc.expected, result)
				}
			}
		})
	}
}

// TestDateTimeFeatureSplitting tests that datetime features can be used for splitting
func TestDateTimeFeatureSplitting(t *testing.T) {
	// Create test data with datetime strings
	datetimeFeature, err := models.NewFeature("timestamp", models.DateTimeFeature, 0)
	if err != nil {
		t.Fatalf("Failed to create datetime feature: %v", err)
	}

	// Create mock dataset with pre-converted datetime integer values
	// These correspond to the datetime strings converted to YYYYMMDDHHMMSS format
	dataset := &MockDataset[string]{
		features: map[string][]interface{}{
			"timestamp": {
				int64(20230101100000), // 2023-01-01T10:00:00Z
				int64(20230615143000), // 2023-06-15T14:30:00Z
				int64(20231231235959), // 2023-12-31T23:59:59Z
				int64(20230315081530), // 2023-03-15T08:15:30Z
			},
		},
		targets: []string{"A", "B", "B", "A"},
		indices: []int{0, 1, 2, 3},
		size:    4,
	}

	// Create splitter
	splitter, err := NewC45Splitter[string]()
	if err != nil {
		t.Fatalf("Failed to create splitter: %v", err)
	}

	// Test that the feature can be evaluated for splitting
	baseImpurity, err := splitter.CalculateImpurity(dataset)
	if err != nil {
		t.Fatalf("Failed to calculate base impurity: %v", err)
	}

	split, err := splitter.evaluateFeature(dataset, datetimeFeature, baseImpurity)
	if err != nil {
		t.Fatalf("Failed to evaluate datetime feature: %v", err)
	}

	// Should find a split since we have different datetime values with different targets
	if split == nil {
		t.Errorf("Expected to find a split for datetime feature, but got nil")
	} else {
		t.Logf("Found split with gain ratio: %f, threshold: %f", split.GainRatio, split.Threshold)

		// Verify the split has the expected structure
		if split.Feature != datetimeFeature {
			t.Errorf("Split feature should match input feature")
		}
		if split.GainRatio <= 0 {
			t.Errorf("Split gain ratio should be positive, got %f", split.GainRatio)
		}
		if len(split.LeftIndices) == 0 && len(split.RightIndices) == 0 {
			t.Errorf("Split should have at least some indices on left or right")
		}
	}
}

// TestDateTimeIntegerArithmetic tests that datetime features use integer arithmetic
func TestDateTimeIntegerArithmetic(t *testing.T) {
	// Test integer conversion for datetime features using the datetime converter directly
	converter := datetimeconverter.NewDateTimeConverter()

	// Test integer conversion for datetime strings
	testCases := []struct {
		name     string
		input    string
		expected int64
	}{
		{
			name:     "datetime string to integer",
			input:    "2023-12-25T14:30:45Z",
			expected: 20231225143045,
		},
		{
			name:     "date only to integer",
			input:    "2023-01-01",
			expected: 20230101,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tc.input)
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
			}
			if result != tc.expected {
				t.Errorf("Expected %d, got %d", tc.expected, result)
			}
		})
	}
}

// TestDateTimeCaching tests that datetime conversion caching works correctly

func TestGetIndices(t *testing.T) {
	values := []numericValue[string]{
		{Value: 1.0, Index: 10, Target: "a"},
		{Value: 2.0, Index: 20, Target: "b"},
		{Value: 3.0, Index: 30, Target: "a"},
		{Value: 4.0, Index: 40, Target: "b"},
	}

	t.Run("valid range", func(t *testing.T) {
		indices := getIndices(values, 1, 2)
		expected := []int{20, 30}

		if len(indices) != len(expected) {
			t.Errorf("getIndices() returned %d indices, want %d", len(indices), len(expected))
		}

		for i, idx := range indices {
			if idx != expected[i] {
				t.Errorf("getIndices() index %d = %d, want %d", i, idx, expected[i])
			}
		}
	})

	t.Run("invalid range", func(t *testing.T) {
		indices := getIndices(values, -1, 2)
		if indices == nil || len(indices) != 0 {
			t.Error("getIndices() with invalid range should return empty slice")
		}

		indices = getIndices(values, 1, 10)
		if indices == nil || len(indices) != 0 {
			t.Error("getIndices() with out-of-bounds range should return empty slice")
		}

		indices = getIndices(values, 3, 1)
		if indices == nil || len(indices) != 0 {
			t.Error("getIndices() with start > end should return empty slice")
		}
	})
}

func TestConvertToFloat64(t *testing.T) {
	splitter, _ := NewC45Splitter[string]()

	tests := []struct {
		name     string
		input    interface{}
		expected float64
		wantErr  bool
	}{
		{"float64", 3.14, 3.14, false},
		{"float32", float32(2.5), 2.5, false},
		{"int", 42, 42.0, false},
		{"int32", int32(100), 100.0, false},
		{"int64", int64(200), 200.0, false},
		{"uint", uint(50), 50.0, false},
		{"uint32", uint32(75), 75.0, false},
		{"uint64", uint64(125), 125.0, false},
		{"string valid", "3.14159", 3.14159, false},
		{"string invalid", "not_a_number", 0, true},
		{"unsupported type", []int{1, 2, 3}, 0, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := splitter.convertToFloat64(tt.input)

			if tt.wantErr {
				if err == nil {
					t.Errorf("convertToFloat64() expected error for input %v", tt.input)
				}
			} else {
				if err != nil {
					t.Errorf("convertToFloat64() unexpected error: %v", err)
				}
				if math.Abs(result-tt.expected) > 1e-10 {
					t.Errorf("convertToFloat64() = %f, want %f", result, tt.expected)
				}
			}
		})
	}
}

func TestCalculateClassificationImpurity(t *testing.T) {
	splitter, _ := NewC45Splitter[string](WithImpurityCriterion(EntropyImpurity))

	dataset := NewMockDataset[string](4)
	dataset.SetTargets([]string{"a", "a", "b", "b"}) // Balanced

	impurity, err := splitter.calculateClassificationImpurity(dataset)
	if err != nil {
		t.Errorf("calculateClassificationImpurity() error = %v", err)
	}

	// For balanced binary classification with entropy, should be 1.0
	if math.Abs(impurity-1.0) > 1e-10 {
		t.Errorf("calculateClassificationImpurity() = %f, want ~1.0", impurity)
	}
}

func TestCalculateMSE(t *testing.T) {
	splitter, _ := NewC45Splitter[float64]()

	dataset := NewMockDataset[float64](4)
	dataset.SetTargets([]float64{1.0, 2.0, 3.0, 4.0}) // Mean = 2.5, Variance = 1.25

	mse, err := splitter.calculateMSE(dataset)
	if err != nil {
		t.Errorf("calculateMSE() error = %v", err)
	}

	expected := 1.25 // Variance
	if math.Abs(mse-expected) > 1e-10 {
		t.Errorf("calculateMSE() = %f, want %f", mse, expected)
	}
}

func TestCalculateDistributionImpurity(t *testing.T) {
	splitter, _ := NewC45Splitter[string](WithImpurityCriterion(GiniImpurity))

	dist := map[string]int{"a": 2, "b": 2}
	impurity := splitter.calculateDistributionImpurity(dist, 4)

	// For balanced binary with Gini, should be 0.5
	if math.Abs(impurity-0.5) > 1e-10 {
		t.Errorf("calculateDistributionImpurity() = %f, want ~0.5", impurity)
	}

	// Test with zero size
	impurity = splitter.calculateDistributionImpurity(dist, 0)
	if impurity != 0 {
		t.Errorf("calculateDistributionImpurity() with size 0 = %f, want 0", impurity)
	}
}

func TestIsValidFeatureType(t *testing.T) {
	splitter, _ := NewC45Splitter[string]()

	validTypes := []models.FeatureType{
		models.CategoricalFeature,
		models.NumericFeature,
		models.DateTimeFeature,
	}

	for _, ftype := range validTypes {
		if !splitter.isValidFeatureType(ftype) {
			t.Errorf("isValidFeatureType() should return true for %v", ftype)
		}
	}

	// Test invalid type
	if splitter.isValidFeatureType("invalid_type") {
		t.Error("isValidFeatureType() should return false for invalid type")
	}
}

func TestCalculateOptimalWorkers(t *testing.T) {
	splitter, _ := NewC45Splitter[string](WithMaxWorkers(8))

	tests := []struct {
		name        string
		numFeatures int
		expected    int
	}{
		{"few features", 2, 1},
		{"enough features", 10, 8},
		{"many features", 100, 8},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			workers := splitter.calculateOptimalWorkers(tt.numFeatures)
			if workers != tt.expected {
				t.Errorf("calculateOptimalWorkers(%d) = %d, want %d", tt.numFeatures, workers, tt.expected)
			}
		})
	}
}

func TestPoolManagement(t *testing.T) {
	splitter, _ := NewC45Splitter[string]()

	t.Run("target distribution pool", func(t *testing.T) {
		// Get distribution from pool
		dist := splitter.getTargetDistFromPool()
		if dist == nil {
			t.Error("getTargetDistFromPool() should return non-nil map")
		}

		// Use it
		dist["test"] = 5

		// Return to pool
		splitter.returnTargetDistToPool(dist)

		// Get again - should be clean
		dist2 := splitter.getTargetDistFromPool()
		if len(dist2) != 0 {
			t.Error("Pool should return clean maps")
		}

		// Test with oversized map (shouldn't be returned to pool)
		bigDist := make(map[string]int)
		for i := 0; i < maxPooledMapSize+10; i++ {
			bigDist[fmt.Sprintf("key_%d", i)] = i
		}
		splitter.returnTargetDistToPool(bigDist)
		// This should not crash or cause issues
	})
}

func TestPrepareForSplitting(t *testing.T) {
	splitter, _ := NewC45Splitter[string]()

	t.Run("normal preparation", func(t *testing.T) {
		err := splitter.prepareForSplitting(context.Background())
		if err != nil {
			t.Errorf("prepareForSplitting() error = %v", err)
		}
	})

	t.Run("context cancellation", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		err := splitter.prepareForSplitting(ctx)
		if err != context.Canceled {
			t.Errorf("prepareForSplitting() with cancelled context should return context.Canceled, got %v", err)
		}
	})
}

func TestExecuteSplitting(t *testing.T) {
	splitter, _ := NewC45Splitter[string](WithMaxWorkers(1)) // Force sequential
	dataset, features := createSimpleClassificationDataset()
	baseImpurity, _ := splitter.CalculateImpurity(dataset)

	t.Run("sequential execution", func(t *testing.T) {
		result, err := splitter.executeSplitting(context.Background(), dataset, features, baseImpurity)
		if err != nil {
			t.Errorf("executeSplitting() sequential error = %v", err)
		}
		if result == nil {
			t.Error("executeSplitting() should return result")
		}
	})

	// Force parallel
	splitter.config.MaxWorkers = 4
	t.Run("parallel execution", func(t *testing.T) {
		// Create more features to trigger parallel processing
		moreFeatures := make([]*models.Feature, minFeaturesForParallel+1)
		for i := 0; i < len(moreFeatures); i++ {
			if i < len(features) {
				moreFeatures[i] = features[i]
			} else {
				moreFeatures[i] = createNumericFeature(fmt.Sprintf("extra_%d", i))
				// Add corresponding data
				values := make([]interface{}, dataset.GetSize())
				for j := range values {
					values[j] = float64(i * j)
				}
				dataset.AddFeature(moreFeatures[i].Name, values)
			}
		}

		result, err := splitter.executeSplitting(context.Background(), dataset, moreFeatures, baseImpurity)
		if err != nil {
			t.Errorf("executeSplitting() parallel error = %v", err)
		}
		if result == nil {
			t.Error("executeSplitting() parallel should return result")
		}
	})
}

func TestSafeEvaluateFeature(t *testing.T) {
	splitter, _ := NewC45Splitter[string]()
	dataset, features := createSimpleClassificationDataset()
	baseImpurity, _ := splitter.CalculateImpurity(dataset)

	result, err := splitter.safeEvaluateFeature(dataset, features[0], baseImpurity)
	if err != nil {
		t.Errorf("safeEvaluateFeature() error = %v", err)
	}
	if result == nil {
		t.Error("safeEvaluateFeature() should return result")
	}
}

func TestFindBestSplitSequential(t *testing.T) {
	splitter, _ := NewC45Splitter[string]()
	dataset, features := createSimpleClassificationDataset()
	baseImpurity, _ := splitter.CalculateImpurity(dataset)

	t.Run("normal sequential", func(t *testing.T) {
		result, err := splitter.findBestSplitSequential(context.Background(), dataset, features, baseImpurity)
		if err != nil {
			t.Errorf("findBestSplitSequential() error = %v", err)
		}
		if result == nil {
			t.Error("findBestSplitSequential() should return result")
		}
	})

	t.Run("context cancellation", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())
		cancel()

		_, err := splitter.findBestSplitSequential(ctx, dataset, features, baseImpurity)
		if err != context.Canceled {
			t.Errorf("findBestSplitSequential() with cancelled context should return context.Canceled, got %v", err)
		}
	})
}

func TestValidateInputsDetailed(t *testing.T) {
	splitter, _ := NewC45Splitter[string]()

	t.Run("duplicate feature names", func(t *testing.T) {
		dataset, _ := createSimpleClassificationDataset()
		features := []*models.Feature{
			createNumericFeature("duplicate"),
			createNumericFeature("duplicate"), // Same name
		}

		err := splitter.validateInputs(dataset, features)
		if err == nil {
			t.Error("validateInputs() should reject duplicate feature names")
		}
	})

	t.Run("empty feature name", func(t *testing.T) {
		dataset, _ := createSimpleClassificationDataset()
		features := []*models.Feature{
			{Name: "", Type: models.NumericFeature}, // Empty name
		}

		err := splitter.validateInputs(dataset, features)
		if err == nil {
			t.Error("validateInputs() should reject empty feature names")
		}
	})

	t.Run("invalid feature type", func(t *testing.T) {
		dataset, _ := createSimpleClassificationDataset()
		features := []*models.Feature{
			{Name: "invalid", Type: "invalid_type"},
		}

		err := splitter.validateInputs(dataset, features)
		if err == nil {
			t.Error("validateInputs() should reject invalid feature types")
		}
	})
}

// Test missing error functions
func TestSplitError(t *testing.T) {
	baseErr := errors.New("base error")

	tests := []struct {
		name     string
		err      *SplitError
		expected string
	}{
		{
			name: "with feature",
			err: &SplitError{
				Op:      "test_op",
				Feature: "test_feature",
				Err:     baseErr,
			},
			expected: "split test_op failed for feature 'test_feature': base error",
		},
		{
			name: "without feature",
			err: &SplitError{
				Op:  "test_op",
				Err: baseErr,
			},
			expected: "split test_op failed: base error",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			if got := test.err.Error(); got != test.expected {
				t.Errorf("SplitError.Error() = %v, want %v", got, test.expected)
			}
			if unwrapped := test.err.Unwrap(); unwrapped != baseErr {
				t.Errorf("SplitError.Unwrap() = %v, want %v", unwrapped, baseErr)
			}
		})
	}
}

func TestValidationError(t *testing.T) {
	err := &ValidationError{
		Field:  "test_field",
		Value:  42,
		Reason: "test reason",
	}

	expected := "validation failed for test_field (value: 42): test reason"
	if got := err.Error(); got != expected {
		t.Errorf("ValidationError.Error() = %v, want %v", got, expected)
	}
}

func TestMultiError(t *testing.T) {
	t.Run("empty", func(t *testing.T) {
		var me MultiError
		if me.Error() != "no errors" {
			t.Errorf("Empty MultiError.Error() = %v, want 'no errors'", me.Error())
		}
		if me.HasErrors() {
			t.Error("Empty MultiError.HasErrors() should return false")
		}
	})

	t.Run("single error", func(t *testing.T) {
		var me MultiError
		err := errors.New("single error")
		me.Add(err)

		if me.Error() != "single error" {
			t.Errorf("Single MultiError.Error() = %v, want 'single error'", me.Error())
		}
		if !me.HasErrors() {
			t.Error("Single MultiError.HasErrors() should return true")
		}
	})

	t.Run("multiple errors", func(t *testing.T) {
		var me MultiError
		me.Add(errors.New("error 1"))
		me.Add(errors.New("error 2"))

		expected := "multiple errors (2): error 1; error 2"
		if me.Error() != expected {
			t.Errorf("Multiple MultiError.Error() = %v, want %v", me.Error(), expected)
		}
	})

	t.Run("add nil", func(t *testing.T) {
		var me MultiError
		me.Add(nil)

		if me.HasErrors() {
			t.Error("MultiError with nil should not have errors")
		}
	})
}

// Test missing config functions

func TestConfigFunctions(t *testing.T) {
	t.Run("WithMinSamplesSplit", func(t *testing.T) {
		config := &SplitterConfig{}

		// Valid value
		if err := WithMinSamplesSplit(5)(config); err != nil {
			t.Errorf("WithMinSamplesSplit(5) error = %v", err)
		}
		if config.MinSamplesSplit != 5 {
			t.Errorf("WithMinSamplesSplit(5) set value to %v, want 5", config.MinSamplesSplit)
		}

		// Invalid value
		if err := WithMinSamplesSplit(1)(config); err == nil {
			t.Error("WithMinSamplesSplit(1) should return error")
		}
	})

	t.Run("WithResourceLimits", func(t *testing.T) {
		config := &SplitterConfig{}
		limits := ResourceLimits{
			MaxFeatures:    100,
			MaxDatasetSize: 1000,
		}

		// Valid limits
		if err := WithResourceLimits(limits)(config); err != nil {
			t.Errorf("WithResourceLimits() error = %v", err)
		}

		// Invalid limits
		invalidLimits := ResourceLimits{MaxFeatures: -1}
		if err := WithResourceLimits(invalidLimits)(config); err == nil {
			t.Error("WithResourceLimits() with invalid limits should return error")
		}
	})
}

// Test FindBestSplit directly
func TestFindBestSplit(t *testing.T) {
	t.Run("simple classification", func(t *testing.T) {
		splitter, err := NewC45Splitter[string](
			WithMinSamplesSplit(2),
			WithMinSamplesLeaf(1),
		)
		if err != nil {
			t.Fatalf("NewC45Splitter() error = %v", err)
		}

		dataset, features := createSimpleClassificationDataset()

		result, err := splitter.FindBestSplit(context.Background(), dataset, features)
		if err != nil {
			t.Errorf("FindBestSplit() error = %v", err)
		}
		if result == nil {
			t.Error("FindBestSplit() should return a split result")
		}
		if result != nil {
			if result.GainRatio <= 0 {
				t.Errorf("FindBestSplit() gain ratio = %v, want > 0", result.GainRatio)
			}
			if result.Feature == nil {
				t.Error("FindBestSplit() should return feature")
			}
		}
	})

	t.Run("invalid inputs", func(t *testing.T) {
		splitter, _ := NewC45Splitter[string]()

		// Nil dataset
		_, err := splitter.FindBestSplit(context.Background(), nil, []*models.Feature{})
		if err == nil {
			t.Error("FindBestSplit() with nil dataset should return error")
		}

		// Empty features
		dataset, _ := createSimpleClassificationDataset()
		_, err = splitter.FindBestSplit(context.Background(), dataset, []*models.Feature{})
		if err == nil {
			t.Error("FindBestSplit() with empty features should return error")
		}
	})

	t.Run("context cancellation", func(t *testing.T) {
		splitter, _ := NewC45Splitter[string]()
		dataset, features := createSimpleClassificationDataset()

		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		_, err := splitter.FindBestSplit(ctx, dataset, features)
		if err == nil {
			t.Error("FindBestSplit() with cancelled context should return error")
		}
		if err != context.Canceled {
			t.Errorf("FindBestSplit() with cancelled context should return context.Canceled, got %v", err)
		}
	})
}

// Test panic recovery in safeEvaluateFeature
func TestSafeEvaluateFeaturePanic(t *testing.T) {
	splitter, _ := NewC45Splitter[string]()

	// Create a dataset that will cause issues
	dataset := &MockDataset[string]{
		features: make(map[string][]interface{}),
		targets:  []string{"a"},
		indices:  []int{0},
		size:     1,
	}

	// Feature that doesn't exist will cause panic in some paths
	feature := createNumericFeature("nonexistent")

	// This should recover from panic and return an error
	result, err := splitter.safeEvaluateFeature(dataset, feature, 1.0)
	if err == nil {
		t.Error("safeEvaluateFeature() should return error for problematic feature")
	}
	if result != nil {
		t.Error("safeEvaluateFeature() should return nil result on error")
	}
}

// Test drainRemainingResults (currently 0% coverage)
func TestDrainRemainingResults(t *testing.T) {
	splitter, _ := NewC45Splitter[string]()

	// Create a channel with some results
	results := make(chan SplitWorkerResult[string], 3)
	results <- SplitWorkerResult[string]{Split: &SplitResult[string]{GainRatio: 0.5}, Err: nil}
	results <- SplitWorkerResult[string]{Split: nil, Err: errors.New("test error")}
	results <- SplitWorkerResult[string]{Split: &SplitResult[string]{GainRatio: 0.8}, Err: nil}
	close(results)

	var errors MultiError
	var bestSplit *SplitResult[string]
	timeout := time.After(100 * time.Millisecond)

	splitter.drainRemainingResults(results, &errors, &bestSplit, timeout)

	// Should have collected the best split
	if bestSplit == nil || bestSplit.GainRatio != 0.8 {
		t.Error("drainRemainingResults() should collect best split")
	}

	// Should have collected the error
	if !errors.HasErrors() {
		t.Error("drainRemainingResults() should collect errors")
	}
}

// Test edge cases in existing functions to improve coverage
func TestEdgeCases(t *testing.T) {
	t.Run("calculateSplitInfo edge cases", func(t *testing.T) {
		// Empty sizes
		splitInfo := calculateSplitInfo([]int{}, 0)
		if splitInfo != 0 {
			t.Errorf("calculateSplitInfo() with empty sizes = %v, want 0", splitInfo)
		}

		// Single partition with zero size
		splitInfo = calculateSplitInfo([]int{0, 5}, 5)
		if splitInfo != 0 {
			t.Errorf("calculateSplitInfo() with zero partition = %v, want 0", splitInfo)
		}
	})

	t.Run("calculateEntropy edge cases", func(t *testing.T) {
		// Distribution with zero counts
		dist := map[string]int{"a": 0, "b": 5}
		entropy := calculateEntropy(dist, 5)
		if entropy != 0 {
			t.Errorf("calculateEntropy() with zero count = %v, want 0", entropy)
		}
	})

	t.Run("calculateGini edge cases", func(t *testing.T) {
		// Single class
		dist := map[string]int{"a": 5}
		gini := calculateGini(dist, 5)
		if gini != 0 {
			t.Errorf("calculateGini() with single class = %v, want 0", gini)
		}
	})
}

// Test parallel processing timeout scenarios
func TestParallelProcessingTimeout(t *testing.T) {
	// Create a splitter with very short timeout
	limits := ResourceLimits{
		MaxFeatures:    1000,
		MaxDatasetSize: 10000,
		Timeout:        1 * time.Nanosecond, // Very short
	}

	splitter, _ := NewC45Splitter[string](
		WithResourceLimits(limits),
		WithMaxWorkers(4),
	)

	dataset, features := createSimpleClassificationDataset()

	// Add more features to force parallel processing
	for i := 0; i < 10; i++ {
		feature := createNumericFeature(fmt.Sprintf("extra_%d", i))
		values := make([]interface{}, dataset.GetSize())
		for j := range values {
			values[j] = float64(i * j)
		}
		dataset.AddFeature(feature.Name, values)
		features = append(features, feature)
	}

	// This might timeout or succeed quickly
	_, err := splitter.FindBestSplit(context.Background(), dataset, features)
	if err != nil && err != context.DeadlineExceeded {
		t.Errorf("FindBestSplit() with timeout should succeed or return DeadlineExceeded, got %v", err)
	}
}

// Test additional edge cases for better coverage
func TestAdditionalEdgeCases(t *testing.T) {
	t.Run("NewC45Splitter with invalid options", func(t *testing.T) {
		// Test with MinSamplesLeaf > MinSamplesSplit (invalid configuration)
		_, err := NewC45Splitter[string](
			WithMinSamplesSplit(2),
			WithMinSamplesLeaf(5), // Invalid: leaf samples > split samples
		)
		if err == nil {
			t.Error("NewC45Splitter() with inconsistent config should return error")
		}
	})

	t.Run("CalculateImpurity with nil dataset", func(t *testing.T) {
		splitter, _ := NewC45Splitter[string]()
		_, err := splitter.CalculateImpurity(nil)
		if err == nil {
			t.Error("CalculateImpurity() with nil dataset should return error")
		}
	})

	t.Run("CalculateImpurity with MSE on non-numeric targets", func(t *testing.T) {
		splitter, _ := NewC45Splitter[string](WithImpurityCriterion(MSEImpurity))
		dataset := NewMockDataset[string](3)
		dataset.SetTargets([]string{"non", "numeric", "targets"})

		_, err := splitter.CalculateImpurity(dataset)
		if err == nil {
			t.Error("CalculateImpurity() with MSE on non-numeric targets should return error")
		}
	})

	t.Run("calculateMSE with conversion errors", func(t *testing.T) {
		splitter, _ := NewC45Splitter[interface{}](WithImpurityCriterion(MSEImpurity))
		dataset := NewMockDataset[interface{}](3)
		// Set targets that can't be converted to float64
		dataset.SetTargets([]interface{}{[]int{1, 2, 3}, map[string]int{"a": 1}, "not_a_number"})

		_, err := splitter.calculateMSE(dataset)
		if err == nil {
			t.Error("calculateMSE() with unconvertible targets should return error")
		}
	})

	t.Run("evaluateFeature with unsupported feature type", func(t *testing.T) {
		splitter, _ := NewC45Splitter[string]()
		dataset, _ := createSimpleClassificationDataset()

		// Create feature with unsupported type
		feature := &models.Feature{
			Name: "unsupported",
			Type: "unsupported_type",
		}

		_, err := splitter.evaluateFeature(dataset, feature, 1.0)
		if err == nil {
			t.Error("evaluateFeature() with unsupported feature type should return error")
		}
	})

	t.Run("getFeatureValue comprehensive error testing", func(t *testing.T) {
		splitter, _ := NewC45Splitter[string]()
		dataset, features := createSimpleClassificationDataset()

		// Test with uninitialized splitter (nil splitter components)
		var nilSplitter *C45Splitter[string]
		_, err := nilSplitter.getFeatureValue(dataset, 0, features[0])
		if err == nil {
			t.Error("getFeatureValue() with nil splitter should return error")
		}

		// Test with feature having empty name
		emptyFeature := &models.Feature{Name: "", Type: models.NumericFeature}
		_, err = splitter.getFeatureValue(dataset, 0, emptyFeature)
		if err == nil {
			t.Error("getFeatureValue() with empty feature name should return error")
		}
	})
}

// Test more comprehensive parallel processing scenarios
func TestParallelProcessingComprehensive(t *testing.T) {
	t.Run("parallel processing with worker errors", func(t *testing.T) {
		splitter, _ := NewC45Splitter[string](WithMaxWorkers(4))

		// Create a dataset that will cause some workers to fail
		dataset := NewMockDataset[string](5)
		dataset.SetTargets([]string{"a", "b", "a", "b", "a"})

		var features []*models.Feature

		// Add some valid features
		validValues := []interface{}{1.0, 2.0, 3.0, 4.0, 5.0}
		dataset.AddFeature("valid", validValues)
		features = append(features, createNumericFeature("valid"))

		// Add features that will cause errors (missing data)
		for i := 0; i < 6; i++ { // Ensure parallel processing
			features = append(features, createNumericFeature(fmt.Sprintf("missing_%d", i)))
		}

		// Some workers will fail, but others should succeed
		result, err := splitter.FindBestSplit(context.Background(), dataset, features)

		// Should either succeed with the valid feature or fail gracefully
		if err != nil && result != nil {
			t.Error("FindBestSplit() should not return both error and result")
		}
		if err == nil && result == nil {
			t.Error("FindBestSplit() should return either error or result")
		}
	})

	t.Run("context timeout during parallel processing", func(t *testing.T) {
		splitter, _ := NewC45Splitter[string](WithMaxWorkers(4))
		dataset, features := createSimpleClassificationDataset()

		// Add many features to ensure parallel processing takes time
		for i := 0; i < 20; i++ {
			feature := createNumericFeature(fmt.Sprintf("feature_%d", i))
			values := make([]interface{}, dataset.GetSize())
			for j := range values {
				values[j] = float64(i*j + j)
			}
			dataset.AddFeature(feature.Name, values)
			features = append(features, feature)
		}

		// Very short timeout
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Nanosecond)
		defer cancel()

		_, err := splitter.FindBestSplit(ctx, dataset, features)
		if err == nil {
			t.Error("FindBestSplit() with very short timeout should return error")
		}
	})
}

func TestAdaptiveTimeout(t *testing.T) {
	limits := DefaultResourceLimits()

	t.Run("adaptive timeouts enabled", func(t *testing.T) {
		limits.AdaptiveTimeouts = true
		baseTimeout := 100 * time.Millisecond

		// Test with context that has plenty of time remaining
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		adaptiveTimeout := limits.CalculateAdaptiveTimeout(ctx, baseTimeout)

		// Should be at least the minimum timeout
		if adaptiveTimeout < minWorkerTimeout {
			t.Errorf("Adaptive timeout %v is less than minimum %v", adaptiveTimeout, minWorkerTimeout)
		}

		// Should not exceed the base timeout
		if adaptiveTimeout > baseTimeout {
			t.Errorf("Adaptive timeout %v exceeds base timeout %v", adaptiveTimeout, baseTimeout)
		}
	})

	t.Run("adaptive timeouts disabled", func(t *testing.T) {
		limits.AdaptiveTimeouts = false
		baseTimeout := 100 * time.Millisecond

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		adaptiveTimeout := limits.CalculateAdaptiveTimeout(ctx, baseTimeout)

		// Should return exactly the base timeout
		if adaptiveTimeout != baseTimeout {
			t.Errorf("Expected base timeout %v, got %v", baseTimeout, adaptiveTimeout)
		}
	})

	t.Run("context with short deadline", func(t *testing.T) {
		limits.AdaptiveTimeouts = true
		baseTimeout := 100 * time.Millisecond

		// Context with very short deadline
		ctx, cancel := context.WithTimeout(context.Background(), 20*time.Millisecond)
		defer cancel()

		adaptiveTimeout := limits.CalculateAdaptiveTimeout(ctx, baseTimeout)

		// Should be at least the minimum timeout
		if adaptiveTimeout < minWorkerTimeout {
			t.Errorf("Adaptive timeout %v is less than minimum %v", adaptiveTimeout, minWorkerTimeout)
		}
	})

	t.Run("context without deadline", func(t *testing.T) {
		limits.AdaptiveTimeouts = true
		baseTimeout := 100 * time.Millisecond

		ctx := context.Background() // No deadline

		adaptiveTimeout := limits.CalculateAdaptiveTimeout(ctx, baseTimeout)

		// Should return the base timeout
		if adaptiveTimeout != baseTimeout {
			t.Errorf("Expected base timeout %v, got %v", baseTimeout, adaptiveTimeout)
		}
	})
}

func TestWithAdaptiveTimeouts(t *testing.T) {
	t.Run("enable adaptive timeouts", func(t *testing.T) {
		config := &SplitterConfig{Limits: DefaultResourceLimits()}
		config.Limits.AdaptiveTimeouts = false // Start disabled

		err := WithAdaptiveTimeouts(true)(config)
		if err != nil {
			t.Errorf("WithAdaptiveTimeouts() error = %v", err)
		}

		if !config.Limits.AdaptiveTimeouts {
			t.Error("Expected adaptive timeouts to be enabled")
		}
	})

	t.Run("disable adaptive timeouts", func(t *testing.T) {
		config := &SplitterConfig{Limits: DefaultResourceLimits()}
		config.Limits.AdaptiveTimeouts = true // Start enabled

		err := WithAdaptiveTimeouts(false)(config)
		if err != nil {
			t.Errorf("WithAdaptiveTimeouts() error = %v", err)
		}

		if config.Limits.AdaptiveTimeouts {
			t.Error("Expected adaptive timeouts to be disabled")
		}
	})
}

func TestStringToImpurityCriterion(t *testing.T) {
	tests := []struct {
		input    string
		expected ImpurityCriterion
		name     string
	}{
		{"gini", GiniImpurity, "gini"},
		{"entropy", EntropyImpurity, "entropy"},
		{"mse", MSEImpurity, "mse"},
		{"unknown", EntropyImpurity, "unknown_default"},
		{"", EntropyImpurity, "empty_default"},
		{"GINI", EntropyImpurity, "case_sensitive"},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := StringToImpurityCriterion(test.input)
			if result != test.expected {
				t.Errorf("StringToImpurityCriterion(%q) = %v, expected %v", test.input, result, test.expected)
			}
		})
	}
}

package training

import (
	"fmt"
	"testing"

	"github.com/berrijam/mulberri/pkg/models"
)

func TestCreateFeatures(t *testing.T) {
	tests := []struct {
		name    string
		rows    [][]string
		headers []string
		wantLen int
		wantErr bool
	}{
		{
			name:    "empty headers",
			rows:    [][]string{{"1", "a"}, {"2", "b"}},
			headers: []string{},
			wantLen: 0,
			wantErr: false,
		},
		{
			name:    "normal case with mixed features",
			rows:    [][]string{{"1", "apple"}, {"2", "banana"}, {"3", "cherry"}},
			headers: []string{"number", "fruit"},
			wantLen: 2,
			wantErr: false,
		},
		{
			name:    "single feature",
			rows:    [][]string{{"1"}, {"2"}, {"3"}},
			headers: []string{"number"},
			wantLen: 1,
			wantErr: false,
		},
		{
			name:    "rows with different lengths",
			rows:    [][]string{{"1", "apple"}, {"2"}, {"3", "banana", "extra"}},
			headers: []string{"number", "fruit"},
			wantLen: 2,
			wantErr: false, // Should handle missing values gracefully
		},
		{
			name:    "empty rows",
			rows:    [][]string{},
			headers: []string{"number", "fruit"},
			wantLen: 2,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CreateFeatures(tt.rows, tt.headers)

			if (err != nil) != tt.wantErr {
				t.Errorf("CreateFeatures() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if len(got) != tt.wantLen {
				t.Errorf("CreateFeatures() got length = %v, want %v", len(got), tt.wantLen)
			}

			// Verify feature properties
			for i, feature := range got {
				if feature == nil {
					t.Errorf("CreateFeatures() feature at index %d is nil", i)
					continue
				}

				if feature.Name != tt.headers[i] {
					t.Errorf("CreateFeatures() feature name = %v, want %v", feature.Name, tt.headers[i])
				}

				if feature.ColumnNumber != i {
					t.Errorf("CreateFeatures() feature ColumnNumber = %v, want %v", feature.ColumnNumber, i)
				}
			}
		})
	}
}

func TestCreateFeaturesValidation(t *testing.T) {
	tests := []struct {
		name        string
		rows        [][]string
		headers     []string
		expectError bool
		errorField  string
	}{
		{
			name:        "nil headers",
			rows:        [][]string{{"1", "2"}},
			headers:     nil,
			expectError: true,
			errorField:  "headers",
		},
		{
			name:        "nil rows",
			rows:        nil,
			headers:     []string{"col1", "col2"},
			expectError: true,
			errorField:  "rows",
		},
		{
			name:        "empty header",
			rows:        [][]string{{"1", "2"}},
			headers:     []string{"col1", ""},
			expectError: true,
			errorField:  "headers",
		},
		{
			name:        "whitespace header",
			rows:        [][]string{{"1", "2"}},
			headers:     []string{"col1", "   "},
			expectError: true,
			errorField:  "headers",
		},
		{
			name:        "duplicate headers",
			rows:        [][]string{{"1", "2"}},
			headers:     []string{"col1", "col1"},
			expectError: true,
			errorField:  "headers",
		},
		{
			name:        "valid input",
			rows:        [][]string{{"1", "2"}},
			headers:     []string{"col1", "col2"},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := CreateFeatures(tt.rows, tt.headers)

			if tt.expectError {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}

				if trainingErr, ok := err.(*TrainingError); ok {
					if trainingErr.Field != tt.errorField {
						t.Errorf("Expected error field '%s', got '%s'", tt.errorField, trainingErr.Field)
					}
				} else {
					t.Errorf("Expected TrainingError, got %T", err)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			}
		})
	}
}

func TestCreateFeaturesWithConfig(t *testing.T) {
	// Test with custom configuration
	config := &FeatureCreationConfig{
		MinNumericSamples:   1,
		NumericThreshold:    0.5,
		TrimWhitespace:      false,
		HandleMissingValues: false,
		ValidateInputs:      false,
	}

	rows := [][]string{{"1", " apple "}, {"2", " banana"}}
	headers := []string{"number", "fruit"}

	features, err := CreateFeaturesWithConfig(rows, headers, config)
	if err != nil {
		t.Fatalf("CreateFeaturesWithConfig() error = %v", err)
	}

	if len(features) != 2 {
		t.Errorf("Expected 2 features, got %d", len(features))
	}

	// With TrimWhitespace = false, the categorical values should include whitespace
	fruitFeature := features[1]
	if fruitFeature.Type != models.CategoricalFeature {
		t.Errorf("Expected categorical feature, got %v", fruitFeature.Type)
	}

	// Test with nil config (should use defaults)
	_, err = CreateFeaturesWithConfig(rows, headers, nil)
	if err != nil {
		t.Errorf("CreateFeaturesWithConfig() with nil config should work: %v", err)
	}
}

func TestAnalyzeAndCreateFeature(t *testing.T) {
	tests := []struct {
		name         string
		featureName  string
		index        int
		values       []string
		expectedType models.FeatureType
		expectError  bool
	}{
		{
			name:         "numeric feature",
			featureName:  "age",
			index:        0,
			values:       []string{"25", "30", "35"},
			expectedType: models.NumericFeature,
			expectError:  false,
		},
		{
			name:         "categorical feature",
			featureName:  "color",
			index:        1,
			values:       []string{"red", "blue", "green"},
			expectedType: models.CategoricalFeature,
			expectError:  false,
		},
		{
			name:         "mixed values defaults to categorical",
			featureName:  "mixed",
			index:        2,
			values:       []string{"abc", "123", "def"},
			expectedType: models.CategoricalFeature,
			expectError:  false,
		},
		{
			name:         "empty values",
			featureName:  "empty",
			index:        3,
			values:       []string{},
			expectedType: models.CategoricalFeature,
			expectError:  false,
		},
		{
			name:         "all empty strings",
			featureName:  "blank",
			index:        4,
			values:       []string{"", "  ", ""},
			expectedType: models.CategoricalFeature,
			expectError:  false,
		},
		{
			name:         "empty feature name",
			featureName:  "",
			index:        5,
			values:       []string{"a", "b"},
			expectedType: models.CategoricalFeature,
			expectError:  true,
		},
		{
			name:         "negative index",
			featureName:  "test",
			index:        -1,
			values:       []string{"a", "b"},
			expectedType: models.CategoricalFeature,
			expectError:  true,
		},
		{
			name:         "nil values",
			featureName:  "test",
			index:        0,
			values:       nil,
			expectedType: models.CategoricalFeature,
			expectError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			feature, err := AnalyzeAndCreateFeature(tt.featureName, tt.index, tt.values)

			if tt.expectError {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}

				if _, ok := err.(*TrainingError); !ok {
					t.Errorf("Expected TrainingError, got %T", err)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
					return
				}

				if feature == nil {
					t.Fatal("AnalyzeAndCreateFeature() returned nil")
				}

				if feature.Name != tt.featureName {
					t.Errorf("AnalyzeAndCreateFeature() name = %v, want %v", feature.Name, tt.featureName)
				}

				if feature.ColumnNumber != tt.index {
					t.Errorf("AnalyzeAndCreateFeature() ColumnNumber = %v, want %v", feature.ColumnNumber, tt.index)
				}

				if feature.Type != tt.expectedType {
					t.Errorf("AnalyzeAndCreateFeature() type = %v, want %v", feature.Type, tt.expectedType)
				}
			}
		})
	}
}

func TestAnalyzeAndCreateFeatureWithConfig(t *testing.T) {
	// Test with validation disabled - but name validation is always enabled
	config := &FeatureCreationConfig{
		ValidateInputs: false,
	}

	// This should still error because name validation is always performed
	_, err := AnalyzeAndCreateFeatureWithConfig("", 0, []string{"a", "b"}, config)
	if err == nil {
		t.Error("Expected error for empty name even with validation disabled")
	}

	// Test with valid name and validation disabled
	feature, err := AnalyzeAndCreateFeatureWithConfig("test", 0, []string{"a", "b"}, config)
	if err != nil {
		t.Errorf("Expected no error with valid name, got: %v", err)
	}
	if feature == nil {
		t.Error("Expected feature to be created")
	}

	// Test with nil config (should use defaults)
	_, err = AnalyzeAndCreateFeatureWithConfig("test", 0, []string{"a", "b"}, nil)
	if err != nil {
		t.Errorf("Expected no error with nil config, got: %v", err)
	}
}

func TestCreateNumericFeature(t *testing.T) {
	tests := []struct {
		name        string
		featureName string
		index       int
		values      []string
		expectMin   float64
		expectMax   float64
		expectRange bool
		expectError bool
	}{
		{
			name:        "normal numeric values",
			featureName: "score",
			index:       0,
			values:      []string{"10", "20", "5", "30"},
			expectMin:   5.0,
			expectMax:   30.0,
			expectRange: true,
			expectError: false,
		},
		{
			name:        "single value",
			featureName: "single",
			index:       1,
			values:      []string{"42"},
			expectMin:   41.999,
			expectMax:   42.001,
			expectRange: true,
			expectError: false,
		},
		{
			name:        "with empty values",
			featureName: "sparse",
			index:       2,
			values:      []string{"1", "", "3", "2"},
			expectMin:   1.0,
			expectMax:   3.0,
			expectRange: true,
			expectError: false,
		},
		{
			name:        "with invalid numbers",
			featureName: "mixed",
			index:       3,
			values:      []string{"1", "abc", "3"},
			expectMin:   1.0,
			expectMax:   3.0,
			expectRange: true,
			expectError: false,
		},
		{
			name:        "all empty values",
			featureName: "empty",
			index:       4,
			values:      []string{"", ""},
			expectRange: false,
			expectError: true, // Should error with validation when no valid values
		},
		{
			name:        "floating point values",
			featureName: "float",
			index:       6,
			values:      []string{"1.5", "2.7", "0.3"},
			expectMin:   0.3,
			expectMax:   2.7,
			expectRange: true,
			expectError: false,
		},
		{
			name:        "negative values",
			featureName: "negative",
			index:       7,
			values:      []string{"-5", "10", "-2"},
			expectMin:   -5.0,
			expectMax:   10.0,
			expectRange: true,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := DefaultFeatureCreationConfig()
			feature, err := CreateNumericFeatureWithConfig(tt.featureName, tt.index, tt.values, config)

			if tt.expectError {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				return
			}

			if err != nil {
				t.Errorf("CreateNumericFeatureWithConfig() error = %v", err)
				return
			}

			if feature == nil {
				t.Fatal("CreateNumericFeatureWithConfig() returned nil")
			}

			if feature.Name != tt.featureName {
				t.Errorf("CreateNumericFeatureWithConfig() name = %v, want %v", feature.Name, tt.featureName)
			}

			if feature.ColumnNumber != tt.index {
				t.Errorf("CreateNumericFeatureWithConfig() ColumnNumber = %v, want %v", feature.ColumnNumber, tt.index)
			}

			if feature.Type != models.NumericFeature {
				t.Errorf("CreateNumericFeatureWithConfig() type = %v, want %v", feature.Type, models.NumericFeature)
			}

			if tt.expectRange {
				// Check if range is approximately correct (for single values with epsilon)
				if tt.name == "single value" {
					if feature.Min < tt.expectMin || feature.Max > tt.expectMax {
						t.Errorf("CreateNumericFeatureWithConfig() range [%v, %v] not within expected [%v, %v]",
							feature.Min, feature.Max, tt.expectMin, tt.expectMax)
					}
				} else {
					if feature.Min != tt.expectMin {
						t.Errorf("CreateNumericFeatureWithConfig() min = %v, want %v", feature.Min, tt.expectMin)
					}
					if feature.Max != tt.expectMax {
						t.Errorf("CreateNumericFeatureWithConfig() max = %v, want %v", feature.Max, tt.expectMax)
					}
				}
			}
		})
	}
}

func TestCreateCategoricalFeature(t *testing.T) {
	tests := []struct {
		name           string
		featureName    string
		index          int
		values         []string
		expectedValues []string
		expectError    bool
	}{
		{
			name:           "normal categorical values",
			featureName:    "color",
			index:          0,
			values:         []string{"red", "blue", "green", "red"},
			expectedValues: []string{"red", "blue", "green"},
			expectError:    false,
		},
		{
			name:           "values with whitespace",
			featureName:    "trimmed",
			index:          1,
			values:         []string{" apple ", "banana", " apple"},
			expectedValues: []string{"apple", "banana"},
			expectError:    false,
		},
		{
			name:           "empty values skipped",
			featureName:    "sparse",
			index:          2,
			values:         []string{"a", "", "b", ""},
			expectedValues: []string{"a", "b"}, // Empty strings are skipped
			expectError:    false,
		},
		{
			name:           "single value",
			featureName:    "single",
			index:          3,
			values:         []string{"only"},
			expectedValues: []string{"only"},
			expectError:    false,
		},
		{
			name:           "empty array",
			featureName:    "empty",
			index:          4,
			values:         []string{},
			expectedValues: []string{},
			expectError:    false,
		},
		{
			name:           "all same values",
			featureName:    "uniform",
			index:          5,
			values:         []string{"same", "same", "same"},
			expectedValues: []string{"same"},
			expectError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := DefaultFeatureCreationConfig()
			feature, err := CreateCategoricalFeatureWithConfig(tt.featureName, tt.index, tt.values, config)

			if tt.expectError {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				return
			}

			if err != nil {
				t.Errorf("CreateCategoricalFeatureWithConfig() error = %v", err)
				return
			}

			if feature == nil {
				t.Fatal("CreateCategoricalFeatureWithConfig() returned nil")
			}

			if feature.Name != tt.featureName {
				t.Errorf("CreateCategoricalFeatureWithConfig() name = %v, want %v", feature.Name, tt.featureName)
			}

			if feature.ColumnNumber != tt.index {
				t.Errorf("CreateCategoricalFeatureWithConfig() ColumnNumber = %v, want %v", feature.ColumnNumber, tt.index)
			}

			if feature.Type != models.CategoricalFeature {
				t.Errorf("CreateCategoricalFeatureWithConfig() type = %v, want %v", feature.Type, models.CategoricalFeature)
			}

			// Check categorical values using the feature's method
			actualValues := feature.GetCategoricalValues()
			if len(actualValues) != len(tt.expectedValues) {
				t.Errorf("CreateCategoricalFeatureWithConfig() values count = %v, want %v", len(actualValues), len(tt.expectedValues))
			}

			// Create a map for easier checking
			valueMap := make(map[string]bool)
			for _, val := range actualValues {
				valueMap[val] = true
			}

			for _, expectedVal := range tt.expectedValues {
				if !valueMap[expectedVal] {
					t.Errorf("CreateCategoricalFeatureWithConfig() missing expected value: %v", expectedVal)
				}
			}
		})
	}
}

func TestDetermineFeatureType(t *testing.T) {
	tests := []struct {
		name     string
		values   []string
		expected models.FeatureType
	}{
		{
			name:     "all numeric",
			values:   []string{"1", "2", "3"},
			expected: models.NumericFeature,
		},
		{
			name:     "all categorical",
			values:   []string{"a", "b", "c"},
			expected: models.CategoricalFeature,
		},
		{
			name:     "first value numeric",
			values:   []string{"1", "abc", "def"},
			expected: models.CategoricalFeature,
		},
		{
			name:     "first value categorical",
			values:   []string{"abc", "1", "2"},
			expected: models.CategoricalFeature,
		},
		{
			name:     "floating point numbers",
			values:   []string{"1.5", "2.7", "3.14"},
			expected: models.NumericFeature,
		},
		{
			name:     "negative numbers",
			values:   []string{"-5", "-10", "15"},
			expected: models.NumericFeature,
		},
		{
			name:     "scientific notation",
			values:   []string{"1e5", "2E-3", "3.14e2"},
			expected: models.NumericFeature,
		},
		{
			name:     "empty values at start",
			values:   []string{"", "  ", "123"},
			expected: models.CategoricalFeature, // Only one numeric value, below threshold
		},
		{
			name:     "empty values at start then categorical",
			values:   []string{"", "  ", "abc"},
			expected: models.CategoricalFeature,
		},
		{
			name:     "all empty values",
			values:   []string{"", "  ", ""},
			expected: models.CategoricalFeature,
		},
		{
			name:     "empty array",
			values:   []string{},
			expected: models.CategoricalFeature,
		},
		{
			name:     "whitespace with numbers",
			values:   []string{"  123  ", "456", "789"},
			expected: models.NumericFeature,
		},
		{
			name:     "numeric strings that can't be parsed",
			values:   []string{"123abc", "456def"},
			expected: models.CategoricalFeature,
		},
		{
			name:     "zero values",
			values:   []string{"0", "0.0", "-0"},
			expected: models.NumericFeature,
		},
		{
			name:     "mostly numeric values",
			values:   []string{"1", "2", "3", "4", "abc"},
			expected: models.NumericFeature, // 80% numeric meets threshold
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := DetermineFeatureType(tt.values)
			if err != nil {
				t.Errorf("DetermineFeatureType() error = %v", err)
			}
			if result != tt.expected {
				t.Errorf("DetermineFeatureType() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestDetermineFeatureTypeWithConfig(t *testing.T) {
	// Test with custom thresholds
	config := &FeatureCreationConfig{
		MinNumericSamples: 1,
		NumericThreshold:  0.5,
		ValidateInputs:    true,
	}

	// 50% numeric should be numeric with threshold 0.5
	values := []string{"1", "abc"}
	result, err := DetermineFeatureTypeWithConfig(values, config)
	if err != nil {
		t.Errorf("DetermineFeatureTypeWithConfig() error = %v", err)
	}
	if result != models.NumericFeature {
		t.Errorf("DetermineFeatureTypeWithConfig() = %v, want %v", result, models.NumericFeature)
	}

	// Test with strict threshold (1.0)
	config.NumericThreshold = 1.0
	result, err = DetermineFeatureTypeWithConfig(values, config)
	if err != nil {
		t.Errorf("DetermineFeatureTypeWithConfig() error = %v", err)
	}
	if result != models.CategoricalFeature {
		t.Errorf("DetermineFeatureTypeWithConfig() with strict threshold = %v, want %v", result, models.CategoricalFeature)
	}

	// Test with nil values and validation
	_, err = DetermineFeatureTypeWithConfig(nil, config)
	if err == nil {
		t.Error("Expected error for nil values with validation enabled")
	}

	// Test with nil config
	result, err = DetermineFeatureTypeWithConfig([]string{"1", "2"}, nil)
	if err != nil {
		t.Errorf("DetermineFeatureTypeWithConfig() with nil config error = %v", err)
	}
	if result != models.NumericFeature {
		t.Errorf("DetermineFeatureTypeWithConfig() with nil config = %v, want %v", result, models.NumericFeature)
	}
}

func TestBackwardCompatibility(t *testing.T) {
	// Test that old functions still work through public API
	feature, err := AnalyzeAndCreateFeature("age", 0, []string{"25", "30", "35"})
	if err != nil {
		t.Errorf("AnalyzeAndCreateFeature() error = %v", err)
	}
	if feature == nil {
		t.Error("AnalyzeAndCreateFeature() returned nil")
	}
	catFeature, err := AnalyzeAndCreateFeature("color", 0, []string{"red", "blue"})
	if err != nil {
		t.Errorf("AnalyzeAndCreateFeature() error = %v", err)
	}
	if catFeature == nil {
		t.Error("AnalyzeAndCreateFeature() returned nil")
	}

	// Test that invalid inputs are handled gracefully
	_, err = AnalyzeAndCreateFeature("", -1, []string{"invalid"})
	if err == nil {
		t.Error("Expected error for invalid inputs, got none")
	}
}

func TestTrainingError(t *testing.T) {
	// Test error message formatting
	err := &TrainingError{
		Op:     "test_operation",
		Field:  "test_field",
		Index:  5,
		Reason: "test reason",
	}

	expected := "training test_operation error at index 5 for field 'test_field': test reason"
	if err.Error() != expected {
		t.Errorf("Expected error message '%s', got '%s'", expected, err.Error())
	}

	// Test error without index
	err2 := &TrainingError{
		Op:     "test_operation",
		Field:  "test_field",
		Index:  -1,
		Reason: "test reason",
	}

	expected2 := "training test_operation error for field 'test_field': test reason"
	if err2.Error() != expected2 {
		t.Errorf("Expected error message '%s', got '%s'", expected2, err2.Error())
	}

	// Test error without field
	err3 := &TrainingError{
		Op:     "test_operation",
		Index:  -1,
		Reason: "test reason",
	}

	expected3 := "training test_operation error: test reason"
	if err3.Error() != expected3 {
		t.Errorf("Expected error message '%s', got '%s'", expected3, err3.Error())
	}

	// Test error unwrapping
	underlying := fmt.Errorf("underlying error")
	err4 := &TrainingError{
		Op:     "test",
		Reason: "test",
		Err:    underlying,
	}

	if err4.Unwrap() != underlying {
		t.Error("Error unwrapping should return underlying error")
	}
}

func TestDefaultFeatureCreationConfig(t *testing.T) {
	config := DefaultFeatureCreationConfig()
	if config == nil {
		t.Fatal("DefaultFeatureCreationConfig() returned nil")
	}

	// Test default values
	if config.MinNumericSamples != 2 {
		t.Errorf("Expected MinNumericSamples = 2, got %d", config.MinNumericSamples)
	}

	if config.NumericThreshold != 0.8 {
		t.Errorf("Expected NumericThreshold = 0.8, got %f", config.NumericThreshold)
	}

	if !config.TrimWhitespace {
		t.Error("Expected TrimWhitespace = true")
	}

	if !config.HandleMissingValues {
		t.Error("Expected HandleMissingValues = true")
	}

	if !config.ValidateInputs {
		t.Error("Expected ValidateInputs = true")
	}
}

func TestExtractColumnValues(t *testing.T) {
	config := DefaultFeatureCreationConfig()

	tests := []struct {
		name        string
		rows        [][]string
		colIdx      int
		config      *FeatureCreationConfig
		expected    []string
		expectError bool
	}{
		{
			name:        "normal extraction",
			rows:        [][]string{{"a", "b"}, {"c", "d"}},
			colIdx:      1,
			config:      config,
			expected:    []string{"b", "d"},
			expectError: false,
		},
		{
			name:        "missing values with handling",
			rows:        [][]string{{"a", "b"}, {"c"}},
			colIdx:      1,
			config:      config,
			expected:    []string{"b", ""},
			expectError: false,
		},
		{
			name:   "missing values without handling",
			rows:   [][]string{{"a", "b"}, {"c"}},
			colIdx: 1,
			config: &FeatureCreationConfig{
				HandleMissingValues: false,
				TrimWhitespace:      true,
			},
			expectError: true,
		},
		{
			name:        "negative column index",
			rows:        [][]string{{"a", "b"}},
			colIdx:      -1,
			config:      config,
			expectError: true,
		},
		{
			name:        "whitespace trimming",
			rows:        [][]string{{" a ", " b "}, {" c ", " d "}},
			colIdx:      0,
			config:      config,
			expected:    []string{"a", "c"},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := extractColumnValues(tt.rows, tt.colIdx, tt.config)

			if tt.expectError {
				if err == nil {
					t.Error("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("extractColumnValues() error = %v", err)
				return
			}

			if len(result) != len(tt.expected) {
				t.Errorf("extractColumnValues() length = %d, want %d", len(result), len(tt.expected))
				return
			}

			for i, expected := range tt.expected {
				if result[i] != expected {
					t.Errorf("extractColumnValues()[%d] = %q, want %q", i, result[i], expected)
				}
			}
		})
	}
}

// Integration test to ensure the full pipeline works correctly with enhanced validation
func TestFeatureCreationIntegration(t *testing.T) {
	rows := [][]string{
		{"25", "Engineer", "100000.50", "Yes"},
		{"30", "Doctor", "150000.75", "No"},
		{"22", "Teacher", "45000.00", "Yes"},
		{"", "Artist", "35000", ""}, // Empty values should be handled
		{"35", "", "200000.25", "No"}, // Empty profession should be handled
	}
	headers := []string{"age", "profession", "salary", "has_degree"}

	features, err := CreateFeatures(rows, headers)
	if err != nil {
		t.Fatalf("CreateFeatures() error = %v", err)
	}

	if len(features) != 4 {
		t.Fatalf("Expected 4 features, got %d", len(features))
	}

	// Test age feature (numeric)
	ageFeature := features[0]
	if ageFeature.Type != models.NumericFeature {
		t.Errorf("Age feature should be numeric, got %v", ageFeature.Type)
	}
	if ageFeature.Min != 22.0 || ageFeature.Max != 35.0 {
		t.Errorf("Age feature range should be [22, 35], got [%v, %v]", ageFeature.Min, ageFeature.Max)
	}

	// Test profession feature (categorical)
	professionFeature := features[1]
	if professionFeature.Type != models.CategoricalFeature {
		t.Errorf("Profession feature should be categorical, got %v", professionFeature.Type)
	}

	// Use the new method to get categorical values
	professionValues := professionFeature.GetCategoricalValues()
	expectedProfessions := 4 // "Engineer", "Doctor", "Teacher", "Artist" (empty string is skipped)
	if len(professionValues) != expectedProfessions {
		t.Errorf("Profession feature should have %d unique values, got %d", expectedProfessions, len(professionValues))
	}

	// Test salary feature (numeric)
	salaryFeature := features[2]
	if salaryFeature.Type != models.NumericFeature {
		t.Errorf("Salary feature should be numeric, got %v", salaryFeature.Type)
	}
	if salaryFeature.Min != 35000.0 || salaryFeature.Max != 200000.25 {
		t.Errorf("Salary feature range should be [35000, 200000.25], got [%v, %v]", salaryFeature.Min, salaryFeature.Max)
	}

	// Test degree feature (categorical)
	degreeFeature := features[3]
	if degreeFeature.Type != models.CategoricalFeature {
		t.Errorf("Degree feature should be categorical, got %v", degreeFeature.Type)
	}

	degreeValues := degreeFeature.GetCategoricalValues()
	expectedDegrees := 2 // "Yes", "No" (empty string is skipped)
	if len(degreeValues) != expectedDegrees {
		t.Errorf("Degree feature should have %d unique values, got %d", expectedDegrees, len(degreeValues))
	}
}

// Test feature creation with edge case data and enhanced error handling
func TestFeatureCreationEdgeCases(t *testing.T) {
	// Test with rows containing fewer columns than headers
	rows := [][]string{
		{"1"},      // Missing second column
		{"2", "b"}, // Complete row
		{"3"},      // Missing second column again
	}
	headers := []string{"num", "cat"}

	features, err := CreateFeatures(rows, headers)
	if err != nil {
		t.Fatalf("CreateFeatures() error = %v", err)
	}

	if len(features) != 2 {
		t.Fatalf("Expected 2 features, got %d", len(features))
	}

	// The second feature should be categorical and handle missing values
	catFeature := features[1]
	if catFeature.Type != models.CategoricalFeature {
		t.Errorf("Second feature should be categorical, got %v", catFeature.Type)
	}

	// Test validation of the created features
	for i, feature := range features {
		if err := feature.Validate(); err != nil {
			t.Errorf("Feature %d failed validation: %v", i, err)
		}
	}
}

// Test error propagation from models package
func TestModelErrorPropagation(t *testing.T) {
	// This test ensures that errors from the models package are properly wrapped

	// Try to create a feature with invalid parameters that would cause models package to error
	config := DefaultFeatureCreationConfig()

	// Test with invalid feature name (empty after trimming)
	_, err := AnalyzeAndCreateFeatureWithConfig("   ", 0, []string{"a", "b"}, config)
	if err == nil {
		t.Error("Expected error for empty feature name")
	}

	if trainingErr, ok := err.(*TrainingError); ok {
		if trainingErr.Op != "validate_feature_inputs" {
			t.Errorf("Expected validate_feature_inputs operation, got %s", trainingErr.Op)
		}
	} else {
		t.Errorf("Expected TrainingError, got %T", err)
	}
}
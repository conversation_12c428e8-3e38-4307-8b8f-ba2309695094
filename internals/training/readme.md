# Training Package Documentation

## Overview

The `training` package provides robust feature creation and analysis capabilities for machine learning applications. It automatically analyzes raw data to determine feature types (numeric or categorical) and creates structured `Feature` objects suitable for decision tree algorithms.

## Key Features

- **Automatic Type Detection**: Intelligently determines whether columns should be treated as numeric or categorical
- **Configurable Thresholds**: Customizable parameters for type determination logic
- **Error Handling**: Comprehensive error wrapping with detailed context
- **Missing Value Support**: Graceful handling of sparse or incomplete data
- **Validation**: Input validation with configurable strictness levels

## Core Functions

### CreateFeatures
```go
func CreateFeatures(rows [][]string, headers []string) ([]*models.Feature, error)
```
Primary function for converting raw tabular data into structured features using default configuration.

**Parameters:**
- `rows`: 2D string slice representing tabular data (rows × columns)
- `headers`: Column names corresponding to each column in rows

**Returns:**
- Array of `Feature` objects ready for machine learning algorithms
- Error if validation fails or feature creation encounters issues

### CreateFeaturesWithConfig
```go
func CreateFeaturesWithConfig(rows [][]string, headers []string, config *FeatureCreationConfig) ([]*models.Feature, error)
```
Advanced feature creation with custom configuration for fine-tuned control.

### AnalyzeAndCreateFeature
```go
func AnalyzeAndCreateFeature(name string, index int, values []string) (*models.Feature, error)
```
Analyzes a single column and creates the appropriate Feature object based on detected data type.

## Configuration

### FeatureCreationConfig
```go
type FeatureCreationConfig struct {
    MinNumericSamples   int     // Minimum samples needed to consider a column numeric (default: 2)
    NumericThreshold    float64 // Minimum ratio of numeric values (default: 0.8)
    TrimWhitespace      bool    // Whether to trim whitespace from values (default: true)
    HandleMissingValues bool    // Whether to handle missing values gracefully (default: true)
    ValidateInputs      bool    // Whether to validate input parameters (default: true)
}
```

**Default Configuration:**
```go
config := DefaultFeatureCreationConfig()
// MinNumericSamples: 2, NumericThreshold: 0.8, all bools: true
```

## Type Determination Logic

### Numeric Features
A column is classified as numeric when:
- At least `MinNumericSamples` values can be parsed as numbers
- The ratio of numeric values meets or exceeds `NumericThreshold`
- Supports integers, floats, scientific notation, and negative numbers

### Categorical Features
A column is classified as categorical when:
- It doesn't meet numeric criteria
- Contains non-numeric strings
- Default fallback for mixed or unclear data types

## Error Handling

### TrainingError
```go
type TrainingError struct {
    Op     string  // Operation where error occurred
    Field  string  // Field that caused the error
    Index  int     // Index in data (if applicable)
    Reason string  // Human-readable error description
    Err    error   // Underlying error (if any)
}
```

Provides detailed context for debugging, including operation name, field information, and data position.

## Usage Examples

### Basic Usage
```go
import "github.com/berrijam/mulberri/internals/training"

// Raw data from CSV or database
rows := [][]string{
    {"25", "Engineer", "75000.50"},
    {"30", "Doctor", "120000.75"},
    {"22", "Teacher", "45000.00"},
}
headers := []string{"age", "profession", "salary"}

// Create features with automatic type detection
features, err := training.CreateFeatures(rows, headers)
if err != nil {
    log.Fatal(err)
}

// Use features for machine learning
for _, feature := range features {
    fmt.Printf("%s: %v\n", feature.Name, feature.Type)
    // Output: age: NumericFeature, profession: CategoricalFeature, salary: NumericFeature
}
```

### Custom Configuration
```go
// Strict numeric detection
config := &training.FeatureCreationConfig{
    MinNumericSamples:   3,        // Need at least 3 numeric values
    NumericThreshold:    0.95,     // 95% must be numeric
    TrimWhitespace:      true,     // Clean data
    HandleMissingValues: true,     // Allow sparse data
    ValidateInputs:      true,     // Strict validation
}

features, err := training.CreateFeaturesWithConfig(rows, headers, config)
```

### Single Column Analysis
```go
// Analyze individual column
values := []string{"1.5", "2.7", "3.14", "4.2"}
feature, err := training.AnalyzeAndCreateFeature("measurements", 0, values)

if feature.Type == models.NumericFeature {
    fmt.Printf("Range: [%.2f, %.2f]\n", feature.Min, feature.Max)
}
```

## Data Handling

### Missing Values
- **Enabled** (default): Missing columns become empty strings, processed gracefully
- **Disabled**: Strict validation, errors on inconsistent row lengths

### Empty Values
- Empty strings are filtered out of categorical features
- Numeric analysis skips empty values
- All-empty columns default to categorical type

### Whitespace
- **Trimming enabled** (default): `" value "` becomes `"value"`
- **Trimming disabled**: Preserves exact string values including whitespace

## Integration

This package is designed to work seamlessly with:
- **CSV parsing**: Process data from `encoding/csv` or similar
- **Database results**: Handle query results from `database/sql`
- **Decision trees**: Output features ready for `models` package algorithms
- **Data pipelines**: Robust error handling for production workflows

## Performance Considerations

- **Memory efficient**: Processes data in single pass
- **Type detection**: O(n) complexity per column
- **Validation overhead**: Can be disabled for performance-critical applications
- **Large datasets**: Handles thousands of rows efficiently

## Error Recovery

The package implements graceful degradation:
- Invalid numeric values are treated as categorical
- Missing columns are handled based on configuration
- Model validation errors are wrapped with context
- Backward compatibility maintained for legacy code

## Best Practices

1. **Use default configuration** for most applications
2. **Enable validation** during development, consider disabling in production
3. **Handle TrainingError types** for detailed error information
4. **Verify feature types** match expectations before training
5. **Test with edge cases**: empty data, all missing values, mixed types


## Decision Tree Algorithm - Mathematical Formulas and Calculation Flow

### 1. Entropy Calculation

#### Formula
```
Entropy(S) = -Σ(i=1 to c) p_i × log₂(p_i)
```

Where:
- `S` = dataset/subset
- `c` = number of classes
- `p_i` = probability of class i = count(class_i) / total_samples

#### Description
Entropy measures the impurity or disorder in a dataset. Higher entropy indicates more mixed classes, lower entropy indicates purer data.

#### Calculation Steps
1. Count occurrences of each target class
2. Calculate probability: `p_i = count_i / n`
3. Apply entropy formula: `E = -Σ(p_i × log₂(p_i))`

#### Example
Dataset: [Yes, Yes, No, Yes, No]
- Count: Yes=3, No=2, Total=5
- Probabilities: p_yes = 3/5 = 0.6, p_no = 2/5 = 0.4
- Entropy = -(0.6 × log₂(0.6) + 0.4 × log₂(0.4))
- Entropy = -(0.6 × -0.737 + 0.4 × -1.322) = 0.971

---

### 2. Information Gain

#### Formula
```
Information_Gain(S, A) = Entropy(S) - Σ(v∈Values(A)) (|S_v|/|S|) × Entropy(S_v)
```

Where:
- `S` = original dataset
- `A` = attribute/feature
- `S_v` = subset of S where attribute A has value v
- `|S|` = size of dataset S

#### Description
Information gain measures how much uncertainty is reduced by splitting on a particular attribute.

#### Calculation Steps
1. Calculate entropy of original dataset: `Entropy(S)`
2. For each possible value of attribute A:
   - Create subset S_v where A = v
   - Calculate entropy of S_v: `Entropy(S_v)`
   - Calculate weight: `w_v = |S_v| / |S|`
3. Calculate weighted entropy: `Weighted_Entropy = Σ(w_v × Entropy(S_v))`
4. Information Gain = `Entropy(S) - Weighted_Entropy`

---

### 3. Split Information

#### Formula
```
Split_Info(S, A) = -Σ(v∈Values(A)) (|S_v|/|S|) × log₂(|S_v|/|S|)
```

#### Description
Split information measures how much information is used to divide the dataset based on attribute A. Used to normalize information gain.

#### Calculation Steps
1. For each value v of attribute A:
   - Calculate proportion: `p_v = |S_v| / |S|`
2. Apply formula: `Split_Info = -Σ(p_v × log₂(p_v))`

---

### 4. Gain Ratio

#### Formula
```
Gain_Ratio(S, A) = Information_Gain(S, A) / Split_Info(S, A)
```

#### Description
Gain ratio normalizes information gain by split information to avoid bias toward attributes with many values.

#### Advantages of Gain Ratio

- Information gain favors attributes with more distinct values
- Gain ratio provides fairer comparison between attributes
- Prevents overfitting to attributes with high cardinality

---


### 5. Split Selection

We have default configuration that are used in the tree building and split selection. The following is the algorithm for split selection.

```
Function FindBestSplit(dataset, features, target):
    
    1. Calculate base entropy:
       Base_Entropy = Entropy(dataset, target)
    
    2. If Base_Entropy = 0:
       Return (no split needed - pure node)
    
    3. Initialize: Best_Gain_Ratio = -1
    
    4. For each feature F in features:
       
       If F is categorical:
           split = CategoricalSplit(dataset, F, target)
       Else if F is numerical:
           split = NumericalSplit(dataset, F, target)
       
       If split.Gain_Ratio > Best_Gain_Ratio:
           Best_Split = split
           Best_Gain_Ratio = Gain_Ratio
    
    5. Return Best_Split
```

---

#### Usage 

1. Default Configuration

```bash
splitter, _ := training.NewC45Splitter()

bestSplit, err := splitter.FindBestSplit(ctx, dataset, features)

```
Uses CLI-defined defaults

Automatic worker count (CPU cores)

Entropy impurity

Safe resource limits


2. Classification

```bash
splitter, _ := training.NewC45Splitter(
    training.WithMinSamplesSplit(20),# depends with the package you are calling it from
    training.WithMinSamplesLeaf(10),
    training.WithImpurityCriterion(training.GiniImpurity),
    training.WithMaxWorkers(8),
)

bestSplit, err := splitter.FindBestSplit(ctx, dataset, features)

```

3. Regression
```bash
splitter, _ := training.NewC45Splitter(
    training.WithMinSamplesSplit(50),
    training.WithMinSamplesLeaf(20),
    training.WithImpurityCriterion(training.MSEImpurity),
    training.WithMaxWorkers(16),
    training.WithResourceLimits(training.ResourceLimits{
        MaxDatasetSize: 10_000_000,
        Timeout:        5 * time.Minute,
    }),
)

bestSplit, err := splitter.FindBestSplit(ctx, dataset, features)

```



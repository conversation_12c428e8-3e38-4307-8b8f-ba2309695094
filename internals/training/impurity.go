package training

import (
	"fmt"
	"math"
	"strconv"
)

// CalculateImpurity computes the impurity of a dataset using the configured criterion
func (c *C45Splitter[T]) CalculateImpurity(dataset Dataset[T]) (float64, error) {
	if dataset == nil {
		return 0, &ValidationError{Field: "dataset", Reason: "cannot be nil"}
	}

	n := dataset.GetSize()
	if n == 0 {
		return 0, nil
	}

	if c.config.Criterion == MSEImpurity {
		return c.calculateMSE(dataset)
	}

	return c.calculateClassificationImpurity(dataset)
}

// calculateClassificationImpurity calculates entropy or gini impurity
func (c *C45Splitter[T]) calculateClassificationImpurity(dataset Dataset[T]) (float64, error) {
	// Use pool for target distribution map
	dist := c.getTargetDistFromPool()
	defer c.returnTargetDistToPool(dist)

	var errors MultiError
	for _, idx := range dataset.GetIndices() {
		target, err := dataset.GetTarget(idx)
		if err != nil {
			errors.Add(fmt.Errorf("sample %d: %w", idx, err))
			continue
		}
		dist[target]++
	}

	if errors.HasErrors() {
		return 0, &SplitError{Op: "calculate_impurity", Err: &errors}
	}

	n := dataset.GetSize()
	switch c.config.Criterion {
	case GiniImpurity:
		return calculateGini(dist, n), nil
	default:
		return calculateEntropy(dist, n), nil
	}
}

// calculateMSE computes mean squared error for regression
func (c *C45Splitter[T]) calculateMSE(dataset Dataset[T]) (float64, error) {
	indices := dataset.GetIndices()
	n := len(indices)
	if n == 0 {
		return 0, nil
	}

	var sum, sumSq float64
	var errors MultiError

	for _, idx := range indices {
		target, err := dataset.GetTarget(idx)
		if err != nil {
			errors.Add(fmt.Errorf("sample %d: %w", idx, err))
			continue
		}

		x, err := c.convertToFloat64(target)
		if err != nil {
			errors.Add(fmt.Errorf("sample %d target conversion: %w", idx, err))
			continue
		}

		sum += x
		sumSq += x * x
	}

	if errors.HasErrors() {
		return 0, &SplitError{Op: "calculate_mse", Err: &errors}
	}

	mean := sum / float64(n)
	return sumSq/float64(n) - mean*mean, nil
}

// calculateDistImpurity calculates impurity for a given distribution
func (c *C45Splitter[T]) calculateDistributionImpurity(dist map[T]int, size int) float64 {
	if size == 0 {
		return 0
	}

	switch c.config.Criterion {
	case GiniImpurity:
		return calculateGini(dist, size)
	default:
		return calculateEntropy(dist, size)
	}
}

// calculateEntropy computes Shannon entropy for a target distribution
// Entropy = -Σ(p_i * log2(p_i)) where p_i is the proportion of class i
func calculateEntropy[T comparable](dist map[T]int, total int) float64 {
	if total == 0 {
		return 0
	}
	entropy := 0.0
	for _, count := range dist {
		if count > 0 {
			p := float64(count) / float64(total)
			entropy -= p * math.Log2(p)
		}
	}
	return entropy
}

// calculateGini computes Gini impurity for a target distribution
// Gini = 1 - Σ(p_i^2) where p_i is the proportion of class i
func calculateGini[T comparable](dist map[T]int, total int) float64 {
	if total == 0 {
		return 0
	}
	gini := 1.0
	for _, count := range dist {
		p := float64(count) / float64(total)
		gini -= p * p
	}
	return gini
}

// calculateSplitInfo computes the split information used in gain ratio calculation
// SplitInfo = -Σ((|S_i|/|S|) * log2(|S_i|/|S|)) where S_i are the split partitions
func calculateSplitInfo(sizes []int, total int) float64 {
	if total == 0 {
		return 0
	}
	splitInfo := 0.0
	for _, size := range sizes {
		if size > 0 {
			ratio := float64(size) / float64(total)
			splitInfo -= ratio * math.Log2(ratio)
		}
	}
	return splitInfo
}

// convertToFloat64 safely converts various numeric types to float64
func (c *C45Splitter[T]) convertToFloat64(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case uint:
		return float64(v), nil
	case uint32:
		return float64(v), nil
	case uint64:
		return float64(v), nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("unsupported numeric type: %T", v)
	}
}

package training

import (
	"context"
	"runtime"
	"time"

	"github.com/berrijam/mulberri/cmd/mulberri/cli"
)

// Default resource limits
const (
	defaultMaxFeatures     = 10000
	defaultMaxDatasetSize  = 10000000
	defaultMaxCacheSize    = 5000000
	defaultTimeout         = 60 * time.Second
	defaultWorkerTimeout   = 100 * time.Millisecond
	defaultDrainTimeout    = 200 * time.Millisecond
	minFeaturesForParallel = 4

	// Adaptive timeout configuration
	minWorkerTimeout   = 50 * time.Millisecond // Minimum timeout regardless of context
	maxWorkerTimeout   = 5 * time.Second       // Maximum timeout to prevent indefinite blocking
	workerTimeoutRatio = 0.05                  // 5% of remaining context time
)

// ResourceLimits prevents resource exhaustion attacks
type ResourceLimits struct {
	MaxFeatures      int
	MaxDatasetSize   int
	MaxCacheSize     int
	MaxCacheAge      time.Duration
	Timeout          time.Duration
	WorkerTimeout    time.Duration // Timeout for worker goroutines when context is cancelled
	DrainTimeout     time.Duration // Timeout for draining results when exiting early
	AdaptiveTimeouts bool          // Whether to use adaptive timeouts based on context
}

func DefaultResourceLimits() ResourceLimits {
	return ResourceLimits{
		MaxFeatures:      defaultMaxFeatures,
		MaxDatasetSize:   defaultMaxDatasetSize,
		MaxCacheSize:     defaultMaxCacheSize,
		MaxCacheAge:      time.Hour,
		Timeout:          defaultTimeout,
		WorkerTimeout:    defaultWorkerTimeout,
		DrainTimeout:     defaultDrainTimeout,
		AdaptiveTimeouts: true, // Enable adaptive timeouts by default
	}
}

// SplitterConfig contains configuration options
type SplitterConfig struct {
	MinSamplesSplit int
	MinSamplesLeaf  int
	Criterion       ImpurityCriterion
	MaxWorkers      int
	CacheSize       int
	Limits          ResourceLimits
	EnableLogging   bool
}

// StringToImpurityCriterion converts a string criterion to ImpurityCriterion
func StringToImpurityCriterion(criterion string) ImpurityCriterion {
	switch criterion {
	case "gini":
		return GiniImpurity
	case "mse":
		return MSEImpurity
	case "entropy":
		return EntropyImpurity
	default:
		return EntropyImpurity
	}
}

func DefaultSplitterConfig() SplitterConfig {
	var cliConfig cli.Config

	criterion := StringToImpurityCriterion(cliConfig.Criterion)
	minSampleLeaf := max(cliConfig.MinSamplesLeaf, 1)
	minSampleSplit := max(cliConfig.MinSamplesSplit, 2)

	return SplitterConfig{
		MinSamplesSplit: minSampleSplit,
		MinSamplesLeaf:  minSampleLeaf,
		Criterion:       criterion,
		MaxWorkers:      runtime.NumCPU(),
		CacheSize:       1000,
		Limits:          DefaultResourceLimits(),
		EnableLogging:   false,
	}
}

// Functional options
type SplitterOption func(*SplitterConfig) error

func WithMinSamplesSplit(samples int) SplitterOption {
	return func(config *SplitterConfig) error {
		if samples < 2 {
			return &ValidationError{Field: "MinSamplesSplit", Value: samples, Reason: "must be >= 2"}
		}
		config.MinSamplesSplit = samples
		return nil
	}
}

func WithMinSamplesLeaf(samples int) SplitterOption {
	return func(config *SplitterConfig) error {
		if samples < 1 {
			return &ValidationError{Field: "MinSamplesLeaf", Value: samples, Reason: "must be >= 1"}
		}
		config.MinSamplesLeaf = samples
		return nil
	}
}

func WithImpurityCriterion(criterion ImpurityCriterion) SplitterOption {
	return func(config *SplitterConfig) error {
		if criterion < EntropyImpurity || criterion > MSEImpurity {
			return &ValidationError{Field: "Criterion", Value: criterion, Reason: "invalid impurity criterion"}
		}
		config.Criterion = criterion
		return nil
	}
}

func WithMaxWorkers(workers int) SplitterOption {
	return func(config *SplitterConfig) error {
		if workers < 1 {
			workers = runtime.NumCPU()
		}
		if workers > 64 {
			workers = 64
		}
		config.MaxWorkers = workers
		return nil
	}
}

func WithCacheSize(size int) SplitterOption {
	return func(config *SplitterConfig) error {
		if size < 0 {
			return &ValidationError{Field: "CacheSize", Value: size, Reason: "must be >= 0"}
		}
		config.CacheSize = size
		return nil
	}
}

func WithResourceLimits(limits ResourceLimits) SplitterOption {
	return func(config *SplitterConfig) error {
		if limits.MaxFeatures <= 0 || limits.MaxDatasetSize <= 0 {
			return &ValidationError{Field: "ResourceLimits", Reason: "limits must be positive"}
		}
		config.Limits = limits
		return nil
	}
}

func WithLogging(enabled bool) SplitterOption {
	return func(config *SplitterConfig) error {
		config.EnableLogging = enabled
		return nil
	}
}

func WithAdaptiveTimeouts(enabled bool) SplitterOption {
	return func(config *SplitterConfig) error {
		config.Limits.AdaptiveTimeouts = enabled
		return nil
	}
}

// CalculateAdaptiveTimeout calculates an appropriate timeout based on context and configuration
func (limits *ResourceLimits) CalculateAdaptiveTimeout(ctx context.Context, baseTimeout time.Duration) time.Duration {
	if !limits.AdaptiveTimeouts {
		return baseTimeout
	}

	// If context has a deadline, use a percentage of remaining time
	if deadline, ok := ctx.Deadline(); ok {
		remaining := time.Until(deadline)
		if remaining > 0 {
			adaptive := time.Duration(float64(remaining) * workerTimeoutRatio)

			// Apply bounds checking
			if adaptive < minWorkerTimeout {
				adaptive = minWorkerTimeout
			}
			if adaptive > maxWorkerTimeout {
				adaptive = maxWorkerTimeout
			}

			// Don't exceed the base timeout
			if adaptive > baseTimeout {
				adaptive = baseTimeout
			}

			return adaptive
		}
	}

	// Fallback to base timeout
	return baseTimeout
}

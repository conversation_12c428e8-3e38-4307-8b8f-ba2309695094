package training

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// C45Splitter implements the C4.5 decision tree splitting algorithm with
// performance optimizations including parallel feature evaluation.
//
// The splitter uses gain ratio (information gain / split information) as
// the splitting criterion to avoid bias toward features with many values.
//
// Thread Safety: C45Splitter is safe for concurrent use. Object pools are thread-safe.
type C45Splitter[T comparable] struct {
	config         SplitterConfig // Configuration options
	targetDistPool sync.Pool      // Pool for target distribution maps
	indicesPool    sync.Pool      // Pool for index slices
}

// NewC45Splitter creates a new C4.5 splitter with the given options
func NewC45Splitter[T comparable](options ...SplitterOption) (*C45Splitter[T], error) {
	config := DefaultSplitterConfig()

	// Apply all functional options
	for _, option := range options {
		if err := option(&config); err != nil {
			return nil, &SplitError{Op: "configure", Err: err}
		}
	}

	// Validate configuration consistency
	if config.MinSamplesLeaf > config.MinSamplesSplit {
		return nil, &SplitError{
			Op: "configure",
			Err: fmt.Errorf("MinSamplesLeaf (%d) cannot be greater than MinSamplesSplit (%d)",
				config.MinSamplesLeaf, config.MinSamplesSplit),
		}
	}

	splitter := &C45Splitter[T]{
		config: config,
	}

	// Initialize object pools with proper cleanup
	splitter.targetDistPool = sync.Pool{
		New: func() interface{} {
			return make(map[T]int)
		},
	}

	splitter.indicesPool = sync.Pool{
		New: func() interface{} {
			return make([]int, 0, 64)
		},
	}

	return splitter, nil
}

// FindBestSplit evaluates all provided features and returns the split that
// maximizes the gain ratio. Returns nil if no beneficial split is found.
//
// The algorithm:
//  1. Validates inputs and checks minimum sample requirements
//  2. Calculates base impurity of the current dataset
//  3. Evaluates each feature (parallel or sequential based on configuration)
//  4. Returns the split with the highest gain ratio
//
// For numeric features, the algorithm examines all possible binary splits
// by sorting values and testing thresholds between adjacent unique values.
//
// For categorical features, the algorithm creates a multi-way split with
// one branch per category value.
//
// Context cancellation is respected throughout the process.
func (c *C45Splitter[T]) FindBestSplit(ctx context.Context, dataset Dataset[T], features []*models.Feature) (*SplitResult[T], error) {
	if c.config.EnableLogging {
		logger.Debug(fmt.Sprintf("Starting split evaluation: %d samples, %d features", dataset.GetSize(), len(features)))
	}

	// Apply timeout if none specified
	if deadline, ok := ctx.Deadline(); !ok || time.Until(deadline) > c.config.Limits.Timeout {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, c.config.Limits.Timeout)
		defer cancel()
	}

	if err := c.validateInputs(dataset, features); err != nil {
		if c.config.EnableLogging {
			logger.Error(fmt.Sprintf("Input validation failed: %v", err))
		}
		return nil, err
	}

	if err := c.prepareForSplitting(ctx); err != nil {
		if c.config.EnableLogging {
			logger.Error(fmt.Sprintf("Preparation failed: %v", err))
		}
		return nil, err
	}

	baseImpurity, err := c.CalculateImpurity(dataset)
	if err != nil {
		if c.config.EnableLogging {
			logger.Error(fmt.Sprintf("Failed to calculate base impurity: %v", err))
		}
		return nil, &SplitError{Op: "calculate_base_impurity", Err: err}
	}

	if c.config.EnableLogging {
		logger.Debug(fmt.Sprintf("Base impurity calculated: %.6f", baseImpurity))
	}

	// If the dataset is pure (impurity = 0), no split is beneficial.
	// Returning (nil, nil) signals to the caller that this dataset should
	// become a leaf node since all samples have the same target value.
	if baseImpurity == 0 {
		if c.config.EnableLogging {
			logger.Debug("Dataset is pure (impurity = 0), creating leaf node")
		}
		return nil, nil
	}

	result, err := c.executeSplitting(ctx, dataset, features, baseImpurity)
	if c.config.EnableLogging {
		if err != nil {
			logger.Error(fmt.Sprintf("Split execution failed: %v", err))
		} else if result != nil {
			logger.Debug(fmt.Sprintf("Best split found: feature=%s, gain_ratio=%.6f", result.Feature.Name, result.GainRatio))
		} else {
			logger.Debug("No beneficial split found")
		}
	}
	return result, err
}

// validateInputs performs comprehensive input validation
func (c *C45Splitter[T]) validateInputs(dataset Dataset[T], features []*models.Feature) error {
	var errors MultiError

	// Basic nil checks
	if dataset == nil {
		errors.Add(&ValidationError{Field: "dataset", Reason: "cannot be nil"})
	} else {
		// Dataset-specific validation
		if dataset.GetSize() == 0 {
			errors.Add(&ValidationError{Field: "dataset", Reason: "cannot be empty"})
		} else if dataset.GetSize() > c.config.Limits.MaxDatasetSize {
			errors.Add(&ValidationError{
				Field:  "dataset",
				Value:  dataset.GetSize(),
				Reason: fmt.Sprintf("size exceeds limit of %d", c.config.Limits.MaxDatasetSize),
			})
		} else if dataset.GetSize() < c.config.MinSamplesSplit {
			errors.Add(&ValidationError{
				Field:  "dataset",
				Value:  dataset.GetSize(),
				Reason: fmt.Sprintf("size (%d) < min samples split (%d)", dataset.GetSize(), c.config.MinSamplesSplit),
			})
		}

		// Validate indices
		indices := dataset.GetIndices()
		if len(indices) == 0 {
			errors.Add(&ValidationError{Field: "dataset.indices", Reason: "no valid indices"})
		} else {
			for i, idx := range indices {
				if idx < 0 {
					errors.Add(&ValidationError{
						Field:  "dataset.indices",
						Value:  idx,
						Reason: fmt.Sprintf("negative index at position %d", i),
					})
				}
			}
		}
	}

	// Features validation
	if len(features) == 0 {
		errors.Add(&ValidationError{Field: "features", Reason: "cannot be empty"})
	} else if len(features) > c.config.Limits.MaxFeatures {
		errors.Add(&ValidationError{
			Field:  "features",
			Value:  len(features),
			Reason: fmt.Sprintf("count exceeds limit of %d", c.config.Limits.MaxFeatures),
		})
	} else {
		// Validate each feature
		featureNames := make(map[string]bool)
		for i, feature := range features {
			if feature == nil {
				errors.Add(&ValidationError{
					Field:  "features",
					Value:  i,
					Reason: fmt.Sprintf("feature at index %d is nil", i),
				})
				continue
			}

			// Check for duplicate names
			if featureNames[feature.Name] {
				errors.Add(&ValidationError{
					Field:  "features",
					Value:  feature.Name,
					Reason: "duplicate feature name",
				})
			}
			featureNames[feature.Name] = true

			// Validate feature name
			if feature.Name == "" {
				errors.Add(&ValidationError{
					Field:  "features",
					Value:  i,
					Reason: fmt.Sprintf("feature at index %d has empty name", i),
				})
			}

			// Validate feature type
			if !c.isValidFeatureType(feature.Type) {
				errors.Add(&ValidationError{
					Field:  "features",
					Value:  feature.Type,
					Reason: fmt.Sprintf("unsupported feature type for feature '%s'", feature.Name),
				})
			}
		}
	}

	if errors.HasErrors() {
		return &SplitError{Op: "validate", Err: &errors}
	}
	return nil
}

// isValidFeatureType checks if a feature type is supported
func (c *C45Splitter[T]) isValidFeatureType(featureType models.FeatureType) bool {
	switch featureType {
	case models.CategoricalFeature, models.NumericFeature, models.DateTimeFeature:
		return true
	default:
		return false
	}
}

// prepareForSplitting handles pre-processing steps
func (c *C45Splitter[T]) prepareForSplitting(ctx context.Context) error {

	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	// No caching preparation needed
	return nil
}

// executeSplitting performs the actual splitting logic
func (c *C45Splitter[T]) executeSplitting(ctx context.Context, dataset Dataset[T], features []*models.Feature, baseImpurity float64) (*SplitResult[T], error) {
	if len(features) >= minFeaturesForParallel {
		return c.findBestSplitParallel(ctx, dataset, features, baseImpurity)
	}
	return c.findBestSplitSequential(ctx, dataset, features, baseImpurity)
}

// calculateOptimalWorkers determines the optimal number of workers
func (c *C45Splitter[T]) calculateOptimalWorkers(numFeatures int) int {
	maxWorkers := min(c.config.MaxWorkers, numFeatures)

	// For small numbers of features, sequential might be faster
	if numFeatures <= minFeaturesForParallel {
		return 1
	}

	return maxWorkers
}

// findBestSplitParallel uses parallel processing to evaluate features
func (c *C45Splitter[T]) findBestSplitParallel(ctx context.Context, dataset Dataset[T], features []*models.Feature, baseImpurity float64) (*SplitResult[T], error) {
	numWorkers := c.calculateOptimalWorkers(len(features))
	numFeatures := len(features)

	jobs := make(chan *models.Feature, numFeatures)
	results := make(chan SplitWorkerResult[T], numFeatures)

	// Start workers
	var wg sync.WaitGroup
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for feature := range jobs {
				select {
				case <-ctx.Done():
					// Use adaptive timeout to prevent blocking
					adaptiveTimeout := c.config.Limits.CalculateAdaptiveTimeout(ctx, c.config.Limits.WorkerTimeout)
					select {
					case results <- SplitWorkerResult[T]{Err: ctx.Err()}:
					case <-time.After(adaptiveTimeout):
					}
					return
				default:
				}

				split, err := c.safeEvaluateFeature(dataset, feature, baseImpurity)

				select {
				case results <- SplitWorkerResult[T]{Split: split, Err: err}:
				case <-ctx.Done():
					// Use adaptive timeout for draining results
					adaptiveTimeout := c.config.Limits.CalculateAdaptiveTimeout(ctx, c.config.Limits.DrainTimeout)
					select {
					case results <- SplitWorkerResult[T]{Err: ctx.Err()}:
					case <-time.After(adaptiveTimeout):
					}
					return
				}
			}
		}()
	}

	go func() {
		defer close(jobs)
		for _, feature := range features {
			select {
			case jobs <- feature:
			case <-ctx.Done():
				return
			}
		}
	}()

	go func() {
		wg.Wait()
		close(results)
	}()

	var bestSplit *SplitResult[T]
	var errors MultiError
	expectedResults := numFeatures
	collectedResults := 0

	timeout := time.After(c.config.Limits.Timeout)

	for collectedResults < expectedResults {
		select {
		case result, ok := <-results:
			if !ok {
				if c.config.EnableLogging {
					logger.Warn(fmt.Sprintf("Results channel closed early. Expected %d results, got %d", expectedResults, collectedResults))
				}
				break
			}

			collectedResults++

			if result.Err != nil {
				if c.config.EnableLogging {
					logger.Error(fmt.Sprintf("Feature evaluation error: %v", result.Err))
				}
				errors.Add(result.Err)
				continue
			}

			if result.Split != nil && (bestSplit == nil || result.Split.GainRatio > bestSplit.GainRatio) {
				bestSplit = result.Split
			}

		case <-ctx.Done():
			c.drainRemainingResults(results, &errors, &bestSplit, timeout)
			return bestSplit, ctx.Err()

		case <-timeout:
			// Overall timeout - collect what we have
			if c.config.EnableLogging {
				logger.Warn(fmt.Sprintf("Timeout waiting for results. Expected %d, got %d", expectedResults, collectedResults))
			}
			// Calculate drain timeout as 10% of main timeout with bounds checking.
			// This gives workers a reasonable amount of time to finish while preventing
			// indefinite blocking when the main timeout has already been exceeded.
			drainTimeout := time.Duration(float64(c.config.Limits.Timeout) * 0.1)
			if drainTimeout < c.config.Limits.WorkerTimeout {
				drainTimeout = c.config.Limits.WorkerTimeout
			}
			if drainTimeout > 2*time.Second {
				drainTimeout = 2 * time.Second
			}
			c.drainRemainingResults(results, &errors, &bestSplit, time.After(drainTimeout))

		}
	}

	// Handle final error state
	if errors.HasErrors() && bestSplit == nil {
		return nil, &SplitError{Op: "parallel_split", Err: &errors}
	}

	return bestSplit, nil
}

// Helper method to safely evaluate features with panic recovery
func (c *C45Splitter[T]) safeEvaluateFeature(dataset Dataset[T], feature *models.Feature, baseImpurity float64) (split *SplitResult[T], err error) {
	defer func() {
		if r := recover(); r != nil {
			featureName := "unknown"
			if feature != nil {
				featureName = feature.Name
			}
			err = fmt.Errorf("panic during feature evaluation for feature '%s': %v", featureName, r)
			split = nil
		}
	}()

	return c.evaluateFeature(dataset, feature, baseImpurity)
}

// Helper method to drain remaining results when exiting early
func (c *C45Splitter[T]) drainRemainingResults(results <-chan SplitWorkerResult[T], errors *MultiError, bestSplit **SplitResult[T], timeout <-chan time.Time) {
	for {
		select {
		case result, ok := <-results:
			if !ok {
				return
			}

			if result.Err != nil {
				errors.Add(result.Err)
			} else if result.Split != nil && (*bestSplit == nil || result.Split.GainRatio > (*bestSplit).GainRatio) {
				*bestSplit = result.Split
			}

		case <-timeout:
			return
		}
	}
}

// evaluateFeature evaluates a single feature for splitting using simplified type system
func (c *C45Splitter[T]) evaluateFeature(dataset Dataset[T], feature *models.Feature, baseImpurity float64) (*SplitResult[T], error) {
	switch feature.Type {
	case models.CategoricalFeature:
		return c.evaluateCategoricalSplit(dataset, feature, baseImpurity)
	case models.NumericFeature:
		return c.evaluateNumericFloatSplit(dataset, feature, baseImpurity)
	case models.DateTimeFeature:
		// Treat datetime as integer numeric features (pre-converted during dataset loading)
		return c.evaluateNumericIntSplit(dataset, feature, baseImpurity)
	default:
		return nil, &SplitError{
			Op:      "evaluate_feature",
			Feature: feature.Name,
			Err:     fmt.Errorf("unsupported feature type: %v", feature.Type),
		}
	}
}

// findBestSplitSequential processes features sequentially
func (c *C45Splitter[T]) findBestSplitSequential(ctx context.Context, dataset Dataset[T], features []*models.Feature, baseImpurity float64) (*SplitResult[T], error) {
	var bestSplit *SplitResult[T]
	var errors MultiError

	for _, feature := range features {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		split, err := c.evaluateFeature(dataset, feature, baseImpurity)
		if err != nil {
			if c.config.EnableLogging {
				logger.Error(fmt.Sprintf("Error evaluating feature %s: %v", feature.Name, err))
			}
			errors.Add(fmt.Errorf("feature %s: %w", feature.Name, err))
			continue
		}

		if split != nil && (bestSplit == nil || split.GainRatio > bestSplit.GainRatio) {
			bestSplit = split
		}
	}

	// If we have some results but also errors, log the errors but continue
	if errors.HasErrors() && bestSplit == nil {
		return nil, &SplitError{Op: "sequential_split", Err: &errors}
	}

	return bestSplit, nil
}

// Pool management functions with proper bounds checking
func (c *C45Splitter[T]) getTargetDistFromPool() map[T]int {
	return c.targetDistPool.Get().(map[T]int)
}

func (c *C45Splitter[T]) returnTargetDistToPool(dist map[T]int) {
	if len(dist) <= maxPooledMapSize {
		for k := range dist {
			delete(dist, k)
		}
		c.targetDistPool.Put(dist)
	}
}

// getFeatureValue retrieves a feature value directly from the dataset
func (c *C45Splitter[T]) getFeatureValue(dataset Dataset[T], idx int, feature *models.Feature) (interface{}, error) {
	// Add nil checks FIRST
	if dataset == nil {
		return nil, fmt.Errorf("dataset cannot be nil")
	}
	if feature == nil {
		return nil, fmt.Errorf("feature cannot be nil")
	}
	if feature.Name == "" {
		return nil, fmt.Errorf("feature name cannot be empty")
	}
	if idx < 0 {
		return nil, fmt.Errorf("index cannot be negative: %d", idx)
	}
	if c == nil {
		return nil, fmt.Errorf("splitter cannot be nil")
	}

	return dataset.GetFeatureValue(idx, feature)
}

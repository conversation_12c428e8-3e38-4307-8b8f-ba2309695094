package prediction

import (
	"testing"

	"github.com/berrijam/mulberri/internals/utils"
	"github.com/berrijam/mulberri/pkg/models"
)

// Helper function to create a test model with features
func createTestModel() *models.DecisionTree {
	model := &models.DecisionTree{
		Features: make(map[string]*models.Feature),
	}

	// Add age feature (numeric with range)
	ageFeature, _ := models.NewFeature("age", models.NumericFeature, 0)
	ageFeature.SetRange(0, 100)
	model.Features["age"] = ageFeature

	// Add income feature (numeric without range)
	incomeFeature, _ := models.NewFeature("income", models.NumericFeature, 1)
	model.Features["income"] = incomeFeature

	// Add category feature (categorical with valid values)
	categoryFeature, _ := models.NewFeature("category", models.CategoricalFeature, 2)
	categoryFeature.AddCategoricalValue("A")
	categoryFeature.AddCategoricalValue("B")
	categoryFeature.AddCategoricalValue("C")
	model.Features["category"] = categoryFeature

	// Add education feature (categorical without constraints)
	educationFeature, _ := models.NewFeature("education", models.CategoricalFeature, 3)
	model.Features["education"] = educationFeature

	return model
}

// Helper function to create test records
func createTestRecords() []utils.PredictionRecord {
	return []utils.PredictionRecord{
		{
			Features: map[string]interface{}{
				"age":       25.0,
				"income":    50000.0,
				"category":  "A",
				"education": "Bachelor",
			},
			RowIndex: 0,
		},
		{
			Features: map[string]interface{}{
				"age":       30.0,
				"income":    75000.0,
				"category":  "B",
				"education": "Master",
			},
			RowIndex: 1,
		},
	}
}

func TestValidateRecords_ValidData(t *testing.T) {
	model := createTestModel()
	records := createTestRecords()

	result, err := ValidatePredictionRecords(records, model)
	if err != nil {
		t.Errorf("ValidatePredictionRecords returned an error: %v", err)
	}

	if len(result.ValidRecords) != 2 {
		t.Errorf("Expected 2 valid records, got %d", len(result.ValidRecords))
	}

	if len(result.InvalidRecords) != 0 {
		t.Errorf("Expected 0 invalid records, got %d", len(result.InvalidRecords))
	}

	if len(result.Errors) != 0 {
		t.Errorf("Expected 0 errors, got %d", len(result.Errors))
	}
}

func TestValidateRecords_MissingRequiredFeatures(t *testing.T) {
	model := createTestModel()
	records := []utils.PredictionRecord{
		{
			Features: map[string]interface{}{
				"age":    25.0,
				"income": 50000.0,
				// Missing "category" and "education"
			},
			RowIndex: 0,
		},
	}

	result, err := ValidatePredictionRecords(records, model)
	if err != nil {
		t.Errorf("ValidatePredictionRecords returned an error: %v", err)
	}

	if len(result.ValidRecords) != 0 {
		t.Errorf("Expected 0 valid records, got %d", len(result.ValidRecords))
	}

	if len(result.InvalidRecords) != 1 {
		t.Errorf("Expected 1 invalid record, got %d", len(result.InvalidRecords))
	}

	if len(result.Errors) != 2 {
		t.Errorf("Expected 2 errors (missing category and education), got %d", len(result.Errors))
	}

	// Check error details
	for _, err := range result.Errors {
		if err.Field != "category" && err.Field != "education" {
			t.Errorf("Unexpected error field: %s", err.Field)
		}
		if err.Reason != "required feature not found in record" {
			t.Errorf("Unexpected error reason: %s", err.Reason)
		}
	}
}

func TestValidateRecords_WrongDataTypes(t *testing.T) {
	model := createTestModel()
	records := []utils.PredictionRecord{
		{
			Features: map[string]interface{}{
				"age":       "twenty-five", // Should be numeric
				"income":    50000.0,
				"category":  123, // Should be string
				"education": "Bachelor",
			},
			RowIndex: 0,
		},
	}

	result, err := ValidatePredictionRecords(records, model)
	if err != nil {
		t.Errorf("ValidatePredictionRecords returned an error: %v", err)
	}

	if len(result.ValidRecords) != 0 {
		t.Errorf("Expected 0 valid records, got %d", len(result.ValidRecords))
	}

	if len(result.InvalidRecords) != 1 {
		t.Errorf("Expected 1 invalid record, got %d", len(result.InvalidRecords))
	}

	if len(result.Errors) != 2 {
		t.Errorf("Expected 2 errors (wrong types for age and category), got %d", len(result.Errors))
	}
}

func TestValidateRecords_EmptyDataset(t *testing.T) {
	model := createTestModel()
	records := []utils.PredictionRecord{}

	result, err := ValidatePredictionRecords(records, model)
	if err != nil {
		t.Errorf("ValidatePredictionRecords returned an error: %v", err)
	}

	if len(result.ValidRecords) != 0 {
		t.Errorf("Expected 0 valid records, got %d", len(result.ValidRecords))
	}

	if len(result.InvalidRecords) != 0 {
		t.Errorf("Expected 0 invalid records, got %d", len(result.InvalidRecords))
	}

	if len(result.Errors) != 0 {
		t.Errorf("Expected 0 errors, got %d", len(result.Errors))
	}
}

func TestValidateRecords_AllMissingValues(t *testing.T) {
	model := createTestModel()
	records := []utils.PredictionRecord{
		{
			Features: map[string]interface{}{
				"age":       nil,
				"income":    nil,
				"category":  nil,
				"education": nil,
			},
			RowIndex: 0,
		},
	}

	result, err := ValidatePredictionRecords(records, model)
	if err != nil {
		t.Errorf("ValidatePredictionRecords returned an error: %v", err)
	}

	// Records with all nil values should be valid (missing data is allowed)
	if len(result.ValidRecords) != 1 {
		t.Errorf("Expected 1 valid record (nil values allowed), got %d", len(result.ValidRecords))
	}

	if len(result.InvalidRecords) != 0 {
		t.Errorf("Expected 0 invalid records, got %d", len(result.InvalidRecords))
	}
}

func TestValidateRecords_NilModel(t *testing.T) {
	records := createTestRecords()

	_, err := ValidatePredictionRecords(records, nil)
	if err == nil {
		t.Errorf("ValidatePredictionRecords should return an error for nil model")
	}

	if validationErr, ok := err.(*ValidationError); ok {
		if validationErr.Field != "model" {
			t.Errorf("Expected validation error for 'model' field, got: %s", validationErr.Field)
		}
	} else {
		t.Errorf("Expected ValidationError, got: %T", err)
	}
}

func TestValidateAndFilter(t *testing.T) {
	model := createTestModel()
	records := []utils.PredictionRecord{
		{
			Features: map[string]interface{}{
				"age":       25.0,
				"income":    50000.0,
				"category":  "A",
				"education": "Bachelor",
			},
			RowIndex: 0,
		},
		{
			Features: map[string]interface{}{
				"age":       150.0,
				"income":    75000.0,
				"category":  "B",
				"education": "Master",
			},
			RowIndex: 1,
		},
	}

	validRecords, err := ValidateAndFilterPredictionRecords(records, model)
	if err != nil {
		t.Errorf("ValidateAndFilterPredictionRecords returned an error: %v", err)
	}

	if len(validRecords) != 2 {
		t.Errorf("Expected 2 valid records, got %d", len(validRecords))
	}

	if validRecords[0].RowIndex != 0 {
		t.Errorf("Expected valid record to have RowIndex 0, got %d", validRecords[0].RowIndex)
	}
}

// Test ValidateAndFilter with nil model to cover error path
func TestValidateAndFilter_NilModel(t *testing.T) {
	records := createTestRecords()

	_, err := ValidateAndFilterPredictionRecords(records, nil)
	if err == nil {
		t.Errorf("ValidateAndFilterPredictionRecords should return an error for nil model")
	}
}

// Test ValidationError.Error() method to improve coverage
func TestValidationError_Error(t *testing.T) {
	// Test error with row index
	errWithRow := &ValidationError{
		Field:  "age",
		Value:  "invalid",
		Reason: "not a number",
		RowIndex: 5,
	}

	expected := "validation failed for field 'age' at row 5 with value 'invalid': not a number"
	if errWithRow.Error() != expected {
		t.Errorf("Expected error message: %s, got: %s", expected, errWithRow.Error())
	}

	// Test error without row index
	errWithoutRow := &ValidationError{
		Field:  "model",
		Value:  "nil",
		Reason: "model cannot be nil",
		RowIndex: -1,
	}

	expected = "validation failed for field 'model' with value 'nil': model cannot be nil"
	if errWithoutRow.Error() != expected {
		t.Errorf("Expected error message: %s, got: %s", expected, errWithoutRow.Error())
	}
}

// Test categorical validation with no constraints (should accept any string)
func TestValidateRecords_CategoricalNoConstraints(t *testing.T) {
	model := &models.DecisionTree{
		Features: make(map[string]*models.Feature),
	}

	// Create a categorical feature with no constraints
	unconstrainedFeature := &models.Feature{
		Name: "unconstrained_category",
		Type: models.CategoricalFeature,
		// No CategoricalValues or Values defined
	}
	model.Features["unconstrained_category"] = unconstrainedFeature

	records := []utils.PredictionRecord{
		{
			Features: map[string]interface{}{
				"unconstrained_category": "any_value", // Should be valid
			},
			RowIndex: 0,
		},
	}

	result, err := ValidatePredictionRecords(records, model)
	if err != nil {
		t.Errorf("ValidatePredictionRecords returned an error: %v", err)
	}

	if len(result.ValidRecords) != 1 {
		t.Errorf("Expected 1 valid record, got %d", len(result.ValidRecords))
	}

	if len(result.InvalidRecords) != 0 {
		t.Errorf("Expected 0 invalid records, got %d", len(result.InvalidRecords))
	}
}

// Test numeric validation with no constraints
func TestValidateRecords_NumericNoConstraints(t *testing.T) {
	model := &models.DecisionTree{
		Features: make(map[string]*models.Feature),
	}

	// Create a numeric feature with no constraints
	unconstrainedNumeric := &models.Feature{
		Name: "unconstrained_numeric",
		Type: models.NumericFeature,
		// No NumericRange, Min, or Max defined
	}
	model.Features["unconstrained_numeric"] = unconstrainedNumeric

	records := []utils.PredictionRecord{
		{
			Features: map[string]interface{}{
				"unconstrained_numeric": 999999.0, // Should be valid
			},
			RowIndex: 0,
		},
	}

	result, err := ValidatePredictionRecords(records, model)
	if err != nil {
		t.Errorf("ValidatePredictionRecords returned an error: %v", err)
	}

	if len(result.ValidRecords) != 1 {
		t.Errorf("Expected 1 valid record, got %d", len(result.ValidRecords))
	}

	if len(result.InvalidRecords) != 0 {
		t.Errorf("Expected 0 invalid records, got %d", len(result.InvalidRecords))
	}
}

// Test ValidateRecords with model having no features
func TestValidateRecords_ModelNoFeatures(t *testing.T) {
	model := &models.DecisionTree{
		Features: make(map[string]*models.Feature), // Empty features map
	}
	records := createTestRecords()

	_, err := ValidatePredictionRecords(records, model)
	if err == nil {
		t.Errorf("ValidatePredictionRecords should return an error for model with no features")
	}

	if validationErr, ok := err.(*ValidationError); ok {
		if validationErr.Field != "model_features" {
			t.Errorf("Expected validation error for 'model_features' field, got: %s", validationErr.Field)
		}
	} else {
		t.Errorf("Expected ValidationError, got: %T", err)
	}
}

// Test categorical validation with whitespace trimming
func TestValidateRecords_CategoricalWhitespaceTrimming(t *testing.T) {
	model := createTestModel()
	records := []utils.PredictionRecord{
		{
			Features: map[string]interface{}{
				"age":       25.0,
				"income":    50000.0,
				"category":  "  A  ", // Valid category with whitespace
				"education": "Bachelor",
			},
			RowIndex: 0,
		},
	}

	result, err := ValidatePredictionRecords(records, model)
	if err != nil {
		t.Errorf("ValidatePredictionRecords returned an error: %v", err)
	}

	if len(result.ValidRecords) != 1 {
		t.Errorf("Expected 1 valid record (whitespace should be trimmed), got %d", len(result.ValidRecords))
	}

	if len(result.InvalidRecords) != 0 {
		t.Errorf("Expected 0 invalid records, got %d", len(result.InvalidRecords))
	}
}

func TestTargetColumnRemoval(t *testing.T) {
	// Setup test model
	model := &models.DecisionTree{
		TargetColumn: "target_class",
		Features: map[string]*models.Feature{
			"age": {
				Name: "age",
				Type: models.NumericFeature,
			},
			"category": {
				Name: "category",
				Type: models.CategoricalFeature,
			},
		},
	}

	tests := []struct {
		name                string
		record              utils.PredictionRecord
		expectTargetRemoved bool
		expectedErrorCount  int
	}{
		{
			name: "record with target column should have it removed",
			record: utils.PredictionRecord{
				Features: map[string]interface{}{
					"age":          25.0,
					"category":     "A",
					"target_class": "positive", // This should be removed
				},
			},
			expectTargetRemoved: true,
			expectedErrorCount:  0, // Target removal doesn't generate validation errors
		},
		{
			name: "record without target column should remain unchanged",
			record: utils.PredictionRecord{
				Features: map[string]interface{}{
					"age":      30.0,
					"category": "B",
				},
			},
			expectTargetRemoved: false,
			expectedErrorCount:  0,
		},
		{
			name: "record with target column and validation errors",
			record: utils.PredictionRecord{
				Features: map[string]interface{}{
					"age":          "invalid_numeric", // Invalid numeric
					"category":     "C",
					"target_class": "negative", // This should be removed
				},
			},
			expectTargetRemoved: true,
			expectedErrorCount:  1, // Only validation error for age (target removal doesn't count)
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Make a copy of the original record to compare
			originalFeatures := make(map[string]interface{})
			for k, v := range tt.record.Features {
				originalFeatures[k] = v
			}

			// Validate the record
			errors := validateSingleRecord(tt.record, model, 0)

			// Check error count
			if len(errors) != tt.expectedErrorCount {
				t.Errorf("expected %d errors, got %d", tt.expectedErrorCount, len(errors))
			}

			// Check if target column was removed
			_, hasTarget := tt.record.Features["target_class"]
			if tt.expectTargetRemoved {
				if hasTarget {
					t.Error("expected target column to be removed, but it's still present")
				}
			} else {
				// If we didn't expect it to be removed, check if it was originally there
				_, hadTarget := originalFeatures["target_class"]
				if hadTarget && !hasTarget {
					t.Error("target column was removed when it shouldn't have been")
				}
			}

			// Verify that other features remain intact
			for key, value := range originalFeatures {
				if key == "target_class" {
					continue // Skip target column check (handled above)
				}
				if recordValue, exists := tt.record.Features[key]; !exists || recordValue != value {
					t.Errorf("feature %s was modified or removed unexpectedly", key)
				}
			}
		})
	}
}

func TestTargetRemovalWithEmptyTargetColumn(t *testing.T) {
	// Test edge case where target column is empty string
	model := &models.DecisionTree{
		TargetColumn: "", // Empty target column
		Features: map[string]*models.Feature{
			"feature1": {
				Name: "feature1",
				Type: models.NumericFeature,
			},
		},
	}

	record := utils.PredictionRecord{
		Features: map[string]interface{}{
			"feature1": 10.0,
			"":         "should_be_removed", // Empty string key
		},
	}

	// Store original state
	_, hadEmptyKey := record.Features[""]

	errors := validateSingleRecord(record, model, 0)

	// Since the current implementation only logs target removal without generating errors,
	// we should expect no validation errors
	if len(errors) != 0 {
		t.Errorf("expected 0 errors, got %d", len(errors))
	}

	// Empty key should be removed if it was originally there
	if _, hasEmpty := record.Features[""]; hasEmpty && hadEmptyKey {
		t.Error("empty target column was not removed")
	}
}

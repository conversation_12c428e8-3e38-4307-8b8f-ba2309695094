# Prediction Package

A Go package for traversing decision trees and making predictions on input records.

## Features

- Tree traversal with multiple strategies (recursive, iterative, automatic selection)
- Missing value handling with configurable strategies
- Batch processing for multiple records
- Input validation
- Decision path tracking
- Probability calculation from class distributions
- Performance metrics collection

## API Reference

### Core Functions

#### `TraverseTreeSimple(tree, record) -> (*TraversalResult, error)`
Wrapper around `TraverseTree` with default options:
- **MissingValueStrategy**: `UseMajorityClass`
- **IncludePath**: `false`
- **MaxDepth**: `1000` (maximum sequential decisions in path)
- **Strategy**: `AutoStrategy`
- **CollectMetrics**: `false`

#### `TraverseTree(tree, record, options) -> (*TraversalResult, error)`
Traverses a decision tree to make a prediction for an input record.

#### `TraverseTreeWithMetrics(tree, record, options) -> (*TraversalResult, *TraversalMetrics, error)`
Traverses a tree and returns performance metrics alongside the result.

#### `PredictBatch(tree, records, options) -> ([]*TraversalResult, error)`
Processes multiple input records sequentially.

#### `ValidateInputRecord(record, tree) -> error`
Validates input record against tree features before traversal.

#### `ExtractPredictions(results) -> []interface{}`
Extracts prediction values from an array of traversal results.

#### `AnalyzeConfidenceStats(results) -> *ConfidenceStats`
Analyzes confidence values from traversal results and computes statistical measures.

### Types

#### `TraversalResult`
```go
type TraversalResult struct {
    Prediction        interface{}             // Value from leaf node
    ClassDistribution map[interface{}]int     // Class counts from leaf node
    Confidence        float64                 // Confidence value from leaf node
    Path              []string                // Decision path (if tracking enabled)
    RulePath             string                  // Decision rule in conjunction format
    LeafNode          *models.TreeNode        // Reference to leaf node
    Probabilities     map[interface{}]float64 // Normalized class distribution
}
```

#### `TraversalOptions`
```go
type TraversalOptions struct {
    MissingValueStrategy MissingValueStrategy
    IncludePath          bool
    MaxDepth             int
    Strategy             TraversalStrategy
    CollectMetrics       bool
}
```

**Configuration Options:**

| Option                 | Type                   | Description                                              | Values                                                      |
| ---------------------- | ---------------------- | -------------------------------------------------------- | ----------------------------------------------------------- |
| `MissingValueStrategy` | `MissingValueStrategy` | How to handle missing values                             | `UseMajorityClass`, `UseDefaultPrediction`, `FailOnMissing` |
| `IncludePath`          | `bool`                 | Track decision path                                      | `true`, `false`                                             |
| `MaxDepth`             | `int`                  | Maximum number of sequential decisions in traversal path | Any positive integer                                        |
| `Strategy`             | `TraversalStrategy`    | Traversal method                                         | `RecursiveTraversal`, `IterativeTraversal`, `AutoStrategy`  |
| `CollectMetrics`       | `bool`                 | Collect performance data                                 | `true`, `false`                                             |

#### `TraversalMetrics`
```go
type TraversalMetrics struct {
    NodesVisited    int
    MaxDepthReached int
    TraversalTime   time.Duration
    Strategy        string
}
```

#### `ConfidenceStats`
```go
type ConfidenceStats struct {
    Mean      float64
    Min       float64
    Max       float64
    StdDev    float64
    LowCount  int     // Count < 0.5
    HighCount int     // Count >= 0.8
}
```

## Traversal Strategies

### `RecursiveTraversal`
Uses recursive function calls to traverse the tree.

### `IterativeTraversal`
Uses an explicit stack to traverse the tree.

### `AutoStrategy`
Compares estimated tree depth against `RECURSIVE_THRESHOLD` constant and selects recursive or iterative traversal.

## Missing Value Strategies

### `UseMajorityClass`
Routes to the child node with the most training samples.

### `UseDefaultPrediction`
Returns the majority class from the current node.

### `FailOnMissing`
Returns an error when missing values are encountered.

### What MaxDepth Represents

`MaxDepth` controls the maximum number of sequential decisions allowed during tree traversal. This is a safety mechanism to prevent infinite loops in malformed trees.

**Important:** MaxDepth represents the depth of the decision path, not the number of features in your dataset. A tree can reuse the same feature multiple times at different depths. For example:
- Depth 1: `age > 30`
- Depth 2: `age > 50` (same feature, different threshold)
- Depth 3: `income > 40000` (different feature)

The default value of 1000 provides a generous safety limit while preventing runaway traversals.

### Calculations Performed
- **Probability normalization**: Converts class counts to probabilities
- **Tree depth estimation**: Calculates tree depth with caching
- **Type conversion**: Converts various numeric types to float64
- **Strategy selection**: Chooses traversal method based on tree depth

### Values Retrieved (Not Calculated)
- **Prediction**: Retrieved from leaf node
- **Confidence**: Retrieved from leaf node
- **Class Distribution**: Retrieved from leaf node

### Processing
- **Path tracking**: Collects decision steps during traversal
- **Input validation**: Checks record format against tree features
- **Error handling**: Structured error reporting
- **Batch processing**: Sequential processing of multiple records

## Usage Examples

### Basic Usage
```go
package main

import (
    "fmt"
    "log"
    "github.com/berrijam/mulberri/pkg/prediction"
    "github.com/berrijam/mulberri/pkg/models"
)

func main() {
    // Assume you have a trained decision tree
    tree := &models.DecisionTree{...}
    
    // Input record for prediction
    record := map[string]interface{}{
        "age":      25,
        "income":   50000.0,
        "category": "premium",
    }
    
    // Make prediction with default settings
    result, err := prediction.TraverseTreeSimple(tree, record)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("Prediction: %v\n", result.Prediction)
    fmt.Printf("Confidence: %.2f\n", result.Confidence)
    fmt.Printf("Probabilities: %v\n", result.Probabilities)
}
```

**Example Output:**
```
Prediction: approved
Confidence: 0.85
Probabilities: map[approved:0.85 denied:0.15]
```

### Advanced Configuration
```go
// Configure traversal options
options := prediction.TraversalOptions{
    MissingValueStrategy: prediction.UseMajorityClass,
    IncludePath:          true,
    MaxDepth:             500,
    Strategy:             prediction.AutoStrategy,
    CollectMetrics:       true,
}

result, err := prediction.TraverseTree(tree, record, options)
if err != nil {
    log.Fatal(err)
}

// Access detailed results
fmt.Printf("Prediction: %v\n", result.Prediction)
fmt.Printf("Decision Path: %v\n", result.Path)
fmt.Printf("Decision Rule: %s\n", result.RulePath)
fmt.Printf("Class Distribution: %v\n", result.ClassDistribution)
fmt.Printf("Probabilities: %v\n", result.Probabilities)
```

**Example Output:**
```
Prediction: approved
Decision Path: [age <= 30.000000 income > 40000.000000 category = premium LEAF[approved]]
Decision Rule: [age <= 30.000000 & income > 40000.000000 & category = premium --> LEAF[approved]]
Class Distribution: map[approved:85 denied:15]
Probabilities: map[approved:0.85 denied:0.15]
```

### Performance Monitoring
```go
// Get traversal results with performance metrics
result, metrics, err := prediction.TraverseTreeWithMetrics(tree, record, options)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Strategy Used: %s\n", metrics.Strategy)
fmt.Printf("Nodes Visited: %d\n", metrics.NodesVisited)
fmt.Printf("Max Depth Reached: %d\n", metrics.MaxDepthReached)
fmt.Printf("Traversal Time: %v\n", metrics.TraversalTime)
```

**Example Output:**
```
Strategy Used: recursive
Nodes Visited: 4
Max Depth Reached: 3
Traversal Time: 127.3µs
```

### Batch Processing
```go
records := []map[string]interface{}{
    {"age": 25.0, "sex": "male"},
    {"age": 25.0, "sex": "female"},
    {"age": 35.0, "sex": "male"},
}

results, err := prediction.PredictBatch(tree, records, options)
if err != nil {
    log.Fatal(err)
}

for i, result := range results {
    fmt.Printf("Record %d: %v (confidence: %.2f)\n", 
        i+1, result.Prediction, result.Confidence)
}
```

**Example Output:**
```
Record 1: denied (confidence: 0.83)
Record 2: approved (confidence: 0.83)
Record 3: approved (confidence: 0.88)
```

### Input Validation
```go
// Validate input before traversal
if err := prediction.ValidateInputRecord(record, tree); err != nil {
    log.Printf("Invalid input: %v", err)
    return
}

result, err := prediction.TraverseTree(tree, record, options)
```

### Decision Path Tracking
```go
options := prediction.TraversalOptions{
    IncludePath: true,
}

result, err := prediction.TraverseTree(tree, record, options)
if err != nil {
    log.Fatal(err)
}

fmt.Println("Decision path:")
for i, step := range result.Path {
    fmt.Printf("%d. %s\n", i+1, step)
}

fmt.Printf("Decision rule: %s\n", result.RulePath)
```

**Example Output:**
```
Decision path:
1. age <= 30.000000
2. income > 40000.000000
3. category = premium
4. LEAF[approved]

Decision rule: [age <= 30.000000 & income > 40000.000000 & category = premium --> LEAF[approved]]
```

### Confidence Statistics
```go
results, err := prediction.PredictBatch(tree, records, options)
if err != nil {
    log.Fatal(err)
}

stats := prediction.AnalyzeConfidenceStats(results)
fmt.Printf("Mean confidence: %.2f\n", stats.Mean)
fmt.Printf("Low confidence predictions (< 0.5): %d\n", stats.LowCount)
fmt.Printf("High confidence predictions (>= 0.8): %d\n", stats.HighCount)
fmt.Printf("Standard deviation: %.2f\n", stats.StdDev)
```

**Example Output:**
```
Mean confidence: 0.73
Low confidence predictions (< 0.5): 12
High confidence predictions (>= 0.8): 156
Standard deviation: 0.18
```

### Probability Analysis
```go
result, err := prediction.TraverseTreeSimple(tree, record)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Raw counts: %v\n", result.ClassDistribution)
fmt.Printf("Probabilities: %v\n", result.Probabilities)
```

**Example Output:**
```
Raw counts: map[approved:70 denied:30]
Probabilities: map[approved:0.7 denied:0.3]
```

### Extracting Predictions
```go
results, err := prediction.PredictBatch(tree, records, options)
if err != nil {
    log.Fatal(err)
}

predictions := prediction.ExtractPredictions(results)
fmt.Printf("All predictions: %v\n", predictions)
```

**Example Output:**
```
All predictions: [denied approved approved]
```

## Feature Support

### Numeric Features
Supports: `int`, `int32`, `int64`, `uint`, `uint32`, `uint64`, `float32`, `float64`, numeric strings

### Categorical Features
Supports: any value convertible to string

### Date Features
Treated as numeric values

## Error Handling

Returns structured errors with operation, field, value, and reason information.

## Dependencies

- `github.com/berrijam/mulberri/pkg/models`: Tree structures and error types
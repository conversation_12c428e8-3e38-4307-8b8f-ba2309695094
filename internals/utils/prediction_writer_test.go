package utils

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Test data generators
func generateTestResults(count int) []*PredictionResult {
	results := make([]*PredictionResult, count)

	for i := 0; i < count; i++ {
		results[i] = &PredictionResult{
			Index:      i,
			Prediction: fmt.Sprintf("class_%d", i%3),
			Confidence: float64(i%10) / 10.0,
			Probabilities: map[interface{}]float64{
				"class_0": float64(i%10) / 10.0,
				"class_1": float64((i+1)%10) / 10.0,
				"class_2": float64((i+2)%10) / 10.0,
			},
			RulePath:     fmt.Sprintf("feature_%d > %d", i%3, i%10),
			DecisionPath: []string{fmt.Sprintf("step_%d", i), fmt.Sprintf("step_%d", i+1)},
			OriginalFeatures: map[string]interface{}{
				"feature_a": fmt.Sprintf("value_%d", i),
				"feature_b": float64(i) * 1.5,
				"feature_c": i%2 == 0,
			},
			ProcessingTime: time.Duration(i) * time.Millisecond,
		}

		// Add error to some records
		if i%7 == 0 && i > 0 {
			results[i].Error = fmt.Sprintf("error_%d", i)
		}
	}

	return results
}

func generateSpecialCharResult() *PredictionResult {
	return &PredictionResult{
		Index:      0,
		Prediction: "class,with\"special\nchars",
		Confidence: 0.85,
		Probabilities: map[interface{}]float64{
			"class,with\"special\nchars": 0.85,
			"normal_class":               0.15,
		},
		RulePath:     "feature_name = \"value,with,commas\"",
		DecisionPath: []string{"step with spaces", "step\"with\"quotes"},
		OriginalFeatures: map[string]interface{}{
			"feature_with_comma":    "value,with,commas",
			"feature_with_quotes":   "value\"with\"quotes",
			"feature_with_newlines": "value\nwith\nnewlines",
		},
		ProcessingTime: time.Millisecond * 100,
	}
}

func createTempDir(t *testing.T) string {
	tempDir, err := os.MkdirTemp("", "output_test")
	require.NoError(t, err)
	return tempDir
}

// Test basic CSV output
func TestWriteCSV(t *testing.T) {
	tempDir := createTempDir(t)
	defer os.RemoveAll(tempDir)

	outputPath := filepath.Join(tempDir, "test.csv")
	results := generateTestResults(3)

	config := OutputConfig{
		IncludeIndex:      true,
		IncludePrediction: true,
		IncludeConfidence: true,
	}

	err := WritePredictionCSV(results, outputPath, config)
	require.NoError(t, err)

	// Verify file exists
	_, err = os.Stat(outputPath)
	require.NoError(t, err)

	// Read and verify content
	content, err := os.ReadFile(outputPath)
	require.NoError(t, err)

	lines := strings.Split(string(content), "\n")
	assert.Equal(t, 5, len(lines)) // header + 3 records + empty line

	// Verify header
	assert.Equal(t, "index,prediction,confidence", lines[0])

	// Verify first record
	assert.Equal(t, "0,class_0,0.000000", lines[1])
	assert.Equal(t, "1,class_1,0.100000", lines[2])
	assert.Equal(t, "2,class_2,0.200000", lines[3])
}

// Test TSV output
func TestWriteTSV(t *testing.T) {
	tempDir := createTempDir(t)
	defer os.RemoveAll(tempDir)

	outputPath := filepath.Join(tempDir, "test.tsv")
	results := generateTestResults(2)

	config := OutputConfig{
		IncludeIndex:      true,
		IncludePrediction: true,
	}

	err := WritePredictionTSV(results, outputPath, config)
	require.NoError(t, err)

	// Read and verify content
	content, err := os.ReadFile(outputPath)
	require.NoError(t, err)

	lines := strings.Split(string(content), "\n")
	assert.Equal(t, 4, len(lines)) // header + 2 records + empty line

	// Verify header and data are tab-separated
	assert.Equal(t, "index\tprediction", lines[0])
	assert.Equal(t, "0\tclass_0", lines[1])
	assert.Equal(t, "1\tclass_1", lines[2])
}

// Test JSON output
func TestWriteJSON(t *testing.T) {
	tempDir := createTempDir(t)
	defer os.RemoveAll(tempDir)

	outputPath := filepath.Join(tempDir, "test.json")
	results := generateTestResults(2)

	config := OutputConfig{
		IncludeIndex:      true,
		IncludePrediction: true,
		IncludeConfidence: true,
	}

	err := WritePredictionJSON(results, outputPath, config)
	require.NoError(t, err)

	// Read and verify content
	content, err := os.ReadFile(outputPath)
	require.NoError(t, err)

	var parsedResults []map[string]interface{}
	err = json.Unmarshal(content, &parsedResults)
	require.NoError(t, err)

	assert.Equal(t, 2, len(parsedResults))
	assert.Equal(t, float64(0), parsedResults[0]["index"])
	assert.Equal(t, "class_0", parsedResults[0]["prediction"])
	assert.Equal(t, 0.0, parsedResults[0]["confidence"])
}

// Test column configurations
func TestColumnConfigurations(t *testing.T) {
	tempDir := createTempDir(t)
	defer os.RemoveAll(tempDir)

	tests := []struct {
		name         string
		config       OutputConfig
		expectedCols []string
	}{
		{
			name: "minimal",
			config: OutputConfig{
				IncludeIndex:      true,
				IncludePrediction: true,
			},
			expectedCols: []string{"index", "prediction"},
		},
		{
			name: "with_features",
			config: OutputConfig{
				IncludeIndex:      true,
				IncludePrediction: true,
				IncludeFeatures:   true,
			},
			expectedCols: []string{"index", "prediction", "feature_feature_a", "feature_feature_b", "feature_feature_c"},
		},
		{
			name: "selected_features",
			config: OutputConfig{
				IncludeIndex:      true,
				IncludePrediction: true,
				IncludeFeatures:   true,
				SelectedFeatures:  []string{"feature_a", "feature_c"},
			},
			expectedCols: []string{"index", "prediction", "feature_feature_a", "feature_feature_c"},
		},
		{
			name: "all_columns",
			config: OutputConfig{
				IncludeIndex:          true,
				IncludePrediction:     true,
				IncludeConfidence:     true,
				IncludeProbabilities:  true,
				IncludeRulePath:       true,
				IncludeDecisionPath:   true,
				IncludeFeatures:       true,
				IncludeProcessingTime: true,
				IncludeErrors:         true,
			},
			expectedCols: []string{"index", "prediction", "confidence", "prob_class_0", "prob_class_1", "prob_class_2",
				"rule_path", "decision_path", "feature_feature_a", "feature_feature_b", "feature_feature_c",
				"processing_time_ms", "error"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			outputPath := filepath.Join(tempDir, fmt.Sprintf("test_%s.csv", tt.name))
			results := generateTestResults(1)

			err := WritePredictionCSV(results, outputPath, tt.config)
			require.NoError(t, err)

			// Read and verify headers
			file, err := os.Open(outputPath)
			require.NoError(t, err)
			defer file.Close()

			reader := csv.NewReader(file)
			headers, err := reader.Read()
			require.NoError(t, err)

			assert.Equal(t, tt.expectedCols, headers)
		})
	}
}

// Test special character handling
func TestSpecialCharacterHandling(t *testing.T) {
	tempDir := createTempDir(t)
	defer os.RemoveAll(tempDir)

	outputPath := filepath.Join(tempDir, "special_chars.csv")
	results := []*PredictionResult{generateSpecialCharResult()}

	config := OutputConfig{
		IncludeIndex:      true,
		IncludePrediction: true,
		IncludeRulePath:   true,
		IncludeFeatures:   true,
	}

	err := WritePredictionCSV(results, outputPath, config)
	require.NoError(t, err)

	// Read and verify content using CSV reader
	file, err := os.Open(outputPath)
	require.NoError(t, err)
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	require.NoError(t, err)

	assert.Equal(t, 2, len(records)) // header + 1 record

	// Verify header
	headers := records[0]
	assert.Contains(t, headers, "index")
	assert.Contains(t, headers, "prediction")
	assert.Contains(t, headers, "rule_path")

	// Verify record data is properly escaped
	record := records[1]
	assert.Equal(t, "0", record[0])                          // index
	assert.Equal(t, "class,with\"special\nchars", record[1]) // prediction

	// Find rule path column
	rulePathIdx := -1
	for i, header := range headers {
		if header == "rule_path" {
			rulePathIdx = i
			break
		}
	}
	assert.True(t, rulePathIdx >= 0, "rule_path column should exist")
	assert.Equal(t, "feature_name = \"value,with,commas\"", record[rulePathIdx])
}

// Test large result sets
func TestLargeResultSets(t *testing.T) {
	tempDir := createTempDir(t)
	defer os.RemoveAll(tempDir)

	outputPath := filepath.Join(tempDir, "large_results.csv")
	results := generateTestResults(1000)

	config := OutputConfig{
		IncludeIndex:      true,
		IncludePrediction: true,
		IncludeConfidence: true,
	}

	err := WritePredictionCSV(results, outputPath, config)
	require.NoError(t, err)

	// Verify file exists and has expected size
	fileInfo, err := os.Stat(outputPath)
	require.NoError(t, err)
	assert.Greater(t, fileInfo.Size(), int64(1000)) // Should be substantial

	// Verify content by reading first and last few lines
	content, err := os.ReadFile(outputPath)
	require.NoError(t, err)

	lines := strings.Split(string(content), "\n")
	assert.Equal(t, 1002, len(lines)) // header + 1000 records + empty line

	// Verify header
	assert.Equal(t, "index,prediction,confidence", lines[0])

	// Verify first record
	assert.Equal(t, "0,class_0,0.000000", lines[1])

	// Verify last record
	assert.Equal(t, "999,class_0,0.900000", lines[1000])
}

// Test error conditions
func TestErrorConditions(t *testing.T) {
	tempDir := createTempDir(t)
	defer os.RemoveAll(tempDir)

	t.Run("no_results", func(t *testing.T) {
		outputPath := filepath.Join(tempDir, "empty.csv")
		config := DefaultOutputConfig()

		err := WritePredictionResults([]*PredictionResult{}, outputPath, CSVFormat, config)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no results to write")
	})

	t.Run("invalid_format", func(t *testing.T) {
		outputPath := filepath.Join(tempDir, "invalid.txt")
		results := generateTestResults(1)
		config := DefaultOutputConfig()

		err := WritePredictionResults(results, outputPath, "invalid", config)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "unsupported output format")
	})

	t.Run("invalid_directory", func(t *testing.T) {
		outputPath := "/nonexistent/path/test.csv"
		results := generateTestResults(1)
		config := DefaultOutputConfig()

		err := WritePredictionResults(results, outputPath, CSVFormat, config)
		// This may or may not fail depending on permissions, but shouldn't panic
		_ = err
	})
}

// Test conversion function
func TestConvertTraversalResults(t *testing.T) {
	traversalResults := []TraversalResultWithContext{
		{
			Result: &TraversalResult{
				Prediction:    "class_a",
				Confidence:    0.85,
				RulePath:      "feature > 0.5",
				Path:          []string{"step1", "step2"},
				Probabilities: map[interface{}]float64{"class_a": 0.85, "class_b": 0.15},
			},
			OriginalRecord: map[string]interface{}{"feature": 0.8},
			ProcessingTime: time.Millisecond * 100,
			Error:          nil,
		},
		{
			Result:         nil,
			OriginalRecord: map[string]interface{}{"feature": "invalid"},
			ProcessingTime: time.Millisecond * 50,
			Error:          fmt.Errorf("validation error"),
		},
	}

	results := ConvertTraversalResults(traversalResults)

	assert.Equal(t, 2, len(results))

	// First result (successful)
	assert.Equal(t, 0, results[0].Index)
	assert.Equal(t, "class_a", results[0].Prediction)
	assert.Equal(t, 0.85, results[0].Confidence)
	assert.Equal(t, "feature > 0.5", results[0].RulePath)
	assert.Equal(t, []string{"step1", "step2"}, results[0].DecisionPath)
	assert.Equal(t, time.Millisecond*100, results[0].ProcessingTime)
	assert.Equal(t, "", results[0].Error)

	// Second result (error)
	assert.Equal(t, 1, results[1].Index)
	assert.Nil(t, results[1].Prediction)
	assert.Equal(t, 0.0, results[1].Confidence)
	assert.Equal(t, time.Millisecond*50, results[1].ProcessingTime)
	assert.Equal(t, "validation error", results[1].Error)
}

// Test default config
func TestDefaultConfig(t *testing.T) {
	config := DefaultOutputConfig()
	assert.True(t, config.IncludeIndex)
	assert.True(t, config.IncludePrediction)
	assert.True(t, config.IncludeConfidence)
	assert.True(t, config.IncludeErrors)
	assert.False(t, config.IncludeFeatures)
	assert.False(t, config.IncludeProbabilities)
}

// Test convenience functions
func TestConvenienceFunctions(t *testing.T) {
	tempDir := createTempDir(t)
	defer os.RemoveAll(tempDir)

	results := generateTestResults(2)
	config := OutputConfig{
		IncludeIndex:      true,
		IncludePrediction: true,
	}

	// Test CSV convenience function
	csvPath := filepath.Join(tempDir, "convenience.csv")
	err := WritePredictionCSV(results, csvPath, config)
	require.NoError(t, err)

	// Test JSON convenience function
	jsonPath := filepath.Join(tempDir, "convenience.json")
	err = WritePredictionJSON(results, jsonPath, config)
	require.NoError(t, err)

	// Test TSV convenience function
	tsvPath := filepath.Join(tempDir, "convenience.tsv")
	err = WritePredictionTSV(results, tsvPath, config)
	require.NoError(t, err)

	// Verify all files exist
	_, err = os.Stat(csvPath)
	require.NoError(t, err)
	_, err = os.Stat(jsonPath)
	require.NoError(t, err)
	_, err = os.Stat(tsvPath)
	require.NoError(t, err)
}

// Benchmark CSV writing
func BenchmarkWriteCSV(b *testing.B) {
	tempDir, _ := os.MkdirTemp("", "benchmark_test")
	defer os.RemoveAll(tempDir)

	results := generateTestResults(1000)
	config := OutputConfig{
		IncludeIndex:      true,
		IncludePrediction: true,
		IncludeConfidence: true,
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		outputPath := filepath.Join(tempDir, fmt.Sprintf("benchmark_%d.csv", i))
		err := WritePredictionCSV(results, outputPath, config)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// Benchmark JSON writing
func BenchmarkWriteJSON(b *testing.B) {
	tempDir, _ := os.MkdirTemp("", "benchmark_test")
	defer os.RemoveAll(tempDir)

	results := generateTestResults(1000)
	config := OutputConfig{
		IncludeIndex:      true,
		IncludePrediction: true,
		IncludeConfidence: true,
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		outputPath := filepath.Join(tempDir, fmt.Sprintf("benchmark_%d.json", i))
		err := WritePredictionJSON(results, outputPath, config)
		if err != nil {
			b.Fatal(err)
		}
	}
}

package utils

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"
)

// PredictionResult represents a single prediction result for output
type PredictionResult struct {
	Index            int                     `json:"index"`
	Prediction       interface{}             `json:"prediction"`
	Confidence       float64                 `json:"confidence"`
	Probabilities    map[interface{}]float64 `json:"probabilities,omitempty"`
	RulePath         string                  `json:"rule_path,omitempty"`
	DecisionPath     []string                `json:"decision_path,omitempty"`
	OriginalFeatures map[string]interface{}  `json:"original_features,omitempty"`
	ProcessingTime   time.Duration           `json:"processing_time,omitempty"`
	Error            string                  `json:"error,omitempty"`
}

// OutputFormat represents supported output formats
type OutputFormat string

const (
	CSVFormat  OutputFormat = "csv"
	JSONFormat OutputFormat = "json"
	TSVFormat  OutputFormat = "tsv"
)

// OutputConfig configures what data to include in output
type OutputConfig struct {
	IncludeIndex          bool
	IncludePrediction     bool
	IncludeConfidence     bool
	IncludeProbabilities  bool
	IncludeRulePath       bool
	IncludeDecisionPath   bool
	IncludeFeatures       bool
	IncludeProcessingTime bool
	IncludeErrors         bool
	SelectedFeatures      []string
}

// DefaultOutputConfig returns default configuration
func DefaultOutputConfig() OutputConfig {
	return OutputConfig{
		IncludeIndex:      true,
		IncludePrediction: true,
		IncludeConfidence: true,
		IncludeErrors:     true,
	}
}

// WritePredictionResults writes prediction results to a file
func WritePredictionResults(results []*PredictionResult, outputPath string, format OutputFormat, config OutputConfig) error {
	if len(results) == 0 {
		return fmt.Errorf("no results to write")
	}

	// Create output directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(outputPath), 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}

	switch format {
	case CSVFormat:
		return writeCSV(results, outputPath, config)
	case TSVFormat:
		return writeTSV(results, outputPath, config)
	case JSONFormat:
		return writeJSON(results, outputPath, config)
	default:
		return fmt.Errorf("unsupported output format: %s", format)
	}
}

// writeCSV writes results in CSV format
func writeCSV(results []*PredictionResult, outputPath string, config OutputConfig) error {
	return writeDelimited(results, outputPath, config, ',')
}

// writeTSV writes results in TSV format
func writeTSV(results []*PredictionResult, outputPath string, config OutputConfig) error {
	return writeDelimited(results, outputPath, config, '\t')
}

// writeDelimited writes results in delimited format (CSV/TSV)
func writeDelimited(results []*PredictionResult, outputPath string, config OutputConfig, delimiter rune) error {
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	writer.Comma = delimiter
	defer writer.Flush()

	// Write header
	headers := buildHeaders(results[0], config)
	if err := writer.Write(headers); err != nil {
		return fmt.Errorf("failed to write headers: %w", err)
	}

	// Write data rows
	for _, result := range results {
		row := buildRow(result, config)
		if err := writer.Write(row); err != nil {
			return fmt.Errorf("failed to write row: %w", err)
		}
	}

	return nil
}

// writeJSON writes results in JSON format
func writeJSON(results []*PredictionResult, outputPath string, config OutputConfig) error {
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")

	// Filter results based on config
	filteredResults := make([]map[string]interface{}, len(results))
	for i, result := range results {
		filteredResults[i] = filterResult(result, config)
	}

	return encoder.Encode(filteredResults)
}

// buildHeaders constructs CSV/TSV headers based on config
func buildHeaders(sample *PredictionResult, config OutputConfig) []string {
	var headers []string

	if config.IncludeIndex {
		headers = append(headers, "index")
	}
	if config.IncludePrediction {
		headers = append(headers, "prediction")
	}
	if config.IncludeConfidence {
		headers = append(headers, "confidence")
	}
	if config.IncludeProbabilities && len(sample.Probabilities) > 0 {
		// Sort probability keys for consistent ordering
		var classes []string
		for class := range sample.Probabilities {
			classes = append(classes, fmt.Sprintf("%v", class))
		}
		sort.Strings(classes)
		for _, class := range classes {
			headers = append(headers, "prob_"+class)
		}
	}
	if config.IncludeRulePath {
		headers = append(headers, "rule_path")
	}
	if config.IncludeDecisionPath {
		headers = append(headers, "decision_path")
	}
	if config.IncludeFeatures && len(sample.OriginalFeatures) > 0 {
		featureNames := getFeatureNames(sample.OriginalFeatures, config.SelectedFeatures)
		for _, name := range featureNames {
			headers = append(headers, "feature_"+name)
		}
	}
	if config.IncludeProcessingTime {
		headers = append(headers, "processing_time_ms")
	}
	if config.IncludeErrors {
		headers = append(headers, "error")
	}

	return headers
}

// buildRow constructs a CSV/TSV row based on config
func buildRow(result *PredictionResult, config OutputConfig) []string {
	var row []string

	if config.IncludeIndex {
		row = append(row, strconv.Itoa(result.Index))
	}
	if config.IncludePrediction {
		row = append(row, formatValue(result.Prediction))
	}
	if config.IncludeConfidence {
		row = append(row, formatFloat(result.Confidence))
	}
	if config.IncludeProbabilities && len(result.Probabilities) > 0 {
		// Sort probability keys for consistent ordering
		var classes []string
		for class := range result.Probabilities {
			classes = append(classes, fmt.Sprintf("%v", class))
		}
		sort.Strings(classes)
		for _, class := range classes {
			prob := 0.0
			for origClass, origProb := range result.Probabilities {
				if fmt.Sprintf("%v", origClass) == class {
					prob = origProb
					break
				}
			}
			row = append(row, formatFloat(prob))
		}
	}
	if config.IncludeRulePath {
		row = append(row, result.RulePath)
	}
	if config.IncludeDecisionPath {
		pathStr := strings.Join(result.DecisionPath, " | ")
		row = append(row, pathStr)
	}
	if config.IncludeFeatures && len(result.OriginalFeatures) > 0 {
		featureNames := getFeatureNames(result.OriginalFeatures, config.SelectedFeatures)
		for _, name := range featureNames {
			value := ""
			if val, exists := result.OriginalFeatures[name]; exists {
				value = formatValue(val)
			}
			row = append(row, value)
		}
	}
	if config.IncludeProcessingTime {
		timeMs := float64(result.ProcessingTime.Nanoseconds()) / 1000000.0
		row = append(row, formatFloat(timeMs))
	}
	if config.IncludeErrors {
		row = append(row, result.Error)
	}

	return row
}

// filterResult filters a result for JSON output based on config
func filterResult(result *PredictionResult, config OutputConfig) map[string]interface{} {
	filtered := make(map[string]interface{})

	if config.IncludeIndex {
		filtered["index"] = result.Index
	}
	if config.IncludePrediction {
		filtered["prediction"] = result.Prediction
	}
	if config.IncludeConfidence {
		filtered["confidence"] = result.Confidence
	}
	if config.IncludeProbabilities && len(result.Probabilities) > 0 {
		filtered["probabilities"] = result.Probabilities
	}
	if config.IncludeRulePath && result.RulePath != "" {
		filtered["rule_path"] = result.RulePath
	}
	if config.IncludeDecisionPath && len(result.DecisionPath) > 0 {
		filtered["decision_path"] = result.DecisionPath
	}
	if config.IncludeFeatures && len(result.OriginalFeatures) > 0 {
		if len(config.SelectedFeatures) > 0 {
			features := make(map[string]interface{})
			for _, name := range config.SelectedFeatures {
				if val, exists := result.OriginalFeatures[name]; exists {
					features[name] = val
				}
			}
			if len(features) > 0 {
				filtered["original_features"] = features
			}
		} else {
			filtered["original_features"] = result.OriginalFeatures
		}
	}
	if config.IncludeProcessingTime {
		timeMs := float64(result.ProcessingTime.Nanoseconds()) / 1000000.0
		filtered["processing_time_ms"] = timeMs
	}
	if config.IncludeErrors && result.Error != "" {
		filtered["error"] = result.Error
	}

	return filtered
}

// getFeatureNames returns sorted feature names for consistent column ordering
func getFeatureNames(features map[string]interface{}, selectedFeatures []string) []string {
	if len(selectedFeatures) > 0 {
		// Use only selected features in specified order
		var names []string
		for _, name := range selectedFeatures {
			if _, exists := features[name]; exists {
				names = append(names, name)
			}
		}
		return names
	}

	// Use all features in alphabetical order
	var names []string
	for name := range features {
		names = append(names, name)
	}
	sort.Strings(names)
	return names
}

// formatValue formats a value for CSV/TSV output
func formatValue(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case float64:
		return formatFloat(v)
	case float32:
		return formatFloat(float64(v))
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case bool:
		return strconv.FormatBool(v)
	case time.Time:
		return v.Format(time.RFC3339)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// formatFloat formats a float64 value with 6 decimal places
func formatFloat(value float64) string {
	return strconv.FormatFloat(value, 'f', 6, 64)
}

// ConvertTraversalResults converts traversal results to PredictionResult format
func ConvertTraversalResults(traversalResults []TraversalResultWithContext) []*PredictionResult {
	results := make([]*PredictionResult, len(traversalResults))

	for i, tr := range traversalResults {
		result := &PredictionResult{
			Index:            i,
			OriginalFeatures: tr.OriginalRecord,
			ProcessingTime:   tr.ProcessingTime,
		}

		if tr.Error != nil {
			result.Error = tr.Error.Error()
		} else if tr.Result != nil {
			result.Prediction = tr.Result.Prediction
			result.Confidence = tr.Result.Confidence
			result.Probabilities = tr.Result.Probabilities
			result.RulePath = tr.Result.RulePath
			result.DecisionPath = tr.Result.Path
		}

		results[i] = result
	}

	return results
}

// TraversalResultWithContext represents a traversal result with additional context
type TraversalResultWithContext struct {
	Result         *TraversalResult
	OriginalRecord map[string]interface{}
	ProcessingTime time.Duration
	Error          error
}

// TraversalResult represents the result of tree traversal
type TraversalResult struct {
	Prediction    interface{}
	Confidence    float64
	Probabilities map[interface{}]float64
	RulePath      string
	Path          []string
}

// WritePredictionCSV is a convenience function for CSV output
func WritePredictionCSV(results []*PredictionResult, outputPath string, config OutputConfig) error {
	return WritePredictionResults(results, outputPath, CSVFormat, config)
}

// WritePredictionJSON is a convenience function for JSON output
func WritePredictionJSON(results []*PredictionResult, outputPath string, config OutputConfig) error {
	return WritePredictionResults(results, outputPath, JSONFormat, config)
}

// WritePredictionTSV is a convenience function for TSV output
func WritePredictionTSV(results []*PredictionResult, outputPath string, config OutputConfig) error {
	return WritePredictionResults(results, outputPath, TSVFormat, config)
}

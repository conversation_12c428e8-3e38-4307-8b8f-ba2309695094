// Package utils provides comprehensive unit tests for feature information parsing functionality.
package utils

import (
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/berrijam/mulberri/pkg/models"
)

// TestNewFeatureInfoParser verifies that the constructor properly initializes a new parser instance.
// This test ensures the parser is ready to use immediately after creation.
func TestNewFeatureInfoParser(t *testing.T) {
	parser := NewFeatureInfoParser()
	if parser == nil {
		t.Fatal("NewFeatureInfoParser should return a non-nil parser")
	}
}

// TestParseFeatureInfoFile_ValidYAML tests parsing of a well-formed YAML feature information file.
// This test covers the core functionality including:
// - Parsing both numeric and nominal features
// - Handling numeric bounds (min/max)
// - Parsing nominal value lists
// - Proper type conversion and validation
func TestParseFeatureInfoFile_ValidYAML(t *testing.T) {
	parser := NewFeatureInfoParser()

	// Create temporary file with valid YAML
	content := `temperature:
  type: numeric
  min: -10.0
  max: 50.0

outlook:
  type: nominal
  values:
    - sunny
    - overcast
    - rainy

play:
  type: nominal
  values:
    - "yes"
    - "no"`

	tmpFile := createTempFile(t, content)
	defer os.Remove(tmpFile)

	config, err := parser.ParseFeatureInfoFile(tmpFile)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if config == nil {
		t.Fatal("Expected config, got nil")
	}

	// Validate temperature feature
	temp, exists := (*config)["temperature"]
	if !exists {
		t.Fatal("Expected temperature feature to exist")
	}
	if temp.Type != "numeric" {
		t.Errorf("Expected temperature type to be numeric, got: %s", temp.Type)
	}
	if temp.Min == nil || *temp.Min != -10.0 {
		t.Errorf("Expected temperature min to be -10.0, got: %v", temp.Min)
	}
	if temp.Max == nil || *temp.Max != 50.0 {
		t.Errorf("Expected temperature max to be 50.0, got: %v", temp.Max)
	}

	// Validate outlook feature
	outlook, exists := (*config)["outlook"]
	if !exists {
		t.Fatal("Expected outlook feature to exist")
	}
	if outlook.Type != "nominal" {
		t.Errorf("Expected outlook type to be nominal, got: %s", outlook.Type)
	}
	expectedValues := []string{"sunny", "overcast", "rainy"}
	if len(outlook.Values) != len(expectedValues) {
		t.Errorf("Expected %d values, got %d", len(expectedValues), len(outlook.Values))
	}
	for i, expected := range expectedValues {
		if i >= len(outlook.Values) || outlook.Values[i] != expected {
			t.Errorf("Expected value %s at index %d, got %s", expected, i, outlook.Values[i])
		}
	}
}

// TestParseFeatureInfoFile_UnicodeSupport verifies that the parser correctly handles Unicode characters.
// This test ensures internationalization support for:
// - Feature names with accented characters, emoji, and non-Latin scripts
// - Feature values containing Unicode symbols and characters
// - Proper validation of Unicode whitespace and empty strings
func TestParseFeatureInfoFile_UnicodeSupport(t *testing.T) {
	parser := NewFeatureInfoParser()

	// Test with Unicode characters
	content := `température:
  type: numeric

météo:
  type: nominal
  values:
    - "☀️ ensoleillé"
    - "☁️ nuageux"
    - "🌧️ pluvieux"

中文特征:
  type: nominal
  values:
    - "是"
    - "否"`

	tmpFile := createTempFile(t, content)
	defer os.Remove(tmpFile)

	config, err := parser.ParseFeatureInfoFile(tmpFile)
	if err != nil {
		t.Fatalf("Expected no error with Unicode, got: %v", err)
	}
	if config == nil {
		t.Fatal("Expected config, got nil")
	}

	// Validate Unicode feature names and values
	_, exists := (*config)["température"]
	if !exists {
		t.Error("Expected Unicode feature name 'température' to exist")
	}

	meteo, exists := (*config)["météo"]
	if !exists {
		t.Error("Expected Unicode feature name 'météo' to exist")
	} else {
		if len(meteo.Values) != 3 {
			t.Errorf("Expected 3 Unicode values, got %d", len(meteo.Values))
		}
	}

	chinese, exists := (*config)["中文特征"]
	if !exists {
		t.Error("Expected Chinese feature name to exist")
	} else {
		if len(chinese.Values) != 2 {
			t.Errorf("Expected 2 Chinese values, got %d", len(chinese.Values))
		}
	}
}

// TestParseFeatureInfoFile_InvalidYAML tests error handling for malformed YAML syntax.
// This test ensures that YAML parsing errors are properly detected and reported
// with clear, actionable error messages that distinguish YAML syntax issues
// from validation problems.
func TestParseFeatureInfoFile_InvalidYAML(t *testing.T) {
	parser := NewFeatureInfoParser()

	content := `temperature:
  type: numeric
outlook:
  type: nominal
  values:
    - sunny
    - overcast
  invalid_indent`

	tmpFile := createTempFile(t, content)
	defer os.Remove(tmpFile)

	_, err := parser.ParseFeatureInfoFile(tmpFile)
	if err == nil {
		t.Error("Expected error for invalid YAML")
	}

	// Test structured error
	if metaErr, ok := err.(*FeatureInfoError); ok {
		if metaErr.Op != "parse_yaml" {
			t.Errorf("Expected metadata error with 'parse_yaml' operation, got: %s", metaErr.Op)
		}
	} else {
		t.Errorf("Expected FeatureInfoError, got: %T", err)
	}

	if !containsSubstring(err.Error(), "parse_yaml") {
		t.Errorf("Expected YAML parsing error message, got: %v", err)
	}
}

// TestParseFeatureInfoFile_EmptyFile tests handling of empty YAML files.
// Empty files should be rejected with a clear validation error since they
// provide no useful feature information for the decision tree algorithm.
func TestParseFeatureInfoFile_EmptyFile(t *testing.T) {
	parser := NewFeatureInfoParser()

	tmpFile := createTempFile(t, "")
	defer os.Remove(tmpFile)

	_, err := parser.ParseFeatureInfoFile(tmpFile)
	if err == nil {
		t.Error("Expected error for empty file")
	}

	// Test structured error
	if metaErr, ok := err.(*FeatureInfoError); ok {
		if metaErr.Op != "validate" {
			t.Errorf("Expected metadata error with 'validate' operation, got: %s", metaErr.Op)
		}
	}

	if !containsSubstring(err.Error(), "file is empty") {
		t.Errorf("Expected empty file error message, got: %v", err)
	}
}

// TestParseFeatureInfoFile_WhitespaceOnlyFile tests handling of files with only whitespace.
func TestParseFeatureInfoFile_WhitespaceOnlyFile(t *testing.T) {
	parser := NewFeatureInfoParser()

	tmpFile := createTempFile(t, "   \n\t  \n  ")
	defer os.Remove(tmpFile)

	_, err := parser.ParseFeatureInfoFile(tmpFile)
	if err == nil {
		t.Error("Expected error for whitespace-only file")
	}

	if !containsSubstring(err.Error(), "contains only whitespace") {
		t.Errorf("Expected whitespace-only file error message, got: %v", err)
	}
}

// TestValidateFeature_InvalidType tests validation of feature type constraints.
// This test suite covers various invalid type scenarios including:
// - Unsupported feature types (not "nominal" or "numeric")
// - Missing type fields
// - Empty type values
func TestValidateFeature_InvalidType(t *testing.T) {
	parser := NewFeatureInfoParser()

	tests := []struct {
		name     string
		content  string
		errorMsg string
	}{
		{
			name: "invalid_type",
			content: `feature1:
  type: categorical`,
			errorMsg: "must be 'nominal', 'numeric', or 'datetime'",
		},
		{
			name: "missing_type",
			content: `feature1:
  values:
    - value1`,
			errorMsg: "must have a type field",
		},
		{
			name: "empty_type",
			content: `feature1:
  type: ""`,
			errorMsg: "must have a type field",
		},
		{
			name: "whitespace_type",
			content: `feature1:
  type: "   "`,
			errorMsg: "must have a type field",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			tmpFile := createTempFile(t, test.content)
			defer os.Remove(tmpFile)

			_, err := parser.ParseFeatureInfoFile(tmpFile)
			if err == nil {
				t.Error("Expected validation error")
			}

			// Test structured error
			if metaErr, ok := err.(*FeatureInfoError); ok {
				if metaErr.Feature != "feature1" {
					t.Errorf("Expected error for feature 'feature1', got: %s", metaErr.Feature)
				}
			}

			if !containsSubstring(err.Error(), test.errorMsg) {
				t.Errorf("Expected error containing '%s', got: %v", test.errorMsg, err)
			}
		})
	}
}

// TestValidateNominalFeature tests validation rules specific to nominal (categorical) features.
// This comprehensive test suite covers:
// - Missing or empty values lists
// - Empty or whitespace-only individual values
// - Duplicate values in the list
// - Invalid presence of numeric constraints (min/max)
func TestValidateNominalFeature(t *testing.T) {
	parser := NewFeatureInfoParser()

	tests := []struct {
		name     string
		content  string
		errorMsg string
	}{
		{
			name: "empty_values",
			content: `feature1:
  type: nominal
  values: []`,
			errorMsg: "must have a non-empty values list",
		},
		{
			name: "missing_values",
			content: `feature1:
  type: nominal`,
			errorMsg: "must have a non-empty values list",
		},
		{
			name: "empty_value",
			content: `feature1:
  type: nominal
  values:
    - value1
    - ""
    - value3`,
			errorMsg: "cannot be empty or whitespace-only",
		},
		{
			name: "whitespace_value",
			content: `feature1:
  type: nominal
  values:
    - value1
    - "   "
    - value3`,
			errorMsg: "cannot be empty or whitespace-only",
		},
		{
			name: "duplicate_values",
			content: `feature1:
  type: nominal
  values:
    - value1
    - value2
    - value1`,
			errorMsg: "duplicate value",
		},
		{
			name: "has_min_max",
			content: `feature1:
  type: nominal
  values:
    - value1
    - value2
  min: 0
  max: 10`,
			errorMsg: "should not have min or max properties",
		},
		{
			name: "has_min_only",
			content: `feature1:
  type: nominal
  values:
    - value1
    - value2
  min: 0`,
			errorMsg: "should not have min or max properties",
		},
		{
			name: "has_max_only",
			content: `feature1:
  type: nominal
  values:
    - value1
    - value2
  max: 10`,
			errorMsg: "should not have min or max properties",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			tmpFile := createTempFile(t, test.content)
			defer os.Remove(tmpFile)

			_, err := parser.ParseFeatureInfoFile(tmpFile)
			if err == nil {
				t.Error("Expected validation error")
			}

			// Test structured error
			if metaErr, ok := err.(*FeatureInfoError); ok {
				if metaErr.Feature != "feature1" {
					t.Errorf("Expected error for feature 'feature1', got: %s", metaErr.Feature)
				}
			}

			if !containsSubstring(err.Error(), test.errorMsg) {
				t.Errorf("Expected error containing '%s', got: %v", test.errorMsg, err)
			}
		})
	}
}

// TestValidateNumericFeature tests validation rules specific to numeric (continuous) features.
// This test suite ensures proper validation of:
// - Invalid presence of categorical values lists
// - Logical min/max relationships (min <= max)
// - Prevention of degenerate ranges (min == max)
func TestValidateNumericFeature(t *testing.T) {
	parser := NewFeatureInfoParser()

	tests := []struct {
		name     string
		content  string
		errorMsg string
	}{
		{
			name: "has_values",
			content: `feature1:
  type: numeric
  values:
    - value1`,
			errorMsg: "should not have a values list",
		},
		{
			name: "min_greater_than_max",
			content: `feature1:
  type: numeric
  min: 10.0
  max: 5.0`,
			errorMsg: "min value (10.000000) cannot be greater than max value (5.000000)",
		},
		{
			name: "min_equals_max",
			content: `feature1:
  type: numeric
  min: 5.0
  max: 5.0`,
			errorMsg: "min and max values cannot be equal",
		},
		{
			name: "valid_numeric_with_bounds",
			content: `feature1:
  type: numeric
  min: 0.0
  max: 100.0`,
			errorMsg: "", // Should not error
		},
		{
			name: "valid_numeric_no_bounds",
			content: `feature1:
  type: numeric`,
			errorMsg: "", // Should not error
		},
		{
			name: "valid_numeric_min_only",
			content: `feature1:
  type: numeric
  min: 0.0`,
			errorMsg: "", // Should not error
		},
		{
			name: "valid_numeric_max_only",
			content: `feature1:
  type: numeric
  max: 100.0`,
			errorMsg: "", // Should not error
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			tmpFile := createTempFile(t, test.content)
			defer os.Remove(tmpFile)

			_, err := parser.ParseFeatureInfoFile(tmpFile)

			if test.errorMsg == "" {
				// Should not error
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			} else {
				// Should error
				if err == nil {
					t.Error("Expected validation error")
				} else {
					if !containsSubstring(err.Error(), test.errorMsg) {
						t.Errorf("Expected error containing '%s', got: %v", test.errorMsg, err)
					}
				}
			}
		})
	}
}

// TestValidateFeatureName tests validation of feature name constraints.
// This test ensures that feature names cannot be empty or consist only of whitespace,
// including various Unicode whitespace characters.
func TestValidateFeatureName(t *testing.T) {
	parser := NewFeatureInfoParser()

	tests := []struct {
		name     string
		content  string
		errorMsg string
	}{
		{
			name: "whitespace_feature_name",
			content: `"   ":
  type: nominal
  values:
    - value1`,
			errorMsg: "feature name cannot be empty or whitespace-only",
		},
		{
			name: "tab_feature_name",
			content: `"\t":
  type: nominal
  values:
    - value1`,
			errorMsg: "feature name cannot be empty or whitespace-only",
		},
		{
			name: "unicode_whitespace_feature_name",
			content: `" \u00A0 ":
  type: nominal
  values:
    - value1`,
			errorMsg: "feature name cannot be empty or whitespace-only",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			tmpFile := createTempFile(t, test.content)
			defer os.Remove(tmpFile)

			_, err := parser.ParseFeatureInfoFile(tmpFile)
			if err == nil {
				t.Error("Expected validation error")
			}
			if !containsSubstring(err.Error(), test.errorMsg) {
				t.Errorf("Expected error containing '%s', got: %v", test.errorMsg, err)
			}
		})
	}
}

// TestParseFeatureInfoFile_ConsistentParsing verifies that the parser produces consistent results
// when parsing the same file multiple times without caching.
func TestParseFeatureInfoFile_ConsistentParsing(t *testing.T) {
	parser := NewFeatureInfoParser()

	content := `temperature:
  type: numeric
  min: -10.0
  max: 50.0

outlook:
  type: nominal
  values:
    - sunny
    - overcast
    - rainy`

	tmpFile := createTempFile(t, content)
	defer os.Remove(tmpFile)

	// Parse multiple times
	config1, err1 := parser.ParseFeatureInfoFile(tmpFile)
	if err1 != nil {
		t.Fatalf("First parse failed: %v", err1)
	}

	config2, err2 := parser.ParseFeatureInfoFile(tmpFile)
	if err2 != nil {
		t.Fatalf("Second parse failed: %v", err2)
	}

	// Verify both configs have the same content
	if len(*config1) != len(*config2) {
		t.Error("Parsed configs have different lengths")
	}

	// Check temperature feature consistency
	temp1 := (*config1)["temperature"]
	temp2 := (*config2)["temperature"]

	if temp1.Type != temp2.Type {
		t.Errorf("Temperature type mismatch: %s vs %s", temp1.Type, temp2.Type)
	}

	if (temp1.Min == nil) != (temp2.Min == nil) || (temp1.Min != nil && *temp1.Min != *temp2.Min) {
		t.Errorf("Temperature min mismatch: %v vs %v", temp1.Min, temp2.Min)
	}

	if (temp1.Max == nil) != (temp2.Max == nil) || (temp1.Max != nil && *temp1.Max != *temp2.Max) {
		t.Errorf("Temperature max mismatch: %v vs %v", temp1.Max, temp2.Max)
	}

	// Check outlook feature consistency
	outlook1 := (*config1)["outlook"]
	outlook2 := (*config2)["outlook"]

	if outlook1.Type != outlook2.Type {
		t.Errorf("Outlook type mismatch: %s vs %s", outlook1.Type, outlook2.Type)
	}

	if len(outlook1.Values) != len(outlook2.Values) {
		t.Errorf("Outlook values length mismatch: %d vs %d", len(outlook1.Values), len(outlook2.Values))
	}

	for i, val1 := range outlook1.Values {
		if i >= len(outlook2.Values) || val1 != outlook2.Values[i] {
			t.Errorf("Outlook value mismatch at index %d: %s vs %s", i, val1, outlook2.Values[i])
		}
	}
}

// TestValidateFeatureInfoAgainstCSV tests validation of feature info against CSV headers
func TestValidateFeatureInfoAgainstCSV(t *testing.T) {
	parser := NewFeatureInfoParser()

	// Create a valid config
	content := `temperature:
  type: numeric
  min: -10.0
  max: 50.0

outlook:
  type: nominal
  values:
    - sunny
    - overcast
    - rainy`

	tmpFile := createTempFile(t, content)
	defer os.Remove(tmpFile)

	config, err := parser.ParseFeatureInfoFile(tmpFile)
	if err != nil {
		t.Fatalf("Failed to parse config: %v", err)
	}

	tests := []struct {
		name        string
		csvHeaders  []string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "matching headers",
			csvHeaders:  []string{"temperature", "outlook", "target"},
			expectError: false,
		},
		{
			name:        "subset of headers (partial metadata)",
			csvHeaders:  []string{"temperature", "outlook", "humidity", "target"},
			expectError: false, // Partial metadata is allowed
		},
		{
			name:        "missing feature in CSV",
			csvHeaders:  []string{"temperature", "target"}, // missing 'outlook'
			expectError: true,
			errorMsg:    "features defined in feature info but missing in CSV",
		},
		{
			name:        "completely different headers",
			csvHeaders:  []string{"feature1", "feature2", "target"},
			expectError: true,
			errorMsg:    "features defined in feature info but missing in CSV",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := parser.ValidateFeatureInfoAgainstCSV(config, test.csvHeaders, tmpFile)

			if test.expectError {
				if err == nil {
					t.Error("Expected validation error but got none")
					return
				}
				if test.errorMsg != "" && !containsSubstring(err.Error(), test.errorMsg) {
					t.Errorf("Expected error containing '%s', got: %v", test.errorMsg, err)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			}
		})
	}
}

// Test error unwrapping functionality
func TestFeatureInfoError_Unwrap(t *testing.T) {
	originalErr := os.ErrNotExist
	metaErr := &FeatureInfoError{
		Op:   "read",
		File: "test.yaml",
		Err:  originalErr,
	}

	if metaErr.Unwrap() != originalErr {
		t.Errorf("FeatureInfoError.Unwrap() should return the original error")
	}
}

// Test error message formatting
func TestFeatureInfoError_ErrorMessage(t *testing.T) {
	tests := []struct {
		name     string
		err      *FeatureInfoError
		expected string
	}{
		{
			name: "with feature",
			err: &FeatureInfoError{
				Op:      "validate",
				File:    "test.yaml",
				Feature: "temperature",
				Err:     os.ErrInvalid,
			},
			expected: "feature info validate error for feature 'temperature' in file 'test.yaml': invalid argument",
		},
		{
			name: "without feature",
			err: &FeatureInfoError{
				Op:   "read",
				File: "test.yaml",
				Err:  os.ErrNotExist,
			},
			expected: "feature info read error in file 'test.yaml': file does not exist",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			actual := test.err.Error()
			if actual != test.expected {
				t.Errorf("Expected error message '%s', got '%s'", test.expected, actual)
			}
		})
	}
}

// Test ToModelFeatureType conversion
func TestFeatureInfo_ToModelFeatureType(t *testing.T) {
	tests := []struct {
		name     string
		info     FeatureInfo
		expected models.FeatureType
	}{
		{
			name:     "nominal to categorical",
			info:     FeatureInfo{Type: "nominal"},
			expected: models.CategoricalFeature,
		},
		{
			name:     "numeric to numeric",
			info:     FeatureInfo{Type: "numeric"},
			expected: models.NumericFeature,
		},
		{
			name:     "datetime to datetime",
			info:     FeatureInfo{Type: "datetime"},
			expected: models.DateTimeFeature,
		},
		{
			name:     "unknown defaults to categorical",
			info:     FeatureInfo{Type: "unknown"},
			expected: models.CategoricalFeature,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := test.info.ToModelFeatureType()
			if result != test.expected {
				t.Errorf("Expected %v, got %v", test.expected, result)
			}
		})
	}
}

// createTempFile creates a temporary YAML file for testing.
func createTempFile(t *testing.T, content string) string {
	tmpDir := t.TempDir()
	tmpFile := filepath.Join(tmpDir, "test_feature_info.yaml")

	err := os.WriteFile(tmpFile, []byte(content), 0644)
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}

	return tmpFile
}

// containsSubstring checks if str contains substr anywhere.
func containsSubstring(str, substr string) bool {
	return len(str) >= len(substr) &&
		(str == substr ||
			str[:len(substr)] == substr ||
			str[len(str)-len(substr):] == substr ||
			strings.Contains(str, substr))
}

// TestValidateDateTimeFeatureConfig tests validation of datetime feature configurations.
func TestValidateDateTimeFeatureConfig(t *testing.T) {
	parser := NewFeatureInfoParser()

	tests := []struct {
		name          string
		content       string
		shouldSucceed bool
		expectedError string
	}{
		{
			name: "valid_datetime",
			content: `timestamp:
  type: datetime`,
			shouldSucceed: true,
		},
		{
			name: "datetime_with_values",
			content: `timestamp:
  type: datetime
  values: ["2023-01-01", "2023-12-31"]`,
			shouldSucceed: false,
			expectedError: "should not have a values list",
		},
		{
			name: "datetime_with_min",
			content: `timestamp:
  type: datetime
  min: 20230101000000`,
			shouldSucceed: true,
		},
		{
			name: "datetime_with_max",
			content: `timestamp:
  type: datetime
  max: 20231231235959`,
			shouldSucceed: true,
		},
		{
			name: "datetime_with_min_max",
			content: `timestamp:
  type: datetime
  min: 20230101000000
  max: 20231231235959`,
			shouldSucceed: true,
		},
		{
			name: "datetime_with_invalid_range",
			content: `timestamp:
  type: datetime
  min: 20231231235959
  max: 20230101000000`,
			shouldSucceed: false,
			expectedError: "min value (20231231235959) cannot be greater than max value (20230101000000)",
		},
		{
			name: "datetime_with_equal_min_max",
			content: `timestamp:
  type: datetime
  min: 20230615120000
  max: 20230615120000`,
			shouldSucceed: false,
			expectedError: "min and max values cannot be equal (20230615120000)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tmpFile := createTempFile(t, tt.content)
			defer os.Remove(tmpFile)

			config, err := parser.ParseFeatureInfoFile(tmpFile)

			if tt.shouldSucceed {
				if err != nil {
					t.Errorf("Expected success, got error: %v", err)
				}
				if config == nil {
					t.Error("Expected non-nil config")
					return
				}
				// Verify the feature type is correctly converted
				if len(*config) > 0 {
					for featureName, featureInfo := range *config {
						if featureInfo.Type != "datetime" {
							t.Errorf("Expected datetime feature type for %s, got %s", featureName, featureInfo.Type)
						}
						// Test the conversion to model type
						modelType := featureInfo.ToModelFeatureType()
						if modelType != models.DateTimeFeature {
							t.Errorf("Expected DateTimeFeature model type, got %v", modelType)
						}
					}
				}
			} else {
				if err == nil {
					t.Error("Expected error, got success")
				}
				if !containsSubstring(err.Error(), tt.expectedError) {
					t.Errorf("Expected error containing '%s', got: %v", tt.expectedError, err)
				}
			}
		})
	}
}

// TestToModelFeatureType tests the type conversion from YAML to model types.
func TestToModelFeatureType(t *testing.T) {
	tests := []struct {
		yamlType     string
		expectedType models.FeatureType
	}{
		{"nominal", models.CategoricalFeature},
		{"numeric", models.NumericFeature},
		{"datetime", models.DateTimeFeature},
		{"invalid", models.CategoricalFeature}, // fallback
		{"", models.CategoricalFeature},        // fallback
	}

	for _, tt := range tests {
		t.Run(tt.yamlType, func(t *testing.T) {
			featureInfo := FeatureInfo{Type: tt.yamlType}
			modelType := featureInfo.ToModelFeatureType()

			if modelType != tt.expectedType {
				t.Errorf("Expected %v, got %v", tt.expectedType, modelType)
			}
		})
	}
}

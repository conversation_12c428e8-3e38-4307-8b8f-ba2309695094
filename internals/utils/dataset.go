// Package utils provides dataset utilities for machine learning operations.
package utils

import (
	"fmt"
	"strconv"

	"github.com/berrijam/mulberri/internals/training"
	datetimeconverter "github.com/berrijam/mulberri/internals/utils/datetime_converter"
	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// CSVDataset implements training.Dataset[string] for CSV data
type CSVDataset struct {
	features [][]string
	targets  []string
	indices  []int
}

// NewCSVDataset creates a new CSV dataset
func NewCSVDataset(features [][]string, targets []string) training.Dataset[string] {
	if len(features) != len(targets) {
		errMsg := fmt.Sprintf("Features and targets length mismatch: %d vs %d", len(features), len(targets))
		logger.Fatal(errMsg)
	}

	indices := make([]int, len(targets))
	for i := range indices {
		indices[i] = i
	}

	return &CSVDataset{
		features: features,
		targets:  targets,
		indices:  indices,
	}
}

// NewCSVDatasetWithDateTimeConversion creates a new CSV dataset with datetime features converted to integers at load time
func NewCSVDatasetWithDateTimeConversion(features [][]string, targets []string, featureObjects []*models.Feature) training.Dataset[string] {
	if len(features) != len(targets) {
		errMsg := fmt.Sprintf("Features and targets length mismatch: %d vs %d", len(features), len(targets))
		logger.Fatal(errMsg)
	}

	if len(features) > 0 && len(featureObjects) != len(features[0]) {
		logger.Error(fmt.Sprintf("Feature objects and feature columns length mismatch: %d vs %d, falling back to basic dataset",
			len(featureObjects), len(features[0])))
		return NewCSVDataset(features, targets)
	}

	// No preprocessing needed - datetime conversion happens lazily in GetFeatureValue
	indices := make([]int, len(targets))
	for i := range indices {
		indices[i] = i
	}

	return &CSVDataset{
		features: features,
		targets:  targets,
		indices:  indices,
	}
}

// NewCSVDatasetSmart creates a CSV dataset with automatic datetime conversion when possible
// Falls back to basic dataset creation if feature objects are not available or don't match
func NewCSVDatasetSmart(features [][]string, targets []string, featureObjects []*models.Feature) training.Dataset[string] {
	// Basic validation
	if len(features) != len(targets) {
		errMsg := fmt.Sprintf("Features and targets length mismatch: %d vs %d", len(features), len(targets))
		logger.Fatal(errMsg)
	}
	if len(features) == 0 || len(featureObjects) != len(features[0]) {
		logger.Debug(fmt.Sprintf("Feature objects count (%d) doesn't match feature columns (%d), using basic CSV dataset",
			len(featureObjects), len(features[0])))
		return NewCSVDataset(features, targets)
	}

	// Check if we can use datetime conversion
	if featureObjects == nil {
		logger.Debug("No feature objects provided, using basic CSV dataset")
		return NewCSVDataset(features, targets)
	}

	// Check if any features are datetime type
	hasDateTimeFeatures := false
	for _, feature := range featureObjects {
		if feature.Type == models.DateTimeFeature {
			hasDateTimeFeatures = true
			break
		}
	}

	if !hasDateTimeFeatures {
		logger.Debug("No datetime features found, using basic CSV dataset")
		return NewCSVDataset(features, targets)
	}

	// Use datetime conversion
	logger.Debug("DateTime features detected, using optimized dataset with datetime conversion")
	return NewCSVDatasetWithDateTimeConversion(features, targets, featureObjects)
}

// GetSize returns the number of samples in the current dataset
func (d *CSVDataset) GetSize() int {
	return len(d.indices)
}

// GetFeatureValue retrieves a feature value for a given sample and feature
func (d *CSVDataset) GetFeatureValue(sampleIdx int, feature *models.Feature) (interface{}, error) {
	if sampleIdx < 0 || sampleIdx >= len(d.features) {
		return nil, fmt.Errorf("sample index %d out of range [0, %d)", sampleIdx, len(d.features))
	}

	if feature.ColumnNumber >= len(d.features[sampleIdx]) {
		return nil, fmt.Errorf("feature column %d out of range for sample %d", feature.ColumnNumber, sampleIdx)
	}

	value := d.features[sampleIdx][feature.ColumnNumber]

	// Convert based on feature type
	switch feature.Type {
	case models.NumericFeature:
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue, nil
		}
		return value, nil
	case models.CategoricalFeature:
		return value, nil
	case models.DateTimeFeature:
		// For datetime features, try to parse as int64 first (pre-converted values)
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue, nil
		}
		// Convert ISO 8601 datetime string to int64
		converter := datetimeconverter.NewDateTimeConverter()
		intValue, err := converter.ConvertISO8601ToInt(value)
		if err != nil {
			// Return error instead of falling back to string
			return nil, fmt.Errorf("failed to convert datetime value '%s' to int64: %w", value, err)
		}
		return intValue, nil
	default:
		return value, nil
	}
}

// GetTarget retrieves the target value for a given sample
func (d *CSVDataset) GetTarget(sampleIdx int) (string, error) {
	if sampleIdx < 0 || sampleIdx >= len(d.targets) {
		return "", fmt.Errorf("sample index %d out of range [0, %d)", sampleIdx, len(d.targets))
	}

	return d.targets[sampleIdx], nil
}

// GetIndices returns the current sample indices
func (d *CSVDataset) GetIndices() []int {
	return d.indices
}

// Subset creates a new dataset view with the specified indices
// This is a zero-copy operation that creates a new view without duplicating data
func (d *CSVDataset) Subset(indices []int) training.Dataset[string] {
	return &CSVDataset{
		features: d.features,
		targets:  d.targets,
		indices:  indices,
	}
}

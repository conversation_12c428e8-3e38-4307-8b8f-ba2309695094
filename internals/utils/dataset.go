// Package utils provides dataset utilities for machine learning operations.
package utils

import (
	"fmt"
	"strconv"

	"github.com/berrijam/mulberri/internals/training"
	datetimeconverter "github.com/berrijam/mulberri/internals/utils/datetime_converter"
	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// CSVDataset implements training.Dataset[string] for CSV data
type CSVDataset struct {
	features [][]string
	targets  []string
	indices  []int
}

// validateDatasetInputs performs common validation for dataset creation
func validateDatasetInputs(features [][]string, targets []string, featureObjects []*models.Feature) error {
	if len(features) != len(targets) {
		logger.Fatal(fmt.Sprintf("features and targets length mismatch: %d vs %d", len(features), len(targets)))
		return fmt.Errorf("features and targets length mismatch: %d vs %d", len(features), len(targets))
	}

	if len(features) > 0 && featureObjects != nil && len(featureObjects) != len(features[0]) {
		return fmt.Errorf("feature objects and feature columns length mismatch: %d vs %d",
			len(featureObjects), len(features[0]))
	}

	return nil
}

// NewCSVDataset creates a new CSV dataset
func NewCSVDataset(features [][]string, targets []string) training.Dataset[string] {
	// Use validation helper (only checks features/targets length mismatch)
	validateDatasetInputs(features, targets, nil)

	indices := make([]int, len(targets))
	for i := range indices {
		indices[i] = i
	}

	return &CSVDataset{
		features: features,
		targets:  targets,
		indices:  indices,
	}
}

// NewCSVDatasetWithDateTimeConversion creates a new CSV dataset with datetime features converted to integers at load time
func NewCSVDatasetWithDateTimeConversion(features [][]string, targets []string, featureObjects []*models.Feature) training.Dataset[string] {
	// Use validation helper
	if err := validateDatasetInputs(features, targets, featureObjects); err != nil {
		// Check if it's a feature objects mismatch (non-fatal)
		if len(features) > 0 && featureObjects != nil && len(featureObjects) != len(features[0]) {
			logger.Error(fmt.Sprintf("Feature objects and feature columns length mismatch: %d vs %d, falling back to basic dataset",
				len(featureObjects), len(features[0])))
			return NewCSVDataset(features, targets)
		}
		// Features/targets mismatch is already handled by Fatal in validateDatasetInputs
	}

	// Pre-convert datetime features to integers during dataset creation
	convertedFeatures := make([][]string, len(features))
	converter := datetimeconverter.NewDateTimeConverter()

	for i, row := range features {
		convertedRow := make([]string, len(row))
		for j, value := range row {
			// Check if this column is a datetime feature
			if j < len(featureObjects) && featureObjects[j].Type == models.DateTimeFeature {
				// Convert datetime string to integer string representation
				intValue, err := converter.ConvertISO8601ToInt(value)
				if err != nil {
					logger.Error(fmt.Sprintf("Failed to convert datetime value '%s' at row %d, col %d: %v", value, i, j, err))
					// Keep original value on conversion failure
					convertedRow[j] = value
				} else {
					// Store as integer string for consistent handling
					convertedRow[j] = strconv.FormatInt(intValue, 10)
				}
			} else {
				// Keep non-datetime values as-is
				convertedRow[j] = value
			}
		}
		convertedFeatures[i] = convertedRow
	}

	indices := make([]int, len(targets))
	for i := range indices {
		indices[i] = i
	}

	return &CSVDataset{
		features: convertedFeatures,
		targets:  targets,
		indices:  indices,
	}
}

// NewCSVDatasetSmart creates a CSV dataset with automatic datetime conversion when possible
// Falls back to basic dataset creation if feature objects are not available or don't match
func NewCSVDatasetSmart(features [][]string, targets []string, featureObjects []*models.Feature) training.Dataset[string] {
	// Use validation helper
	if err := validateDatasetInputs(features, targets, featureObjects); err != nil {
		// Check if it's a feature objects mismatch (non-fatal)
		if len(features) == 0 || (featureObjects != nil && len(featureObjects) != len(features[0])) {
			logger.Debug(fmt.Sprintf("Feature objects count (%d) doesn't match feature columns (%d), using basic CSV dataset",
				len(featureObjects), len(features[0])))
			return NewCSVDataset(features, targets)
		}
		// Features/targets mismatch is already handled by Fatal in validateDatasetInputs
	}

	// Check if we can use datetime conversion
	if featureObjects == nil {
		logger.Debug("No feature objects provided, using basic CSV dataset")
		return NewCSVDataset(features, targets)
	}

	// Check if any features are datetime type
	hasDateTimeFeatures := false
	for _, feature := range featureObjects {
		if feature.Type == models.DateTimeFeature {
			hasDateTimeFeatures = true
			break
		}
	}

	if !hasDateTimeFeatures {
		logger.Debug("No datetime features found, using basic CSV dataset")
		return NewCSVDataset(features, targets)
	}

	// Use datetime conversion
	logger.Debug("DateTime features detected, using optimized dataset with datetime conversion")
	return NewCSVDatasetWithDateTimeConversion(features, targets, featureObjects)
}

// GetSize returns the number of samples in the current dataset
func (d *CSVDataset) GetSize() int {
	return len(d.indices)
}

// GetFeatureValue retrieves a feature value for a given sample and feature
// Returns values in one of three types: string (categorical), float64 (numeric), int64 (datetime/integer)
func (d *CSVDataset) GetFeatureValue(sampleIdx int, feature *models.Feature) (interface{}, error) {
	if sampleIdx < 0 || sampleIdx >= len(d.features) {
		return nil, fmt.Errorf("sample index %d out of range [0, %d)", sampleIdx, len(d.features))
	}

	if feature.ColumnNumber >= len(d.features[sampleIdx]) {
		return nil, fmt.Errorf("feature column %d out of range for sample %d", feature.ColumnNumber, sampleIdx)
	}

	value := d.features[sampleIdx][feature.ColumnNumber]

	// Convert based on feature type to one of three internal types
	switch feature.Type {
	case models.NumericFeature:
		// Try to parse as float (numeric type)
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue, nil
		}
		// If parsing fails, treat as categorical string
		return value, nil
	case models.CategoricalFeature:
		// Return as string (categorical type)
		return value, nil
	case models.DateTimeFeature:
		// Parse as int64 (datetime converted to integer during dataset creation)
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue, nil
		}
		// If parsing fails, this indicates a problem with pre-conversion
		return nil, fmt.Errorf("datetime feature value '%s' should have been pre-converted to integer", value)
	default:
		// Default to string for unknown types
		return value, nil
	}
}

// GetTarget retrieves the target value for a given sample
func (d *CSVDataset) GetTarget(sampleIdx int) (string, error) {
	if sampleIdx < 0 || sampleIdx >= len(d.targets) {
		return "", fmt.Errorf("sample index %d out of range [0, %d)", sampleIdx, len(d.targets))
	}

	return d.targets[sampleIdx], nil
}

// GetIndices returns the current sample indices
func (d *CSVDataset) GetIndices() []int {
	return d.indices
}

// Subset creates a new dataset view with the specified indices
// This is a zero-copy operation that creates a new view without duplicating data
func (d *CSVDataset) Subset(indices []int) training.Dataset[string] {
	return &CSVDataset{
		features: d.features,
		targets:  d.targets,
		indices:  indices,
	}
}

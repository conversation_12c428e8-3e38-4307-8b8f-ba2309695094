package utils

import (
	"reflect"
	"testing"

	"github.com/berrijam/mulberri/pkg/models"
)

func TestCSVDataset(t *testing.T) {
	features := [][]string{
		{"5.1", "setosa", "red"},
		{"4.9", "versicolor", "blue"},
		{"6.2", "virginica", "red"},
	}
	targets := []string{"iris1", "iris2", "iris3"}

	dataset := NewCSVDataset(features, targets)

	// Test basic properties
	if dataset.GetSize() != 3 {
		t.<PERSON><PERSON><PERSON>("Expected size 3, got %d", dataset.GetSize())
	}

	expectedIndices := []int{0, 1, 2}
	if !reflect.DeepEqual(dataset.GetIndices(), expectedIndices) {
		t.<PERSON>rf("Expected indices %v, got %v", expectedIndices, dataset.GetIndices())
	}

	// Test target retrieval
	target, err := dataset.GetTarget(1)
	if err != nil {
		t.<PERSON>rf("Unexpected error getting target: %v", err)
	}
	if target != "iris2" {
		t.<PERSON><PERSON><PERSON>("Expected target 'iris2', got '%s'", target)
	}

	// Test feature value retrieval
	numericFeature := &models.Feature{
		Name:         "sepal_length",
		Type:         models.NumericFeature,
		ColumnNumber: 0,
	}

	value, err := dataset.GetFeatureValue(0, numericFeature)
	if err != nil {
		t.Errorf("Unexpected error getting feature value: %v", err)
	}

	// Should be converted to float64
	if floatVal, ok := value.(float64); !ok || floatVal != 5.1 {
		t.Errorf("Expected numeric value 5.1, got %v (type %T)", value, value)
	}

	// Test categorical feature
	categoricalFeature := &models.Feature{
		Name:         "species",
		Type:         models.CategoricalFeature,
		ColumnNumber: 1,
	}

	value, err = dataset.GetFeatureValue(1, categoricalFeature)
	if err != nil {
		t.Errorf("Unexpected error getting categorical feature: %v", err)
	}

	if strVal, ok := value.(string); !ok || strVal != "versicolor" {
		t.Errorf("Expected categorical value 'versicolor', got %v (type %T)", value, value)
	}
}

func TestCSVDatasetSubset(t *testing.T) {
	features := [][]string{
		{"1.0", "a"},
		{"2.0", "b"},
		{"3.0", "c"},
		{"4.0", "d"},
	}
	targets := []string{"x", "y", "z", "w"}

	dataset := NewCSVDataset(features, targets)

	// Create subset with indices [1, 3]
	subset := dataset.Subset([]int{1, 3})

	if subset.GetSize() != 2 {
		t.Errorf("Expected subset size 2, got %d", subset.GetSize())
	}

	expectedIndices := []int{1, 3}
	if !reflect.DeepEqual(subset.GetIndices(), expectedIndices) {
		t.Errorf("Expected subset indices %v, got %v", expectedIndices, subset.GetIndices())
	}

	// Test that subset still references original data
	target, err := subset.GetTarget(1)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	if target != "y" {
		t.Errorf("Expected target 'y', got '%s'", target)
	}
}

func TestCSVDatasetErrors(t *testing.T) {
	features := [][]string{
		{"1.0", "a"},
		{"2.0", "b"},
	}
	targets := []string{"x", "y"}

	dataset := NewCSVDataset(features, targets)

	// Test out of bounds sample index for target
	_, err := dataset.GetTarget(5)
	if err == nil {
		t.Error("Expected error for out of bounds target index")
	}

	// Test negative sample index for target
	_, err = dataset.GetTarget(-1)
	if err == nil {
		t.Error("Expected error for negative target index")
	}

	// Test out of bounds sample index for feature
	feature := &models.Feature{
		Name:         "test",
		Type:         models.NumericFeature,
		ColumnNumber: 0,
	}

	_, err = dataset.GetFeatureValue(5, feature)
	if err == nil {
		t.Error("Expected error for out of bounds feature sample index")
	}

	// Test out of bounds column number
	badFeature := &models.Feature{
		Name:         "test",
		Type:         models.NumericFeature,
		ColumnNumber: 10,
	}

	_, err = dataset.GetFeatureValue(0, badFeature)
	if err == nil {
		t.Error("Expected error for out of bounds feature column")
	}
}

func TestCSVDatasetPanic(t *testing.T) {
	// Since logger.Fatal() calls os.Exit(1), we can't test this directly in a unit test
	// without the process exiting. Instead, we test the validation logic indirectly
	// by ensuring that valid inputs work and documenting the expected behavior.

	// Test that valid inputs work (no exit)
	features := [][]string{
		{"1.0", "a"},
		{"2.0", "b"},
	}
	targets := []string{"x", "y"} // Matching length

	dataset := NewCSVDataset(features, targets)
	if dataset.GetSize() != 2 {
		t.Errorf("Expected size 2, got %d", dataset.GetSize())
	}

	// Note: Testing mismatched lengths would cause os.Exit(1) via logger.Fatal()
	// This is the expected behavior for this critical error condition.
}

// TestNewCSVDatasetWithDateTimeConversion tests datetime conversion at dataset creation
func TestNewCSVDatasetWithDateTimeConversion(t *testing.T) {
	features := [][]string{
		{"2023-01-15T09:30:00Z", "setosa", "25.5"},
		{"2023-06-20T14:45:30Z", "versicolor", "30.2"},
		{"2023-12-25T18:00:00Z", "virginica", "35.8"},
	}
	targets := []string{"class1", "class2", "class3"}

	// Create feature objects
	timestampFeature, err := models.NewFeature("timestamp", models.DateTimeFeature, 0)
	if err != nil {
		t.Fatalf("Failed to create timestamp feature: %v", err)
	}
	speciesFeature, err := models.NewFeature("species", models.CategoricalFeature, 1)
	if err != nil {
		t.Fatalf("Failed to create species feature: %v", err)
	}
	valueFeature, err := models.NewFeature("value", models.NumericFeature, 2)
	if err != nil {
		t.Fatalf("Failed to create value feature: %v", err)
	}

	featureObjects := []*models.Feature{timestampFeature, speciesFeature, valueFeature}

	dataset := NewCSVDatasetWithDateTimeConversion(features, targets, featureObjects)

	// Test basic properties
	if dataset.GetSize() != 3 {
		t.Errorf("Expected size 3, got %d", dataset.GetSize())
	}

	// Test that datetime feature was converted to int64
	timestampValue, err := dataset.GetFeatureValue(0, featureObjects[0])
	if err != nil {
		t.Errorf("Unexpected error getting timestamp: %v", err)
	}

	timestampInt, ok := timestampValue.(int64)
	if !ok {
		t.Errorf("Expected timestamp to be int64, got %T", timestampValue)
	}

	// Should be stored as integer value
	expectedTimestamp := int64(20230115093000)
	if timestampInt != expectedTimestamp {
		t.Errorf("Expected timestamp %d, got %d", expectedTimestamp, timestampInt)
	}

	// Test that non-datetime features remain unchanged
	speciesValue, err := dataset.GetFeatureValue(1, featureObjects[1])
	if err != nil {
		t.Errorf("Unexpected error getting species: %v", err)
	}
	if speciesValue != "versicolor" {
		t.Errorf("Expected species 'versicolor', got '%v'", speciesValue)
	}

	valueValue, err := dataset.GetFeatureValue(1, featureObjects[2])
	if err != nil {
		t.Errorf("Unexpected error getting value: %v", err)
	}
	// Numeric features should be converted to float64
	expectedValue := 30.2
	if valueValue != expectedValue {
		t.Errorf("Expected value %v, got %v (type %T)", expectedValue, valueValue, valueValue)
	}
}

// TestDateTimeConversionEfficiency tests that datetime conversion happens only once
func TestDateTimeConversionEfficiency(t *testing.T) {
	features := [][]string{
		{"2023-01-15T09:30:00Z", "A"},
		{"2023-06-20T14:45:30Z", "B"},
		{"2023-12-25T18:00:00Z", "C"},
	}
	targets := []string{"class1", "class2", "class3"}

	// Create feature objects
	timestampFeature, err := models.NewFeature("timestamp", models.DateTimeFeature, 0)
	if err != nil {
		t.Fatalf("Failed to create timestamp feature: %v", err)
	}
	categoryFeature, err := models.NewFeature("category", models.CategoricalFeature, 1)
	if err != nil {
		t.Fatalf("Failed to create category feature: %v", err)
	}

	featureObjects := []*models.Feature{timestampFeature, categoryFeature}

	// Create dataset with datetime conversion
	dataset := NewCSVDatasetWithDateTimeConversion(features, targets, featureObjects)

	// Verify that datetime values are converted to int64
	for i := 0; i < 3; i++ {
		timestampValue, err := dataset.GetFeatureValue(i, timestampFeature)
		if err != nil {
			t.Errorf("Error getting timestamp for row %d: %v", i, err)
			continue
		}

		// Should be int64
		if timestampInt, ok := timestampValue.(int64); !ok {
			t.Errorf("Row %d: Expected timestamp to be int64, got %T", i, timestampValue)
		} else {
			// Verify it's the correct integer value
			expectedValues := []int64{20230115093000, 20230620144530, 20231225180000}
			if timestampInt != expectedValues[i] {
				t.Errorf("Row %d: Expected timestamp %d, got %d", i, expectedValues[i], timestampInt)
			}
		}
	}

	// Verify that categorical values remain as strings
	for i := 0; i < 3; i++ {
		categoryValue, err := dataset.GetFeatureValue(i, categoryFeature)
		if err != nil {
			t.Errorf("Error getting category for row %d: %v", i, err)
			continue
		}

		// Should be string
		if _, ok := categoryValue.(string); !ok {
			t.Errorf("Row %d: Expected category to be string, got %T", i, categoryValue)
		}
	}
}

// TestNewCSVDatasetSmart tests the smart dataset creation with automatic fallback
func TestNewCSVDatasetSmart(t *testing.T) {
	features := [][]string{
		{"2023-01-15T09:30:00Z", "A", "25.5"},
		{"2023-06-20T14:45:30Z", "B", "30.2"},
	}
	targets := []string{"class1", "class2"}

	t.Run("with_datetime_features", func(t *testing.T) {
		// Create feature objects with datetime
		timestampFeature, _ := models.NewFeature("timestamp", models.DateTimeFeature, 0)
		categoryFeature, _ := models.NewFeature("category", models.CategoricalFeature, 1)
		valueFeature, _ := models.NewFeature("value", models.NumericFeature, 2)
		featureObjects := []*models.Feature{timestampFeature, categoryFeature, valueFeature}

		dataset := NewCSVDatasetSmart(features, targets, featureObjects)

		// Should use datetime conversion - verify timestamp is stored as integer string
		timestampValue, err := dataset.GetFeatureValue(0, timestampFeature)
		if err != nil {
			t.Errorf("Error getting timestamp: %v", err)
		}
		timestampInt, ok := timestampValue.(int64)
		if !ok {
			t.Errorf("Expected timestamp to be int64, got %T", timestampValue)
		}
		// Should be stored as integer value
		expectedTimestamp := int64(20230115093000)
		if timestampInt != expectedTimestamp {
			t.Errorf("Expected timestamp %d, got %d", expectedTimestamp, timestampInt)
		}
	})

	t.Run("without_datetime_features", func(t *testing.T) {
		// Create feature objects without datetime
		categoryFeature, _ := models.NewFeature("category", models.CategoricalFeature, 1)
		valueFeature, _ := models.NewFeature("value", models.NumericFeature, 2)
		featureObjects := []*models.Feature{categoryFeature, valueFeature}

		dataset := NewCSVDatasetSmart(features, targets, featureObjects)

		// Should fallback to basic dataset - verify timestamp remains as string
		// Note: We need to create a dummy feature to access the timestamp column
		timestampFeature, _ := models.NewFeature("timestamp", models.CategoricalFeature, 0)
		timestampValue, err := dataset.GetFeatureValue(0, timestampFeature)
		if err != nil {
			t.Errorf("Error getting timestamp: %v", err)
		}
		if timestampValue != "2023-01-15T09:30:00Z" {
			t.Errorf("Expected timestamp to remain as string, got %v", timestampValue)
		}
	})

	t.Run("nil_feature_objects", func(t *testing.T) {
		dataset := NewCSVDatasetSmart(features, targets, nil)

		// Should fallback to basic dataset
		if dataset.GetSize() != 2 {
			t.Errorf("Expected size 2, got %d", dataset.GetSize())
		}
	})

	t.Run("mismatched_feature_count", func(t *testing.T) {
		// Only provide 2 feature objects for 3 columns
		categoryFeature, _ := models.NewFeature("category", models.CategoricalFeature, 1)
		valueFeature, _ := models.NewFeature("value", models.NumericFeature, 2)
		featureObjects := []*models.Feature{categoryFeature, valueFeature}

		dataset := NewCSVDatasetSmart(features, targets, featureObjects)

		// Should fallback to basic dataset
		if dataset.GetSize() != 2 {
			t.Errorf("Expected size 2, got %d", dataset.GetSize())
		}
	})
}

func TestCSVDatasetNumericConversionFailure(t *testing.T) {
	features := [][]string{
		{"not_a_number", "a"},
	}
	targets := []string{"x"}

	dataset := NewCSVDataset(features, targets)

	numericFeature := &models.Feature{
		Name:         "test",
		Type:         models.NumericFeature,
		ColumnNumber: 0,
	}

	// Should return the original string value when conversion fails
	value, err := dataset.GetFeatureValue(0, numericFeature)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	if strVal, ok := value.(string); !ok || strVal != "not_a_number" {
		t.Errorf("Expected string value 'not_a_number', got %v (type %T)", value, value)
	}
}

// Package utils provides utilities for parsing and validating machine learning feature information files.
package utils

import (
	"fmt"
	"os"
	"strings"

	datetimeconverter "github.com/berrijam/mulberri/internals/utils/datetime_converter"
	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
	"gopkg.in/yaml.v3"
)

// FeatureInfo represents information for a single feature from YAML.
// Supports nominal features (with values list), numeric features (with optional min/max), and datetime features (with optional min/max).
type FeatureInfo struct {
	Type   string      `yaml:"type"`             // "nominal", "numeric", or "datetime"
	Values []string    `yaml:"values,omitempty"` // Valid values for nominal features
	Min    interface{} `yaml:"min,omitempty"`    // Minimum value for numeric/datetime features (float64 for numeric, string or float64 for datetime)
	Max    interface{} `yaml:"max,omitempty"`    // Maximum value for numeric/datetime features (float64 for numeric, string or float64 for datetime)
}

// FeatureInfoParser handles parsing and validation of feature information files.
type FeatureInfoParser struct{}

// FeatureInfoConfig maps feature names to their information definitions.
type FeatureInfoConfig map[string]FeatureInfo

// FeatureInfoError represents errors that occur during feature info processing
type FeatureInfoError struct {
	Op      string
	File    string
	Feature string
	Err     error
}

func (e *FeatureInfoError) Error() string {
	if e.Feature != "" {
		return fmt.Sprintf("feature info %s error for feature '%s' in file '%s': %v", e.Op, e.Feature, e.File, e.Err)
	}
	return fmt.Sprintf("feature info %s error in file '%s': %v", e.Op, e.File, e.Err)
}

func (e *FeatureInfoError) Unwrap() error {
	return e.Err
}

// ToModelFeatureType converts YAML type to models.FeatureType.
// Returns CategoricalFeature for "nominal", NumericFeature for "numeric",
// DateTimeFeature for "datetime", and CategoricalFeature as default fallback.
func (fi *FeatureInfo) ToModelFeatureType() models.FeatureType {
	switch fi.Type {
	case "nominal":
		return models.CategoricalFeature
	case "numeric":
		return models.NumericFeature
	case "datetime":
		return models.DateTimeFeature
	default:
		return models.CategoricalFeature
	}
}

// NewFeatureInfoParser creates a new parser.
func NewFeatureInfoParser() *FeatureInfoParser {
	return &FeatureInfoParser{}
}

// ParseFeatureInfoFile parses a YAML feature information file with graceful error handling.
// Returns (nil, nil) for missing files to enable fallback to automatic detection.
// Supports Unicode in feature names and values.
func (p *FeatureInfoParser) ParseFeatureInfoFile(filePath string) (*FeatureInfoConfig, error) {
	logger.Debug(fmt.Sprintf("Starting to parse feature info file: %s", filePath))

	// Validate file path first
	validatedPath, err := ValidateFeatureInfoFile(filePath)
	if err != nil {
		logger.Error(fmt.Sprintf("File validation failed: %v", err))
		return nil, &FeatureInfoError{
			Op:   "validate",
			File: filePath,
			Err:  err,
		}
	}
	filePath = validatedPath

	// Read file with specific error context
	logger.Debug(fmt.Sprintf("Reading file: %s", filePath))
	data, err := os.ReadFile(filePath)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to read file: %v", err))
		return nil, &FeatureInfoError{
			Op:   "read",
			File: filePath,
			Err:  err,
		}
	}

	// Check for empty file
	if len(data) == 0 {
		logger.Error("File is empty")
		return nil, &FeatureInfoError{
			Op:   "validate",
			File: filePath,
			Err:  fmt.Errorf("file is empty"),
		}
	}

	// Check for whitespace-only file
	if len(strings.TrimSpace(string(data))) == 0 {
		logger.Error("File contains only whitespace")
		return nil, &FeatureInfoError{
			Op:   "validate",
			File: filePath,
			Err:  fmt.Errorf("file contains only whitespace"),
		}
	}

	logger.Debug(fmt.Sprintf("File size: %d bytes", len(data)))
	var config FeatureInfoConfig

	// Parse YAML with specific error context
	logger.Debug("Parsing YAML content")
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		logger.Debug(fmt.Sprintf("YAML parsing failed: %v", err))
		return nil, &FeatureInfoError{
			Op:   "parse_yaml",
			File: filePath,
			Err:  err,
		}
	}

	// Validate configuration
	logger.Debug(fmt.Sprintf("Validating configuration with %d features", len(config)))
	err = p.validateConfig(config, filePath)
	if err != nil {
		logger.Error(fmt.Sprintf("Configuration validation failed: %v", err))
		return nil, err // Already wrapped in validateConfig
	}

	logger.Info(fmt.Sprintf("Successfully parsed feature info file: %s (%d features)", filePath, len(config)))
	return &config, nil
}

// validateConfig ensures configuration contains at least one feature and validates each one.
func (p *FeatureInfoParser) validateConfig(config FeatureInfoConfig, filePath string) error {
	if len(config) == 0 {
		return &FeatureInfoError{
			Op:   "validate",
			File: filePath,
			Err:  fmt.Errorf("feature info file is empty or contains no features"),
		}
	}

	for featureName, featureInfo := range config {
		if err := p.validateFeature(featureName, featureInfo, filePath); err != nil {
			return err
		}
	}
	return nil
}

// validateFeature validates a single feature's information according to its type.
// Supports Unicode in feature names. Enforces type-specific constraints.
func (p *FeatureInfoParser) validateFeature(featureName string, featureInfo FeatureInfo, filePath string) error {
	// Validate feature name
	if strings.TrimSpace(featureName) == "" {
		return &FeatureInfoError{
			Op:      "validate",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("feature name cannot be empty or whitespace-only"),
		}
	}

	// Validate type field
	if strings.TrimSpace(featureInfo.Type) == "" {
		return &FeatureInfoError{
			Op:      "validate",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("must have a type field"),
		}
	}

	if featureInfo.Type != "nominal" && featureInfo.Type != "numeric" && featureInfo.Type != "datetime" {
		return &FeatureInfoError{
			Op:      "validate",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("invalid type '%s': must be 'nominal', 'numeric', or 'datetime'", featureInfo.Type),
		}
	}

	// Type-specific validation
	switch featureInfo.Type {
	case "nominal":
		return p.validateNominalFeature(featureName, featureInfo, filePath)
	case "numeric":
		return p.validateNumericFeature(featureName, featureInfo, filePath)
	case "datetime":
		return p.validateDateTimeFeatureConfig(featureName, featureInfo, filePath)
	}
	return nil
}

// validateNominalFeature validates nominal (categorical) feature requirements in YAML files.
// This validates the YAML schema/configuration, not runtime data values.
// Must have non-empty values list, no duplicates, no min/max properties.
func (p *FeatureInfoParser) validateNominalFeature(featureName string, featureInfo FeatureInfo, filePath string) error {
	if len(featureInfo.Values) == 0 {
		return &FeatureInfoError{
			Op:      "validate",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("must have a non-empty values list"),
		}
	}

	// Check for empty values and duplicates
	valueSet := make(map[string]bool)
	for i, value := range featureInfo.Values {
		if strings.TrimSpace(value) == "" {
			return &FeatureInfoError{
				Op:      "validate",
				File:    filePath,
				Feature: featureName,
				Err:     fmt.Errorf("value at index %d cannot be empty or whitespace-only", i),
			}
		}

		if valueSet[value] {
			return &FeatureInfoError{
				Op:      "validate",
				File:    filePath,
				Feature: featureName,
				Err:     fmt.Errorf("duplicate value '%s' at index %d", value, i),
			}
		}
		valueSet[value] = true
	}

	// Nominal features should not have numeric constraints
	if featureInfo.Min != nil || featureInfo.Max != nil {
		return &FeatureInfoError{
			Op:      "validate",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("should not have min or max properties"),
		}
	}

	return nil
}

// validateNumericFeature validates numeric (continuous) feature requirements in YAML files.
// This validates the YAML schema/configuration, not runtime data values.
// Cannot have values list. If min/max specified, min must be < max.
func (p *FeatureInfoParser) validateNumericFeature(featureName string, featureInfo FeatureInfo, filePath string) error {
	if len(featureInfo.Values) > 0 {
		return &FeatureInfoError{
			Op:      "validate",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("should not have a values list"),
		}
	}

	// Validate min/max relationship if both specified
	if featureInfo.Min != nil && featureInfo.Max != nil {
		// Convert to float64 for comparison
		minFloat, minOk := featureInfo.Min.(float64)
		maxFloat, maxOk := featureInfo.Max.(float64)

		if !minOk || !maxOk {
			return &FeatureInfoError{
				Op:      "validate",
				File:    filePath,
				Feature: featureName,
				Err:     fmt.Errorf("numeric features require numeric min/max values"),
			}
		}

		if minFloat > maxFloat {
			return &FeatureInfoError{
				Op:      "validate",
				File:    filePath,
				Feature: featureName,
				Err:     fmt.Errorf("min value (%.6f) cannot be greater than max value (%.6f)", minFloat, maxFloat),
			}
		}
		if minFloat == maxFloat {
			return &FeatureInfoError{
				Op:      "validate",
				File:    filePath,
				Feature: featureName,
				Err:     fmt.Errorf("min and max values cannot be equal (%.6f)", minFloat),
			}
		}
	}

	return nil
}

// validateDateTimeFeatureConfig validates datetime feature configuration requirements in YAML files.
// This validates the YAML schema/configuration, not runtime data values.
// DateTime features should not have values list, but can have min/max for range validation.
// Note: This is different from data_validator.validateDateTimeFeature() which validates runtime data values.
func (p *FeatureInfoParser) validateDateTimeFeatureConfig(featureName string, featureInfo FeatureInfo, filePath string) error {
	// DateTime features should not have a values list
	if len(featureInfo.Values) > 0 {
		return &FeatureInfoError{
			Op:      "validate",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("should not have a values list"),
		}
	}

	// Validate min/max constraints if provided
	if featureInfo.Min != nil && featureInfo.Max != nil {
		// Convert min/max values to int64 for validation (handle both string and numeric formats)
		var minInt, maxInt int64
		var err error

		// Handle min value - could be string or int
		switch minVal := featureInfo.Min.(type) {
		case string:
			minInt, err = datetimeconverter.GetGlobalConverter().ConvertISO8601ToInt(minVal)
			if err != nil {
				return &FeatureInfoError{
					Op:      "validate",
					File:    filePath,
					Feature: featureName,
					Err:     fmt.Errorf("invalid min datetime format: %v", err),
				}
			}
		case int:
			minInt = int64(minVal)
		case int64:
			minInt = minVal
		default:
			return &FeatureInfoError{
				Op:      "validate",
				File:    filePath,
				Feature: featureName,
				Err:     fmt.Errorf("min value must be string or integer, got %T", featureInfo.Min),
			}
		}

		// Handle max value - could be string or int
		switch maxVal := featureInfo.Max.(type) {
		case string:
			maxInt, err = datetimeconverter.GetGlobalConverter().ConvertISO8601ToInt(maxVal)
			if err != nil {
				return &FeatureInfoError{
					Op:      "validate",
					File:    filePath,
					Feature: featureName,
					Err:     fmt.Errorf("invalid max datetime format: %v", err),
				}
			}
		case int:
			maxInt = int64(maxVal)
		case int64:
			maxInt = maxVal
		default:
			return &FeatureInfoError{
				Op:      "validate",
				File:    filePath,
				Feature: featureName,
				Err:     fmt.Errorf("max value must be string or integer, got %T", featureInfo.Max),
			}
		}

		if minInt > maxInt {
			return &FeatureInfoError{
				Op:      "validate",
				File:    filePath,
				Feature: featureName,
				Err:     fmt.Errorf("min value (%d) cannot be greater than max value (%d)", minInt, maxInt),
			}
		}
		if minInt == maxInt {
			return &FeatureInfoError{
				Op:      "validate",
				File:    filePath,
				Feature: featureName,
				Err:     fmt.Errorf("min and max values cannot be equal (%d)", minInt),
			}
		}
	}

	return nil
}



// ValidateFeatureInfoAgainstCSV validates that feature info matches CSV headers and provides useful validation.
// This helps catch mismatches between feature info and actual data structure.
func (p *FeatureInfoParser) ValidateFeatureInfoAgainstCSV(config *FeatureInfoConfig, csvHeaders []string, filePath string) error {
	return p.ValidateFeatureInfoAgainstCSVWithTarget(config, csvHeaders, "", filePath)
}

// ValidateFeatureInfoAgainstCSVWithTarget validates that feature info matches CSV headers, excluding the target column.
// This helps catch mismatches between feature info and actual data structure while allowing target column feature info.
func (p *FeatureInfoParser) ValidateFeatureInfoAgainstCSVWithTarget(config *FeatureInfoConfig, csvHeaders []string, targetColumn string, filePath string) error {

	if config == nil {
		logger.Error("Feature config is nil")
		return &FeatureInfoError{
			Op:   "validate_csv",
			File: filePath,
			Err:  fmt.Errorf("feature config is nil"),
		}
	}
	logger.Debug(fmt.Sprintf("Validating feature config against CSV headers: %d features vs %d headers, target='%s'", len(*config), len(csvHeaders), targetColumn))

	// Create a map of CSV headers for quick lookup
	csvHeaderMap := make(map[string]bool)
	for _, header := range csvHeaders {
		csvHeaderMap[header] = true
	}

	// Add target column to the map if specified (so it's not considered missing)
	if targetColumn != "" {
		csvHeaderMap[targetColumn] = true
	}

	// Check if all features in config exist in CSV
	var missingInCSV []string
	for featureName := range *config {
		if !csvHeaderMap[featureName] {
			missingInCSV = append(missingInCSV, featureName)
		}
	}

	if len(missingInCSV) > 0 {
		logger.Error(fmt.Sprintf("Features missing in CSV: %v", missingInCSV))
		return &FeatureInfoError{
			Op:   "validate_csv",
			File: filePath,
			Err:  fmt.Errorf("features defined in feature info but missing in CSV: %v", missingInCSV),
		}
	}

	logger.Debug("Feature config validation against CSV successful")
	// Note: We don't require all CSV columns to be in feature info to allow for partial feature definitions
	return nil
}

# DateTime Converter Utility

A Go utility for converting ISO 8601 formatted date/time strings into numeric formats suitable for data processing and machine learning applications.

## Overview

This utility focuses on converting ISO 8601 formatted strings in the format `YYYY-MM-DDTHH:mm:ss.sssZ` (where T separates date and time, and Z indicates UTC or timezone offset) into compact numeric representations.

## Features

- **Date-only conversion**: `YYYY-MM-DD` → `YYYYMMDD` (8-digit number)
- **Time-only conversion**: `HH:mm:ss.sssZ` → `HHMMSS` (6-digit number, milliseconds ignored)
- **DateTime conversion**: `YYYY-MM-DDTHH:mm:ss.sssZ` → `YYYYMMDDHHMMSS` (14-digit number)
- **UTC normalization**: All times are converted to UTC before formatting
- **Timezone support**: Handles timezone offsets (e.g., `+05:30`, `-08:00`)
- **Comprehensive validation**: Input format validation with detailed error messages
- **High performance**: Optimized for processing large datasets

## Dependencies

This utility uses the [github.com/relvacode/iso8601](https://github.com/relvacode/iso8601) library for robust ISO 8601 parsing.

## Usage

### Basic Usage

```go
package main

import (
    "fmt"
    "log"
    
	datetimeconverter "github.com/berrijam/mulberri/internals/utils/datetime_converter"
)

func main() {
    // Create a new converter instance
    converter := datetimeconverter.NewDateTimeConverter()
    
    // Convert different types of inputs
    
    // Date only
    result, err := converter.ConvertISO8601("2023-12-25")
    if err != nil {
        log.Fatal(err)
    }
    fmt.Println(result) // Output: 20231225
    
    // Time only
    result, err = converter.ConvertISO8601("14:30:45Z")
    if err != nil {
        log.Fatal(err)
    }
    fmt.Println(result) // Output: 143045
    
    // DateTime
    result, err = converter.ConvertISO8601("2023-12-25T14:30:45.123Z")
    if err != nil {
        log.Fatal(err)
    }
    fmt.Println(result) // Output: 20231225143045
}
```





## Supported Input Formats

### Date Only
- `2023-12-25`

### Time Only
- `14:30:45Z` (UTC)
- `14:30:45.123Z` (with milliseconds)
- `14:30:45+05:30` (with timezone offset)
- `14:30:45-08:00` (with negative timezone offset)

### DateTime
- `2023-12-25T14:30:45Z`
- `2023-12-25T14:30:45.123Z`
- `2023-12-25T14:30:45+05:30`
- `2023-12-25T14:30:45.123-08:00`

## Conversion Examples

| Input | Type | Output |
|-------|------|--------|
| `2023-12-25` | Date | `20231225` |
| `14:30:45Z` | Time | `143045` |
| `09:15:30+05:30` | Time | `34530` | 
| `2023-12-25T14:30:45Z` | DateTime | `20231225143045` ||
| `2023-06-15T09:30:45+05:30` | DateTime | `20230615040045` | 



## Error Handling

The utility provides detailed error messages for various failure scenarios:

```go
converter := datetimeconverter.NewDateTimeConverter()

// Empty input
_, err := converter.ConvertISO8601ToInt("")
// Error: datetime conversion failed for input '': input cannot be empty

// Invalid format
_, err = converter.ConvertISO8601ToInt("2023/12/25")
// Error: datetime conversion failed for input '2023/12/25': input does not match expected ISO 8601 format

// Time without timezone
_, err = converter.ConvertISO8601ToInt("14:30:45")
// Error: datetime conversion failed for input '14:30:45': input does not match expected ISO 8601 format
```

## Use Cases

This utility is particularly useful for:

- **Data preprocessing**: Converting date/time strings in datasets before ML training
- **Feature engineering**: Creating numeric date/time features for machine learning models
- **Data storage optimization**: Storing date/time values as compact integers
- **ETL pipelines**: Transforming date/time data during data processing workflows
- **Time series analysis**: Converting timestamps to numeric format for analysis

## Testing

The utility includes comprehensive tests covering:
- All supported input formats
- Timezone conversion accuracy
- Error handling scenarios
- Edge cases (leap years, midnight, end of day)
- Performance benchmarks

Run tests with:
```bash
go test ./internals/utils -v -run "TestDateTimeConverter|TestConvertISO8601"
```

## Example Program

See `examples/datetime_converter_example.go` for a complete example demonstrating all features of the utility.

```bash
go run examples/datetime_converter_example.go
```

package datetimeconverter

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewDateTimeConverter(t *testing.T) {
	converter := NewDateTimeConverter()
	assert.NotNil(t, converter)
	assert.NotNil(t, converter.dateOnlyRegex)
	assert.NotNil(t, converter.timeOnlyRegex)
	assert.NotNil(t, converter.dateTimeRegex)
}

func TestConvertISO8601_DateOnly(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected int64
		hasError bool
	}{
		{
			name:     "valid date",
			input:    "2023-12-25",
			expected: 20231225,
			hasError: false,
		},
		{
			name:     "valid date with leading zeros",
			input:    "2023-01-05",
			expected: 20230105,
			hasError: false,
		},
		{
			name:     "leap year date",
			input:    "2024-02-29",
			expected: 20240229,
			hasError: false,
		},
		{
			name:     "invalid date format",
			input:    "2023/12/25",
			expected: 0,
			hasError: true,
		},
		{
			name:     "invalid date - too short",
			input:    "23-12-25",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertISO8601_TimeOnly(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected int64
		hasError bool
	}{
		{
			name:     "time with milliseconds and Z",
			input:    "14:30:45.123Z",
			expected: 143045,
			hasError: false,
		},
		{
			name:     "time without milliseconds and Z",
			input:    "14:30:45Z",
			expected: 143045,
			hasError: false,
		},
		{
			name:     "time with timezone offset",
			input:    "14:30:45.123+05:30",
			expected: 90045, // Converted to UTC (14:30 - 5:30 = 09:00)
			hasError: false,
		},
		{
			name:     "time with negative timezone offset",
			input:    "14:30:45.123-08:00",
			expected: 223045, // Converted to UTC (14:30 + 8:00 = 22:30)
			hasError: false,
		},
		{
			name:     "midnight time",
			input:    "00:00:00Z",
			expected: 0,
			hasError: false,
		},
		{
			name:     "end of day time",
			input:    "23:59:59Z",
			expected: 235959,
			hasError: false,
		},
		{
			name:     "invalid time format - no timezone",
			input:    "14:30:45",
			expected: 0,
			hasError: true,
		},
		{
			name:     "invalid time format - wrong separator",
			input:    "14.30.45Z",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertISO8601ToInt_DateTime(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected int64
		hasError bool
	}{
		{
			name:     "full datetime with milliseconds and Z",
			input:    "2023-12-25T14:30:45.123Z",
			expected: 20231225143045,
			hasError: false,
		},
		{
			name:     "full datetime without milliseconds and Z",
			input:    "2023-12-25T14:30:45Z",
			expected: 20231225143045,
			hasError: false,
		},
		{
			name:     "datetime with timezone offset",
			input:    "2023-12-25T14:30:45.123+05:30",
			expected: 20231225090045, // Converted to UTC (14:30 - 5:30 = 09:00)
			hasError: false,
		},
		{
			name:     "datetime with negative timezone offset",
			input:    "2023-12-25T14:30:45.123-08:00",
			expected: 20231225223045, // Converted to UTC (14:30 + 8:00 = 22:30)
			hasError: false,
		},
		{
			name:     "new year datetime",
			input:    "2024-01-01T00:00:00Z",
			expected: 20240101000000,
			hasError: false,
		},
		{
			name:     "invalid datetime - missing T separator",
			input:    "2023-12-25 14:30:45Z",
			expected: 0,
			hasError: true,
		},
		{
			name:     "invalid datetime - wrong date format",
			input:    "2023/12/25T14:30:45Z",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertISO8601ToInt_EdgeCases(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected int64
		hasError bool
	}{
		{
			name:     "empty string",
			input:    "",
			expected: 0,
			hasError: true,
		},
		{
			name:     "whitespace only",
			input:    "   ",
			expected: 0,
			hasError: true,
		},
		{
			name:     "string with leading/trailing whitespace",
			input:    "  2023-12-25  ",
			expected: 20231225,
			hasError: false,
		},
		{
			name:     "invalid format",
			input:    "not-a-date",
			expected: 0,
			hasError: true,
		},
		{
			name:     "partial date",
			input:    "2023-12",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestDateTimeConversionError(t *testing.T) {
	// Test error without wrapped error
	err1 := &DateTimeConversionError{
		Input:  "invalid-input",
		Reason: "test reason",
	}
	assert.Contains(t, err1.Error(), "invalid-input")
	assert.Contains(t, err1.Error(), "test reason")
	assert.Nil(t, err1.Unwrap())

	// Test error with wrapped error
	wrappedErr := assert.AnError
	err2 := &DateTimeConversionError{
		Input:  "invalid-input",
		Reason: "test reason",
		Err:    wrappedErr,
	}
	assert.Contains(t, err2.Error(), "invalid-input")
	assert.Contains(t, err2.Error(), "test reason")
	assert.Equal(t, wrappedErr, err2.Unwrap())
}

// Benchmark tests
func BenchmarkConvertISO8601ToInt_Date(b *testing.B) {
	converter := NewDateTimeConverter()
	input := "2023-12-25"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converter.ConvertISO8601ToInt(input)
	}
}

func BenchmarkConvertISO8601ToInt_Time(b *testing.B) {
	converter := NewDateTimeConverter()
	input := "14:30:45.123Z"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converter.ConvertISO8601ToInt(input)
	}
}

func BenchmarkConvertISO8601ToInt_DateTime(b *testing.B) {
	converter := NewDateTimeConverter()
	input := "2023-12-25T14:30:45.123Z"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converter.ConvertISO8601ToInt(input)
	}
}

// Additional tests to improve coverage
func TestConvertISO8601ToInt_ErrorPaths(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name        string
		input       string
		expectError bool
		description string
	}{
		{
			name:        "invalid_datetime_parsing",
			input:       "2023-13-45T25:70:90Z", // Invalid date and time components
			expectError: true,
			description: "Should fail on invalid datetime components",
		},
		{
			name:        "invalid_date_parsing",
			input:       "2023-02-30", // Invalid date (Feb 30th doesn't exist)
			expectError: true,
			description: "Should fail on invalid date",
		},
		{
			name:        "malformed_timezone",
			input:       "14:30:45+25:70", // Invalid timezone offset - but regex allows it
			expectError: false,            // The regex validation passes, but parsing might handle it
			description: "Malformed timezone might be handled by the parser",
		},
		{
			name:        "whitespace_input",
			input:       "   2023-12-25   ", // Input with whitespace
			expectError: false,
			description: "Should handle whitespace correctly",
		},
		{
			name:        "only_whitespace",
			input:       "   ", // Only whitespace
			expectError: true,
			description: "Should fail on whitespace-only input",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tt.input)

			if tt.expectError {
				assert.Error(t, err, tt.description)
				assert.Equal(t, int64(0), result, "Should return 0 on error")
				assert.IsType(t, &DateTimeConversionError{}, err, "Should return DateTimeConversionError")
			} else {
				assert.NoError(t, err, tt.description)
				assert.Greater(t, result, int64(0), "Should return positive result on success")
			}
		})
	}
}

func TestConvertISO8601ToInt_EdgeCasesExtended(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected int64
		hasError bool
	}{
		{
			name:     "leap_year_valid",
			input:    "2024-02-29",
			expected: 20240229,
			hasError: false,
		},
		{
			name:     "leap_year_invalid",
			input:    "2023-02-29", // 2023 is not a leap year
			expected: 0,
			hasError: true,
		},
		{
			name:     "year_boundary",
			input:    "1999-12-31T23:59:59Z",
			expected: 19991231235959,
			hasError: false,
		},
		{
			name:     "millennium_boundary",
			input:    "2000-01-01T00:00:00Z",
			expected: 20000101000000,
			hasError: false,
		},
		{
			name:     "far_future_date",
			input:    "2099-12-31",
			expected: 20991231,
			hasError: false,
		},
		{
			name:     "time_with_large_offset",
			input:    "12:00:00+12:00", // UTC-12 to UTC+12 range
			expected: 0,                // 12:00 - 12:00 = 00:00
			hasError: false,
		},
		{
			name:     "time_with_negative_large_offset",
			input:    "12:00:00-12:00", // UTC-12 to UTC+12 range
			expected: 0,                // 12:00 + 12:00 = 24:00, but wraps to 00:00 for time-only
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.Equal(t, int64(0), result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestDateTimeConversionError_Coverage(t *testing.T) {
	// Test error without wrapped error
	err1 := &DateTimeConversionError{
		Input:  "test-input",
		Reason: "test reason",
	}

	errorMsg := err1.Error()
	assert.Contains(t, errorMsg, "test-input")
	assert.Contains(t, errorMsg, "test reason")
	assert.Nil(t, err1.Unwrap())

	// Test error with wrapped error
	wrappedErr := fmt.Errorf("wrapped error")
	err2 := &DateTimeConversionError{
		Input:  "test-input",
		Reason: "test reason",
		Err:    wrappedErr,
	}

	errorMsg2 := err2.Error()
	assert.Contains(t, errorMsg2, "test-input")
	assert.Contains(t, errorMsg2, "test reason")
	assert.Contains(t, errorMsg2, "wrapped error")
	assert.Equal(t, wrappedErr, err2.Unwrap())
}

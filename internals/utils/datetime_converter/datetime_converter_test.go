package datetimeconverter

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewDateTimeConverter(t *testing.T) {
	converter := NewDateTimeConverter()
	assert.NotNil(t, converter)
	assert.NotNil(t, converter.dateOnlyRegex)
	assert.NotNil(t, converter.timeOnlyRegex)
	assert.NotNil(t, converter.dateTimeRegex)
}

func TestConvertISO8601_DateOnly(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected int64
		hasError bool
	}{
		{
			name:     "valid date",
			input:    "2023-12-25",
			expected: 20231225,
			hasError: false,
		},
		{
			name:     "valid date with leading zeros",
			input:    "2023-01-05",
			expected: 20230105,
			hasError: false,
		},
		{
			name:     "leap year date",
			input:    "2024-02-29",
			expected: 20240229,
			hasError: false,
		},
		{
			name:     "invalid date format",
			input:    "2023/12/25",
			expected: 0,
			hasError: true,
		},
		{
			name:     "invalid date - too short",
			input:    "23-12-25",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertISO8601_TimeOnly(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected int64
		hasError bool
	}{
		{
			name:     "time with milliseconds and Z",
			input:    "14:30:45.123Z",
			expected: 143045,
			hasError: false,
		},
		{
			name:     "time without milliseconds and Z",
			input:    "14:30:45Z",
			expected: 143045,
			hasError: false,
		},
		{
			name:     "time with timezone offset",
			input:    "14:30:45.123+05:30",
			expected: 90045, // Converted to UTC (14:30 - 5:30 = 09:00)
			hasError: false,
		},
		{
			name:     "time with negative timezone offset",
			input:    "14:30:45.123-08:00",
			expected: 223045, // Converted to UTC (14:30 + 8:00 = 22:30)
			hasError: false,
		},
		{
			name:     "midnight time",
			input:    "00:00:00Z",
			expected: 0,
			hasError: false,
		},
		{
			name:     "end of day time",
			input:    "23:59:59Z",
			expected: 235959,
			hasError: false,
		},
		{
			name:     "time without timezone (treated as UTC)",
			input:    "14:30:45",
			expected: 143045,
			hasError: false,
		},
		{
			name:     "time with milliseconds without timezone",
			input:    "00:02:15.000",
			expected: 215,
			hasError: false,
		},
		{
			name:     "invalid time format - wrong separator",
			input:    "14.30.45Z",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertISO8601ToInt_DateTime(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected int64
		hasError bool
	}{
		{
			name:     "full datetime with milliseconds and Z",
			input:    "2023-12-25T14:30:45.123Z",
			expected: 20231225143045,
			hasError: false,
		},
		{
			name:     "full datetime without milliseconds and Z",
			input:    "2023-12-25T14:30:45Z",
			expected: 20231225143045,
			hasError: false,
		},
		{
			name:     "datetime with timezone offset",
			input:    "2023-12-25T14:30:45.123+05:30",
			expected: 20231225090045, // Converted to UTC (14:30 - 5:30 = 09:00)
			hasError: false,
		},
		{
			name:     "datetime with negative timezone offset",
			input:    "2023-12-25T14:30:45.123-08:00",
			expected: 20231225223045, // Converted to UTC (14:30 + 8:00 = 22:30)
			hasError: false,
		},
		{
			name:     "new year datetime",
			input:    "2024-01-01T00:00:00Z",
			expected: 20240101000000,
			hasError: false,
		},
		{
			name:     "invalid datetime - missing T separator",
			input:    "2023-12-25 14:30:45Z",
			expected: 0,
			hasError: true,
		},
		{
			name:     "invalid datetime - wrong date format",
			input:    "2023/12/25T14:30:45Z",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertISO8601ToInt_EdgeCases(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected int64
		hasError bool
	}{
		{
			name:     "empty string",
			input:    "",
			expected: 0,
			hasError: true,
		},
		{
			name:     "whitespace only",
			input:    "   ",
			expected: 0,
			hasError: true,
		},
		{
			name:     "string with leading/trailing whitespace",
			input:    "  2023-12-25  ",
			expected: 20231225,
			hasError: false,
		},
		{
			name:     "invalid format",
			input:    "not-a-date",
			expected: 0,
			hasError: true,
		},
		{
			name:     "partial date",
			input:    "2023-12",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestDateTimeConversionError(t *testing.T) {
	// Test error without wrapped error
	err1 := &DateTimeConversionError{
		Input:  "invalid-input",
		Reason: "test reason",
	}
	assert.Contains(t, err1.Error(), "invalid-input")
	assert.Contains(t, err1.Error(), "test reason")
	assert.Nil(t, err1.Unwrap())

	// Test error with wrapped error
	wrappedErr := assert.AnError
	err2 := &DateTimeConversionError{
		Input:  "invalid-input",
		Reason: "test reason",
		Err:    wrappedErr,
	}
	assert.Contains(t, err2.Error(), "invalid-input")
	assert.Contains(t, err2.Error(), "test reason")
	assert.Equal(t, wrappedErr, err2.Unwrap())
}

// Benchmark tests
func BenchmarkConvertISO8601ToInt_Date(b *testing.B) {
	converter := NewDateTimeConverter()
	input := "2023-12-25"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converter.ConvertISO8601ToInt(input)
	}
}

func BenchmarkConvertISO8601ToInt_Time(b *testing.B) {
	converter := NewDateTimeConverter()
	input := "14:30:45.123Z"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converter.ConvertISO8601ToInt(input)
	}
}

func BenchmarkConvertISO8601ToInt_DateTime(b *testing.B) {
	converter := NewDateTimeConverter()
	input := "2023-12-25T14:30:45.123Z"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = converter.ConvertISO8601ToInt(input)
	}
}

// Additional tests to improve coverage
func TestConvertISO8601ToInt_ErrorPaths(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name        string
		input       string
		expectError bool
		description string
	}{
		{
			name:        "invalid_datetime_parsing",
			input:       "2023-13-45T25:70:90Z", // Invalid date and time components
			expectError: true,
			description: "Should fail on invalid datetime components",
		},
		{
			name:        "invalid_date_parsing",
			input:       "2023-02-30", // Invalid date (Feb 30th doesn't exist)
			expectError: true,
			description: "Should fail on invalid date",
		},
		{
			name:        "malformed_timezone",
			input:       "14:30:45+25:70", // Invalid timezone offset - but regex allows it
			expectError: false,            // The regex validation passes, but parsing might handle it
			description: "Malformed timezone might be handled by the parser",
		},
		{
			name:        "whitespace_input",
			input:       "   2023-12-25   ", // Input with whitespace
			expectError: false,
			description: "Should handle whitespace correctly",
		},
		{
			name:        "only_whitespace",
			input:       "   ", // Only whitespace
			expectError: true,
			description: "Should fail on whitespace-only input",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tt.input)

			if tt.expectError {
				assert.Error(t, err, tt.description)
				assert.Equal(t, int64(0), result, "Should return 0 on error")
				assert.IsType(t, &DateTimeConversionError{}, err, "Should return DateTimeConversionError")
			} else {
				assert.NoError(t, err, tt.description)
				assert.Greater(t, result, int64(0), "Should return positive result on success")
			}
		})
	}
}

func TestConvertISO8601ToInt_EdgeCasesExtended(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected int64
		hasError bool
	}{
		{
			name:     "leap_year_valid",
			input:    "2024-02-29",
			expected: 20240229,
			hasError: false,
		},
		{
			name:     "leap_year_invalid",
			input:    "2023-02-29", // 2023 is not a leap year
			expected: 0,
			hasError: true,
		},
		{
			name:     "year_boundary",
			input:    "1999-12-31T23:59:59Z",
			expected: 19991231235959,
			hasError: false,
		},
		{
			name:     "millennium_boundary",
			input:    "2000-01-01T00:00:00Z",
			expected: 20000101000000,
			hasError: false,
		},
		{
			name:     "far_future_date",
			input:    "2099-12-31",
			expected: 20991231,
			hasError: false,
		},
		{
			name:     "time_with_large_offset",
			input:    "12:00:00+12:00", // UTC-12 to UTC+12 range
			expected: 0,                // 12:00 - 12:00 = 00:00
			hasError: false,
		},
		{
			name:     "time_with_negative_large_offset",
			input:    "12:00:00-12:00", // UTC-12 to UTC+12 range
			expected: 0,                // 12:00 + 12:00 = 24:00, but wraps to 00:00 for time-only
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.Equal(t, int64(0), result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestDateTimeConversionError_Coverage(t *testing.T) {
	// Test error without wrapped error
	err1 := &DateTimeConversionError{
		Input:  "test-input",
		Reason: "test reason",
	}

	errorMsg := err1.Error()
	assert.Contains(t, errorMsg, "test-input")
	assert.Contains(t, errorMsg, "test reason")
	assert.Nil(t, err1.Unwrap())

	// Test error with wrapped error
	wrappedErr := fmt.Errorf("wrapped error")
	err2 := &DateTimeConversionError{
		Input:  "test-input",
		Reason: "test reason",
		Err:    wrappedErr,
	}

	errorMsg2 := err2.Error()
	assert.Contains(t, errorMsg2, "test-input")
	assert.Contains(t, errorMsg2, "test reason")
	assert.Contains(t, errorMsg2, "wrapped error")
	assert.Equal(t, wrappedErr, err2.Unwrap())
}

// Testcenarios tests specific scenarios mentioned in the code review
func TestScenarios(t *testing.T) {
	converter := NewDateTimeConverter()

	// Test data that validates the architectural changes from the code review
	tests := []struct {
		name        string
		input       string
		expected    int64
		hasError    bool
		description string
	}{
		// Test precision preservation (no floating-point loss)
		{
			name:        "precision_high_resolution_datetime",
			input:       "2023-12-31T23:59:59.999Z",
			expected:    20231231235959, // Milliseconds are ignored, but no precision loss in integer conversion
			hasError:    false,
			description: "High resolution datetime should preserve integer precision",
		},
		{
			name:        "precision_edge_case_year",
			input:       "9999-12-31T23:59:59Z",
			expected:    99991231235959,
			hasError:    false,
			description: "Large year values should not lose precision",
		},

		// Test immediate validation (no type guessing)
		{
			name:        "immediate_validation_invalid_format",
			input:       "2023-12-25 14:30:45", // Space instead of T
			expected:    0,
			hasError:    true,
			description: "Should immediately reject invalid format without guessing",
		},
		{
			name:        "immediate_validation_partial_match",
			input:       "2023-12-25T", // Incomplete datetime
			expected:    0,
			hasError:    true,
			description: "Should immediately reject partial datetime formats",
		},

		// Test conversion consistency for tree building
		{
			name:        "consistency_date_boundary",
			input:       "2023-12-31",
			expected:    20231231,
			hasError:    false,
			description: "Date boundary conversion should be consistent",
		},
		{
			name:        "consistency_time_boundary",
			input:       "23:59:59Z",
			expected:    235959,
			hasError:    false,
			description: "Time boundary conversion should be consistent",
		},
		{
			name:        "consistency_datetime_boundary",
			input:       "2023-12-31T23:59:59Z",
			expected:    20231231235959,
			hasError:    false,
			description: "DateTime boundary conversion should be consistent",
		},

		// Test timezone normalization to UTC
		{
			name:        "utc_normalization_positive_offset",
			input:       "2023-12-25T15:30:45+01:00",
			expected:    20231225143045, // 15:30 - 1:00 = 14:30 UTC
			hasError:    false,
			description: "Positive timezone offset should normalize to UTC",
		},
		{
			name:        "utc_normalization_negative_offset",
			input:       "2023-12-25T15:30:45-05:00",
			expected:    20231225203045, // 15:30 + 5:00 = 20:30 UTC
			hasError:    false,
			description: "Negative timezone offset should normalize to UTC",
		},
		{
			name:        "utc_normalization_date_rollover",
			input:       "2023-12-25T02:30:45+05:00",
			expected:    20231224213045, // 02:30 - 5:00 = 21:30 previous day UTC
			hasError:    false,
			description: "Timezone conversion should handle date rollover",
		},

		// Test integer arithmetic scenarios (avoiding floating-point)
		{
			name:        "integer_arithmetic_large_values",
			input:       "2099-12-31T23:59:59Z",
			expected:    20991231235959,
			hasError:    false,
			description: "Large integer values should be handled without floating-point conversion",
		},
		{
			name:        "integer_arithmetic_small_values",
			input:       "1900-01-01T00:00:00Z",
			expected:    19000101000000,
			hasError:    false,
			description: "Small integer values should be handled consistently",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt(tt.input)

			if tt.hasError {
				assert.Error(t, err, tt.description)
				assert.Equal(t, int64(0), result, "Should return 0 on error")
				assert.IsType(t, &DateTimeConversionError{}, err, "Should return DateTimeConversionError")
			} else {
				assert.NoError(t, err, tt.description)
				assert.Equal(t, tt.expected, result, tt.description)

				// Additional validation: ensure result is a valid integer representation
				assert.Greater(t, result, int64(0), "Valid datetime should produce positive integer")

				// Ensure no floating-point precision loss by checking the result is exactly what we expect
				assert.Equal(t, tt.expected, result, "Integer conversion should be exact (no precision loss)")
			}
		})
	}
}

// TestDatasetIntegrationScenarios tests scenarios that validate integration with the dataset system
func TestDatasetIntegrationScenarios(t *testing.T) {
	converter := NewDateTimeConverter()

	// Test data that would be used in actual dataset scenarios
	datasetTestCases := []struct {
		name     string
		input    string
		expected int64
		hasError bool
	}{
		// Typical CSV datetime values
		{"csv_datetime_1", "2023-01-15T09:30:00Z", 20230115093000, false},
		{"csv_datetime_2", "2023-06-20T14:45:30Z", 20230620144530, false},
		{"csv_datetime_3", "2023-12-25T18:00:00Z", 20231225180000, false},

		// Date-only values (common in datasets)
		{"csv_date_1", "2023-01-01", 20230101, false},
		{"csv_date_2", "2023-06-15", 20230615, false},
		{"csv_date_3", "2023-12-31", 20231231, false},

		// Time-only values (less common but supported)
		{"csv_time_1", "09:00:00Z", 90000, false},
		{"csv_time_2", "15:30:45Z", 153045, false},
		{"csv_time_3", "23:59:59Z", 235959, false},

		// Values with timezone offsets (real-world data)
		{"csv_tz_1", "2023-07-04T12:00:00-04:00", 20230704160000, false}, // EDT to UTC
		{"csv_tz_2", "2023-07-04T12:00:00+09:00", 20230704030000, false}, // JST to UTC
		{"csv_tz_3", "2023-07-04T12:00:00+00:00", 20230704120000, false}, // Already UTC
	}

	t.Run("dataset_integration", func(t *testing.T) {
		for _, tt := range datasetTestCases {
			t.Run(tt.name, func(t *testing.T) {
				result, err := converter.ConvertISO8601ToInt(tt.input)

				if tt.hasError {
					assert.Error(t, err)
				} else {
					assert.NoError(t, err)
					assert.Equal(t, tt.expected, result)

					// Validate that the result can be used in integer arithmetic (splitting)
					// This simulates what happens in the decision tree splitting algorithm
					threshold := result
					assert.True(t, threshold > 0, "Threshold should be positive for valid datetime")

					// Test that we can perform integer comparisons (used in tree traversal)
					testValue := result + 1
					assert.True(t, testValue > threshold, "Integer arithmetic should work correctly")
					testValue = result - 1
					assert.True(t, testValue < threshold, "Integer arithmetic should work correctly")
				}
			})
		}
	})
}

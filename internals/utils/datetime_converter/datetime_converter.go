// Package utils provides utilities for converting ISO 8601 formatted date/time strings
// into numeric formats for data processing.
//
// This utility focuses on converting ISO 8601 formatted strings in the format:
// YYYY-MM-DDTHH:mm:ss.sssZ (where T separates date and time, Z indicates UTC or offset)
//
// Conversion rules:
// - Date only: YYYYMMDD (8-digit number)
// - Time only: HHMMSS (6-digit number, milliseconds ignored, timezone optional)
// - Date and time: YYYYMMDDHHMMSS (14-digit number, milliseconds ignored)
//
// Example usage:
//
//	converter := utils.NewDateTimeConverter()
//
//	// Convert date only
//	result, err := converter.ConvertISO8601("2023-12-25")
//	// result: 20231225
//
//	// Convert time only (milliseconds ignored, timezone optional)
//	result, err := converter.ConvertISO8601("14:30:45.123")
//	// result: 143045
//
//	// Convert date and time (milliseconds ignored)
//	result, err := converter.ConvertISO8601("2023-12-25T14:30:45.123Z")
//	// result: 20231225143045
package datetimeconverter

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/relvacode/iso8601"
)

// DateTimeConverter handles conversion of ISO 8601 formatted strings to numeric formats
type DateTimeConverter struct {
	// dateOnlyRegex matches date-only patterns like "2023-12-25"
	dateOnlyRegex *regexp.Regexp
	// timeOnlyRegex matches time-only patterns like "14:30:45.123Z" or "14:30:45Z"
	timeOnlyRegex *regexp.Regexp
	// dateTimeRegex matches full datetime patterns like "2023-12-25T14:30:45.123Z"
	dateTimeRegex *regexp.Regexp
}

// DateTimeConversionError represents errors that occur during date/time conversion
type DateTimeConversionError struct {
	Input  string
	Reason string
	Err    error
}

func (e *DateTimeConversionError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("datetime conversion failed for input '%s': %s (%v)", e.Input, e.Reason, e.Err)
	}
	return fmt.Sprintf("datetime conversion failed for input '%s': %s", e.Input, e.Reason)
}

func (e *DateTimeConversionError) Unwrap() error {
	return e.Err
}

// NewDateTimeConverter creates a new DateTimeConverter with compiled regex patterns
func NewDateTimeConverter() *DateTimeConverter {
	return &DateTimeConverter{
		// Date only: YYYY-MM-DD
		dateOnlyRegex: regexp.MustCompile(`^\d{4}-\d{2}-\d{2}$`),
		// Time only: HH:mm:ss.sss or HH:mm:ss or HH:mm:ss.sssZ (supports milliseconds, timezone optional)
		timeOnlyRegex: regexp.MustCompile(`^\d{2}:\d{2}:\d{2}(\.\d{1,3})?((Z|[+-]\d{2}:\d{2})?)$`),
		// DateTime: YYYY-MM-DDTHH:mm:ss.sssZ or similar with timezone (supports milliseconds)
		dateTimeRegex: regexp.MustCompile(`^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?(Z|[+-]\d{2}:\d{2})$`),
	}
}

// ConvertISO8601 converts an ISO 8601 formatted string to the appropriate numeric format
//
// Parameters:
// - input: ISO 8601 formatted string (date, time, or datetime)
//
// Returns:
// - int64: The converted numeric value
// - error: Any error encountered during conversion
//
// Conversion rules:
// - Date only (YYYY-MM-DD): returns YYYYMMDD (8-digit number)
// - Time only (HH:mm:ss.sss): returns HHMMSS (6-digit number, ignoring milliseconds, timezone optional)
// - DateTime (YYYY-MM-DDTHH:mm:ss.sssZ): returns YYYYMMDDHHMMSS (14-digit number, ignoring milliseconds)
func (dtc *DateTimeConverter) ConvertISO8601ToInt(input string) (int64, error) {
	input = strings.TrimSpace(input)

	if input == "" {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "input cannot be empty",
		}
	}

	// Check if it's a full datetime
	if dtc.dateTimeRegex.MatchString(input) {
		return dtc.convertDateTimeToInt(input)
	}

	// Check if it's date only
	if dtc.dateOnlyRegex.MatchString(input) {
		return dtc.convertDateOnlyToInt(input)
	}

	// Check if it's time only
	if dtc.timeOnlyRegex.MatchString(input) {
		return dtc.convertTimeOnlyToInt(input)
	}

	return 0, &DateTimeConversionError{
		Input:  input,
		Reason: "input does not match expected ISO 8601 format (YYYY-MM-DD, HH:mm:ss.sss, or YYYY-MM-DDTHH:mm:ss.sssZ)",
	}
}

// convertDateTimeToInt converts a full datetime string to YYYYMMDDHHMMSS integer format
// The time is converted to UTC before formatting
func (dtc *DateTimeConverter) convertDateTimeToInt(input string) (int64, error) {
	parsedTime, err := iso8601.ParseString(input)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to parse datetime",
			Err:    err,
		}
	}

	// Convert to UTC for consistent formatting
	utcTime := parsedTime.UTC()

	// Format as YYYYMMDDHHMMSS
	formatted := utcTime.Format("20060102150405")
	result, err := strconv.ParseInt(formatted, 10, 64)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to convert formatted datetime to integer",
			Err:    err,
		}
	}

	return result, nil
}

// convertDateOnlyToInt converts a date-only string to YYYYMMDD integer format
func (dtc *DateTimeConverter) convertDateOnlyToInt(input string) (int64, error) {
	// Parse as date (add time component for parsing)
	parsedTime, err := time.Parse("2006-01-02", input)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to parse date",
			Err:    err,
		}
	}

	// Format as YYYYMMDD
	formatted := parsedTime.Format("20060102")
	result, err := strconv.ParseInt(formatted, 10, 64)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to convert formatted date to integer",
			Err:    err,
		}
	}

	return result, nil
}

// convertTimeOnlyToInt converts a time-only string to HHMMSS integer format
// The time is converted to UTC before formatting
func (dtc *DateTimeConverter) convertTimeOnlyToInt(input string) (int64, error) {
	// For time-only parsing, we need to add a date component
	// Use a fixed date (1970-01-01) and parse as full datetime
	dateTimeInput := "1970-01-01T" + input

	// If the input doesn't have a timezone, add Z to make it UTC
	if !strings.Contains(input, "Z") && !strings.Contains(input, "+") && !strings.Contains(input, "-") {
		dateTimeInput += "Z"
	}

	parsedTime, err := iso8601.ParseString(dateTimeInput)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to parse time",
			Err:    err,
		}
	}

	// Convert to UTC for consistent formatting
	utcTime := parsedTime.UTC()

	// Format as HHMMSS (ignore milliseconds)
	formatted := utcTime.Format("150405")
	result, err := strconv.ParseInt(formatted, 10, 64)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to convert formatted time to integer",
			Err:    err,
		}
	}

	return result, nil
}

// Global singleton instance
var (
	globalConverter *DateTimeConverter
	once            sync.Once
)

// GetGlobalConverter returns a singleton instance of DateTimeConverter
// This eliminates the need to create multiple converter instances throughout the application
func GetGlobalConverter() *DateTimeConverter {
	once.Do(func() {
		globalConverter = NewDateTimeConverter()
	})
	return globalConverter
}

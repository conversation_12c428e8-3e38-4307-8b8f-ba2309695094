# Utils Package

This package provides comprehensive utilities for loading and validating machine learning datasets with a focus on data integrity, performance, and clear error reporting.

## Features

### Core Functionality
- **RFC 4180 Compliant CSV Loading**: Robust CSV parsing with proper quote handling and validation
- **Feature Metadata Parsing**: YAML-based feature type definitions with Unicode support
- **Structured Error Handling**: Rich error context for debugging and monitoring
- **Performance Optimized**: Memory-efficient processing with pre-allocation and buffered I/O
- **Security Hardened**: Path traversal protection and file validation
- **Comprehensive Validation**: Input sanitization and data consistency checks
- **Prediction Output**: Flexible output formats (CSV, JSON, TSV) for prediction results

### Data Handling
- **CSV Dataset Implementation**: Efficient in-memory dataset for training operations
- **Zero-copy Subsetting**: Memory-efficient dataset views for cross-validation and sampling
- **Prediction Result Writing**: Configurable output formats with comprehensive result data

#### CSV Processing
- Read and validate CSV files (`ReadTrainingCSV`)
- Extract features and target columns (`ExtractFeatureAndTarget`)
- Load prediction data with type conversion (`ReadPredictionCSV`)
- Load prediction data from trained models (`LoadPredictionDataFromModel`)
- Robust error handling with structured error types
- Configurable NA value detection
- Support for quoted fields and special characters
- Memory-efficient processing for large datasets

#### Prediction Output
- Write prediction results in multiple formats (CSV, JSON, TSV)
- Configurable output columns and data inclusion
- Support for special characters and proper escaping
- Performance optimized for large result sets
- Comprehensive error handling and validation

#### Metadata File Validation
- Path validation with security checks (non-empty, no path traversal)
- File extension validation (must be .yaml, .yml)
- File existence and accessibility verification
- File type validation (regular files only)
- File size limits (configurable, default 10MB)
- Read permission verification

## Quick Start

### Basic CSV Loading
```go
package main

import (
    "fmt"
    "log"
    "your-project/utils"
)

func main() {
    // Load CSV data
    headers, data, targetIdx, err := utils.ReadTrainingCSV("data.csv", "target_column")
    if err != nil {
        // Handle structured errors
        if csvErr, ok := err.(*utils.CSVError); ok {
            log.Printf("CSV error at line %d: %v", csvErr.Line, csvErr.Err)
        } else {
            log.Fatal(err)
        }
        return
    }

    // Extract features and targets
    features, targets, featureHeaders, err := utils.ExtractFeatureAndTarget(
        data, headers, "target_column", targetIdx)
    if err != nil {
        log.Fatal(err)
    }

    fmt.Printf("Loaded %d samples with %d features\n", len(features), len(featureHeaders))
    fmt.Printf("Feature names: %v\n", featureHeaders)
    fmt.Printf("First few targets: %v\n", targets[:min(5, len(targets))])
}
```

### Prediction Data Loading
```go
package main

import (
    "fmt"
    "log"
    "your-project/utils"
    "your-project/models"
)

func main() {
    // Method 1: Load prediction data with explicit feature types
    expectedFeatures := map[string]models.FeatureType{
        "age":      models.NumericFeature,
        "income":   models.NumericFeature,
        "category": models.CategoricalFeature,
    }

    headers, records, err := utils.ReadPredictionCSV("prediction_data.csv", expectedFeatures)
    if err != nil {
        log.Fatal(err)
    }

    fmt.Printf("Loaded %d prediction records\n", len(records))
    for i, record := range records {
        fmt.Printf("Record %d: %+v\n", i, record.Features)
    }

    // Method 2: Load prediction data from a trained model
    // Assume you have a trained model loaded
    var trainedModel *models.DecisionTree
    // ... load your trained model ...

    headers, records, err = utils.LoadPredictionDataFromModel("prediction_data.csv", trainedModel)
    if err != nil {
        log.Fatal(err)
    }

    fmt.Printf("Loaded %d prediction records using model schema\n", len(records))
}
```

### Writing Prediction Results
```go
package main

import (
    "fmt"
    "log"
    "time"
    "your-project/utils"
)

func main() {
    // Create sample prediction results
    results := []*utils.PredictionResult{
        {
            Index:      0,
            Prediction: "class_a",
            Confidence: 0.85,
            Probabilities: map[interface{}]float64{
                "class_a": 0.85,
                "class_b": 0.15,
            },
            RulePath:     "feature_1 > 0.5",
            DecisionPath: []string{"root", "feature_1 > 0.5", "leaf"},
            OriginalFeatures: map[string]interface{}{
                "feature_1": 0.7,
                "feature_2": "category_x",
            },
            ProcessingTime: time.Millisecond * 100,
        },
        {
            Index:      1,
            Prediction: "class_b",
            Confidence: 0.92,
            Probabilities: map[interface{}]float64{
                "class_a": 0.08,
                "class_b": 0.92,
            },
            RulePath:     "feature_1 <= 0.5",
            DecisionPath: []string{"root", "feature_1 <= 0.5", "leaf"},
            OriginalFeatures: map[string]interface{}{
                "feature_1": 0.3,
                "feature_2": "category_y",
            },
            ProcessingTime: time.Millisecond * 80,
        },
    }

    // Configure output - include all available columns
    config := utils.OutputConfig{
        IncludeIndex:          true,
        IncludePrediction:     true,
        IncludeConfidence:     true,
        IncludeProbabilities:  true,
        IncludeRulePath:       true,
        IncludeDecisionPath:   true,
        IncludeFeatures:       true,
        IncludeProcessingTime: true,
        IncludeErrors:         true,
    }

    // Write to CSV
    err := utils.WritePredictionCSV(results, "predictions.csv", config)
    if err != nil {
        log.Fatal(err)
    }

    // Write to JSON
    err = utils.WritePredictionJSON(results, "predictions.json", config)
    if err != nil {
        log.Fatal(err)
    }

    // Write to TSV
    err = utils.WritePredictionTSV(results, "predictions.tsv", config)
    if err != nil {
        log.Fatal(err)
    }

    fmt.Println("Prediction results written successfully")
}
```

### Converting Traversal Results
```go
package main

import (
    "fmt"
    "log"
    "time"
    "your-project/utils"
)

func main() {
    // Sample traversal results from model prediction
    traversalResults := []utils.TraversalResultWithContext{
        {
            Result: &utils.TraversalResult{
                Prediction:    "approved",
                Confidence:    0.87,
                Probabilities: map[interface{}]float64{"approved": 0.87, "denied": 0.13},
                RulePath:      "credit_score > 700",
                Path:          []string{"root", "credit_score > 700", "approved"},
            },
            OriginalRecord: map[string]interface{}{
                "credit_score": 750,
                "income":       65000,
                "age":          32,
            },
            ProcessingTime: time.Millisecond * 150,
            Error:          nil,
        },
        {
            Result:         nil,
            OriginalRecord: map[string]interface{}{
                "credit_score": "invalid",
                "income":       50000,
                "age":          25,
            },
            ProcessingTime: time.Millisecond * 50,
            Error:          fmt.Errorf("invalid credit score format"),
        },
    }

    // Convert to PredictionResult format
    results := utils.ConvertTraversalResults(traversalResults)

    // Write results with default configuration
    config := utils.DefaultOutputConfig()
    err := utils.WritePredictionCSV(results, "loan_predictions.csv", config)
    if err != nil {
        log.Fatal(err)
    }

    fmt.Printf("Converted and wrote %d prediction results\n", len(results))
}
```

### Feature Metadata Parsing
```go
package main

import (
    "fmt"
    "log"
    "your-project/utils"
)

func main() {
    parser := utils.NewFeatureInfoParser()
    
    // Parse feature metadata
    config, err := parser.ParseFeatureInfoFile("features.yaml")
    if err != nil {
        // Handle structured errors
        if metaErr, ok := err.(*utils.MetadataError); ok {
            log.Printf("Metadata error for feature '%s': %v", metaErr.Feature, metaErr.Err)
        } else {
            log.Fatal(err)
        }
        return
    }

    // Process features based on metadata
    for featureName, featureInfo := range *config {
        switch featureInfo.ToModelFeatureType() {
        case models.CategoricalFeature:
            fmt.Printf("Categorical feature '%s' with values: %v\n", 
                featureName, featureInfo.Values)
        case models.NumericFeature:
            fmt.Printf("Numeric feature '%s'", featureName)
            if featureInfo.Min != nil && featureInfo.Max != nil {
                fmt.Printf(" (range: %.2f to %.2f)", *featureInfo.Min, *featureInfo.Max)
            }
            fmt.Println()
        }
    }
}
```

## Output Formats and Configuration

### Supported Output Formats
- **CSV**: Comma-separated values with proper escaping
- **JSON**: JavaScript Object Notation with pretty printing
- **TSV**: Tab-separated values

### Output Configuration Options
```go
type OutputConfig struct {
    IncludeIndex          bool     // Include row index
    IncludePrediction     bool     // Include prediction result
    IncludeConfidence     bool     // Include confidence score
    IncludeProbabilities  bool     // Include class probabilities
    IncludeRulePath       bool     // Include decision rule path
    IncludeDecisionPath   bool     // Include full decision path
    IncludeFeatures       bool     // Include original features
    IncludeProcessingTime bool     // Include processing time
    IncludeErrors         bool     // Include error messages
    SelectedFeatures      []string // Specific features to include
}
```

### Default Configuration
```go
// Default configuration includes essential columns
config := utils.DefaultOutputConfig()
// Equivalent to:
config := utils.OutputConfig{
    IncludeIndex:      true,
    IncludePrediction: true,
    IncludeConfidence: true,
    IncludeErrors:     true,
}
```

### Custom Configuration Examples
```go
// Minimal output - just predictions
minimal := utils.OutputConfig{
    IncludeIndex:      true,
    IncludePrediction: true,
}

// Comprehensive output - all available data
comprehensive := utils.OutputConfig{
    IncludeIndex:          true,
    IncludePrediction:     true,
    IncludeConfidence:     true,
    IncludeProbabilities:  true,
    IncludeRulePath:       true,
    IncludeDecisionPath:   true,
    IncludeFeatures:       true,
    IncludeProcessingTime: true,
    IncludeErrors:         true,
}

// Selected features only
selective := utils.OutputConfig{
    IncludeIndex:      true,
    IncludePrediction: true,
    IncludeFeatures:   true,
    SelectedFeatures:  []string{"age", "income", "credit_score"},
}
```

## YAML Schema for Feature Metadata

### Nominal (Categorical) Features
```yaml
weather:
  type: nominal
  values:
    - sunny
    - cloudy
    - rainy

play_tennis:
  type: nominal
  values:
    - "yes"
    - "no"
```

### Numeric Features
```yaml
# With bounds
temperature:
  type: numeric
  min: -50.0
  max: 50.0

# Without bounds
age:
  type: numeric
```

### Unicode Support
```yaml
météo:
  type: nominal
  values:
    - "☀️ ensoleillé"
    - "☁️ nuageux"
    - "🌧️ pluvieux"

température:
  type: numeric
  min: -10.0
  max: 40.0
```

## Error Handling

The package provides structured error types for different failure scenarios:

### CSV Errors
```go
if csvErr, ok := err.(*utils.CSVError); ok {
    fmt.Printf("CSV %s error in %s", csvErr.Op, csvErr.File)
    if csvErr.Line > 0 {
        fmt.Printf(" at line %d", csvErr.Line)
    }
    if csvErr.Column != "" {
        fmt.Printf(", column %s", csvErr.Column)
    }
    fmt.Printf(": %v\n", csvErr.Err)
}
```

### Metadata Errors
```go
if metaErr, ok := err.(*utils.MetadataError); ok {
    fmt.Printf("Metadata %s error in %s", metaErr.Op, metaErr.File)
    if metaErr.Feature != "" {
        fmt.Printf(" for feature '%s'", metaErr.Feature)
    }
    fmt.Printf(": %v\n", metaErr.Err)
}
```

### Validation Errors
```go
if validationErr, ok := err.(*utils.ValidationError); ok {
    fmt.Printf("Validation failed for %s '%s': %s\n", 
        validationErr.Field, validationErr.Value, validationErr.Reason)
}
```

### Output Errors
```go
// Handle output-specific errors
err := utils.WritePredictionCSV(results, "output.csv", config)
if err != nil {
    if strings.Contains(err.Error(), "no results to write") {
        log.Println("No prediction results available")
    } else if strings.Contains(err.Error(), "failed to create output file") {
        log.Printf("Cannot write to file: %v", err)
    } else {
        log.Printf("Unexpected error: %v", err)
    }
}
```

## Configuration

### Configurable Constants
```go
const (
    MinRequiredDataRows     = 2     // Minimum data rows required
    DefaultDelimiter        = ','   // CSV delimiter
    MaxMetadataFileSize     = 10MB  // Maximum metadata file size
)
```

### Configurable NA Values
```go
var DefaultNAValues = []string{
    "na", "nan", "n/a", "null", "none",
    "missing", "unknown", "undefined", "",
}
```

### Output Formats
```go
const (
    CSVFormat  utils.OutputFormat = "csv"
    JSONFormat utils.OutputFormat = "json"
    TSVFormat  utils.OutputFormat = "tsv"
)
```

## Performance Characteristics

### Memory Optimization
- **Pre-allocation**: Slices are pre-allocated based on estimated sizes
- **Buffered I/O**: Uses buffered readers for large file processing
- **Zero-copy where possible**: Minimizes data copying during processing
- **Object pooling**: Reuses string slices for repeated operations
- **Efficient output writing**: Streaming writers for large result sets

### Benchmarks
Run benchmarks to measure performance on your datasets:

```bash
# Run all benchmarks
go test -bench=. -benchmem

# Run specific benchmarks  
go test -bench=BenchmarkReadTrainingCSV_Large -benchmem
go test -bench=BenchmarkExtractFeatureAndTarget -benchmem

# Profile memory usage
go test -bench=BenchmarkCSVFullPipeline -memprofile=mem.prof
go tool pprof mem.prof
```

## Validation Rules

### CSV Files
- Must have `.csv` extension
- Must contain headers (non-empty)
- Target column must exist in headers
- All rows must have consistent field count
- Target column cannot contain NA/empty values
- Minimum 2 data rows required

### Output Files
- Output directory must be writable
- File extensions automatically determined by format
- Special characters properly escaped in CSV/TSV
- JSON output uses pretty printing by default

### Metadata Files  
- Must have `.yaml` or `.yml` extension (case-insensitive)
- File size limit (10MB default)
- No path traversal attempts
- Must be regular files (not directories/pipes)

#### Nominal Features
- Must specify `type: nominal`
- Must provide non-empty `values` list
- Values cannot be empty or whitespace-only
- No duplicate values allowed
- Cannot specify `min` or `max` properties

#### Numeric Features
- Must specify `type: numeric`
- Cannot provide `values` list
- Optional `min` and `max` bounds
- If both bounds specified: `min < max`
- Bounds cannot be equal

## Testing

### Running Tests
```bash
# Run all tests
go test ./...

# Run tests with race detection
go test -race ./...

# Run tests with coverage
go test -cover ./...
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out

# Run output-specific tests
go test -run TestWrite ./...
go test -run TestPrediction ./...
```

### Test Categories
- **Unit tests**: Individual function testing
- **Integration tests**: End-to-end workflow testing  
- **Edge case tests**: Unicode, malformed data, security
- **Error handling tests**: Structured error validation
- **Benchmark tests**: Performance measurement
- **Output format tests**: CSV, JSON, TSV validation
- **Special character tests**: Escaping and encoding

## Dependencies

- `gopkg.in/yaml.v3` - YAML parsing
- `github.com/berrijam/mulberri/pkg/models` - Feature type definitions
- Go standard library packages (`encoding/csv`, `encoding/json`, `os`, `time`)

## Security Considerations

### Path Security
- Path traversal protection (`../` detection)
- File type validation (regular files only)
- File size limits to prevent DoS
- Read permission verification
- Output directory creation with safe permissions

### Input Validation
- All user inputs are validated and sanitized
- Structured error messages without sensitive information
- No execution of user-provided content
- Safe handling of special characters in output

### Output Security
- Proper CSV escaping to prevent formula injection
- JSON encoding prevents code injection
- File permissions set appropriately
- No sensitive data in error messages

## Performance Tips

### For Large Datasets
1. **Pre-process if possible**: Remove unnecessary columns before loading
2. **Stream processing**: Consider processing in batches for very large files
3. **Memory monitoring**: Use the benchmark tests to understand memory usage
4. **Concurrent processing**: The package supports concurrent access for read operations
5. **Efficient output**: Use appropriate output format for your use case

### For Large Result Sets
1. **Choose format wisely**: CSV is most efficient for tabular data
2. **Selective columns**: Only include necessary columns in output
3. **Batch writing**: Process results in batches if memory is limited
4. **Monitor disk space**: Large result sets can consume significant storage

### For Production Use
1. **Error monitoring**: Log structured errors for debugging
2. **Metrics collection**: Track parsing times and error rates
3. **Resource limits**: Set appropriate file size limits
4. **Caching**: Cache parsed metadata if files don't change frequently
5. **Output validation**: Verify output files are written correctly

## CSVDataset

The `CSVDataset` provides an efficient in-memory implementation of the `training.Dataset[string]` interface for CSV data processing.

### Features

- **Zero-copy subsetting**: Create dataset views without duplicating data
- **Type conversion**: Automatic string-to-numeric conversion for numeric features
- **Memory efficient**: Uses index-based views for subset operations
- **Error handling**: Comprehensive bounds checking and validation

### Usage

```go
import "github.com/berrijam/mulberri/internals/utils"

// Create dataset from CSV data
features := [][]string{
    {"5.1", "setosa", "red"},
    {"4.9", "versicolor", "blue"},
    {"6.2", "virginica", "red"},
}
targets := []string{"iris1", "iris2", "iris3"}

dataset := utils.NewCSVDataset(features, targets)

// Basic operations
size := dataset.GetSize()                    // Returns 3
indices := dataset.GetIndices()              // Returns [0, 1, 2]
target, err := dataset.GetTarget(1)          // Returns "iris2"

// Feature value retrieval with type conversion
numericFeature := &models.Feature{
    Name:         "sepal_length",
    Type:         models.NumericFeature,
    ColumnNumber: 0,
}
value, err := dataset.GetFeatureValue(0, numericFeature)  // Returns 5.1 (float64)

// Zero-copy subsetting
subset := dataset.Subset([]int{0, 2})        // Create view of samples 0 and 2
subsetSize := subset.GetSize()               // Returns 2
```

### Interface Compliance

The `CSVDataset` implements the `training.Dataset[string]` interface:

```go
type Dataset[T comparable] interface {
    GetSize() int
    GetFeatureValue(sampleIdx int, feature *models.Feature) (interface{}, error)
    GetTarget(sampleIdx int) (T, error)
    GetIndices() []int
    Subset(indices []int) Dataset[T]
}
```

## Data Structures

### PredictionResult
```go
type PredictionResult struct {
    Index            int                     // Row index
    Prediction       interface{}             // Predicted class/value
    Confidence       float64                 // Confidence score (0-1)
    Probabilities    map[interface{}]float64 // Class probabilities
    RulePath         string                  // Decision rule text
    DecisionPath     []string                // Full decision path
    OriginalFeatures map[string]interface{}  // Input features
    ProcessingTime   time.Duration           // Processing time
    Error            string                  // Error message if any
}
```

### TraversalResult
```go
type TraversalResult struct {
    Prediction    interface{}                // Predicted value
    Confidence    float64                    // Confidence score
    Probabilities map[interface{}]float64    // Class probabilities
    RulePath      string                     // Decision rule
    Path          []string                   // Decision path steps
}
```

### TraversalResultWithContext
```go
type TraversalResultWithContext struct {
    Result         *TraversalResult           // Traversal result
    OriginalRecord map[string]interface{}     // Original input record
    ProcessingTime time.Duration              // Processing duration
    Error          error                      // Processing error
}
```
package utils

import (
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"testing"
)

func TestValidateFeatureInfoFile(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "metadata_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tests := []struct {
		name          string
		setup         func() string
		expectError   bool
		errorContains string
	}{
		{
			name: "empty file path",
			setup: func() string {
				return ""
			},
			expectError:   true,
			errorContains: "empty file path provided",
		},
		{
			name: "whitespace only file path",
			setup: func() string {
				return "   \t\n  "
			},
			expectError:   true,
			errorContains: "empty file path provided",
		},
		{
			name: "path traversal attempt",
			setup: func() string {
				return "../../../etc/passwd.yaml"
			},
			expectError:   true,
			errorContains: "path traversal detected",
		},
		{
			name: "invalid file extension - .json",
			setup: func() string {
				return "test.json"
			},
			expectError:   true,
			errorContains: "invalid file extension: .json, expected .yaml or .yml",
		},
		{
			name: "invalid file extension - .xml",
			setup: func() string {
				return "test.xml"
			},
			expectError:   true,
			errorContains: "invalid file extension: .xml, expected .yaml or .yml",
		},
		{
			name: "invalid file extension - no extension",
			setup: func() string {
				return "testfile"
			},
			expectError:   true,
			errorContains: "invalid file extension: , expected .yaml or .yml",
		},
		{
			name: "case insensitive extension - .YAML",
			setup: func() string {
				filePath := filepath.Join(tempDir, "test.YAML")
				err := os.WriteFile(filePath, []byte("key: value\n"), 0644)
				if err != nil {
					t.Fatalf("Failed to create test file: %v", err)
				}
				return filePath
			},
			expectError: false,
		},
		{
			name: "case insensitive extension - .YML",
			setup: func() string {
				filePath := filepath.Join(tempDir, "test.YML")
				err := os.WriteFile(filePath, []byte("key: value\n"), 0644)
				if err != nil {
					t.Fatalf("Failed to create test file: %v", err)
				}
				return filePath
			},
			expectError: false,
		},
		{
			name: "file does not exist - .yaml",
			setup: func() string {
				return filepath.Join(tempDir, "nonexistent.yaml")
			},
			expectError:   true,
			errorContains: "file does not exist:",
		},
		{
			name: "file does not exist - .yml",
			setup: func() string {
				return filepath.Join(tempDir, "nonexistent.yml")
			},
			expectError:   true,
			errorContains: "file does not exist:",
		},
		{
			name: "valid yaml file",
			setup: func() string {
				filePath := filepath.Join(tempDir, "valid.yaml")
				err := os.WriteFile(filePath, []byte("key: value\n"), 0644)
				if err != nil {
					t.Fatalf("Failed to create test file: %v", err)
				}
				return filePath
			},
			expectError: false,
		},
		{
			name: "valid yml file",
			setup: func() string {
				filePath := filepath.Join(tempDir, "valid.yml")
				err := os.WriteFile(filePath, []byte("key: value\n"), 0644)
				if err != nil {
					t.Fatalf("Failed to create test file: %v", err)
				}
				return filePath
			},
			expectError: false,
		},
		{
			name: "file too large",
			setup: func() string {
				filePath := filepath.Join(tempDir, "large.yaml")
				// Create a file larger than MaxMetadataFileSize
				largeContent := strings.Repeat("a", MaxMetadataFileSize+1)
				err := os.WriteFile(filePath, []byte(largeContent), 0644)
				if err != nil {
					t.Fatalf("Failed to create large test file: %v", err)
				}
				return filePath
			},
			expectError:   true,
			errorContains: "file too large:",
		},
	}

	// Test with relative path
	t.Run("relative path to valid file", func(t *testing.T) {
		// Create a file in current directory
		relativeFile := "test_relative.yaml"
		err := os.WriteFile(relativeFile, []byte("key: value\n"), 0644)
		if err != nil {
			t.Fatalf("Failed to create relative test file: %v", err)
		}
		defer os.Remove(relativeFile)

		returnedPath, err := ValidateFeatureInfoFile(relativeFile)
		if err != nil {
			t.Errorf("Expected no error for relative path, got: %v", err)
		}
		if returnedPath == "" {
			t.Error("Expected non-empty path for valid file")
		}

		// Verify the returned path matches the input path
		if returnedPath != relativeFile {
			t.Errorf("Expected returned path to match input path: expected %s, got %s", relativeFile, returnedPath)
		}
	})

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filePath := tt.setup()

			returnedPath, err := ValidateFeatureInfoFile(filePath)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
					return
				}
				if returnedPath != "" {
					t.Errorf("Expected empty path on error, got: %s", returnedPath)
				}
				if tt.errorContains != "" && !strings.Contains(err.Error(), tt.errorContains) {
					t.Errorf("Expected error to contain '%s', got: %v", tt.errorContains, err)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
				if returnedPath == "" {
					t.Error("Expected non-empty path for valid file")
				}

				// Verify the returned path is cleaned/normalized
				expectedPath := filepath.Clean(filePath)
				if returnedPath != expectedPath {
					t.Errorf("Expected returned path to be cleaned: expected %s, got %s", expectedPath, returnedPath)
				}
			}
		})
	}
}

// Test for the "error accessing file" that's not "file does not exist"
func TestValidateFeatureInfoFile_AccessError(t *testing.T) {
	if runtime.GOOS == "windows" {
		t.Skip("Skipping access permission test on Windows")
	}

	tempDir, err := os.MkdirTemp("", "metadata_access_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a subdirectory and a file in it
	subDir := filepath.Join(tempDir, "subdir")
	err = os.Mkdir(subDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create subdirectory: %v", err)
	}

	filePath := filepath.Join(subDir, "test.yaml")
	err = os.WriteFile(filePath, []byte("key: value\n"), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// Remove execute permission on the parent directory
	// This should make the file inaccessible but still existing
	err = os.Chmod(subDir, 0000)
	if err != nil {
		t.Fatalf("Failed to change directory permissions: %v", err)
	}

	// Restore permissions in cleanup
	t.Cleanup(func() {
		os.Chmod(subDir, 0755)
	})

	returnedPath, err := ValidateFeatureInfoFile(filePath)
	if err == nil {
		t.Error("Expected error accessing file but got none")
		return
	}

	if returnedPath != "" {
		t.Errorf("Expected empty path on error, got: %s", returnedPath)
	}

	if !strings.Contains(err.Error(), "error accessing file:") && !strings.Contains(err.Error(), "cannot read file:") {
		t.Errorf("Expected 'error accessing file:' or 'cannot read file:' error, got: %v", err)
	}
}

// Test for "not a regular file" error
func TestValidateFeatureInfoFile_NotRegularFile(t *testing.T) {
	if runtime.GOOS == "windows" {
		t.Skip("Skipping named pipe test on Windows")
	}

	tempDir, err := os.MkdirTemp("", "metadata_pipe_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a named pipe (FIFO) which is not a regular file
	pipePath := filepath.Join(tempDir, "testpipe.yaml")

	// Create named pipe using mkfifo command if available
	cmd := exec.Command("mkfifo", pipePath)
	err = cmd.Run()
	if err != nil {
		t.Skipf("Could not create named pipe (mkfifo not available): %v", err)
	}

	returnedPath, err := ValidateFeatureInfoFile(pipePath)
	if err == nil {
		t.Error("Expected 'not a regular file' error but got none")
		return
	}

	if returnedPath != "" {
		t.Errorf("Expected empty path on error, got: %s", returnedPath)
	}

	if !strings.Contains(err.Error(), "not a regular file:") {
		t.Errorf("Expected 'not a regular file:' error, got: %v", err)
	}
}

// Test that symlinks to regular files work correctly
func TestValidateFeatureInfoFile_Symlink(t *testing.T) {
	if runtime.GOOS == "windows" {
		t.Skip("Skipping symlink test on Windows")
	}

	tempDir, err := os.MkdirTemp("", "metadata_symlink_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a regular file first
	targetFile := filepath.Join(tempDir, "target.yaml")
	err = os.WriteFile(targetFile, []byte("key: value\n"), 0644)
	if err != nil {
		t.Fatalf("Failed to create target file: %v", err)
	}

	// Create a symlink to it
	symlinkFile := filepath.Join(tempDir, "symlink.yaml")
	err = os.Symlink(targetFile, symlinkFile)
	if err != nil {
		t.Skipf("Failed to create symlink, skipping test: %v", err)
	}

	// Test the symlink - it should work fine as symlinks are considered regular files by Go's IsRegular()
	returnedPath, err := ValidateFeatureInfoFile(symlinkFile)
	if err != nil {
		t.Errorf("Expected no error for symlink to regular file, got: %v", err)
	}
	if returnedPath == "" {
		t.Error("Expected non-empty path for valid symlink")
	}

	// Verify the returned path matches the input path
	if returnedPath != symlinkFile {
		t.Errorf("Expected returned path to match input path: expected %s, got %s", symlinkFile, returnedPath)
	}
}

// Test directory as input
func TestValidateFeatureInfoFile_Directory(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "metadata_dir_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a directory with yaml extension
	dirPath := filepath.Join(tempDir, "test.yaml")
	err = os.Mkdir(dirPath, 0755)
	if err != nil {
		t.Fatalf("Failed to create directory: %v", err)
	}

	returnedPath, err := ValidateFeatureInfoFile(dirPath)
	if err == nil {
		t.Error("Expected error for directory input but got none")
		return
	}

	if returnedPath != "" {
		t.Errorf("Expected empty path on error, got: %s", returnedPath)
	}

	if !strings.Contains(err.Error(), "not a regular file:") {
		t.Errorf("Expected 'not a regular file:' error, got: %v", err)
	}
}

// Test path validation function
func TestValidatePath(t *testing.T) {
	tests := []struct {
		name        string
		path        string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "normal path",
			path:        "/home/<USER>/file.yaml",
			expectError: false,
		},
		{
			name:        "relative path",
			path:        "config/file.yaml",
			expectError: false,
		},
		{
			name:        "path traversal with ..",
			path:        "../../../etc/passwd",
			expectError: true,
			errorMsg:    "path traversal detected",
		},
		{
			name:        "path traversal in middle",
			path:        "/home/<USER>/../../etc/passwd",
			expectError: true,
			errorMsg:    "path traversal detected",
		},
		{
			name:        "legitimate parent reference",
			path:        "config/../data/file.yaml",
			expectError: true, // Still blocked for security
			errorMsg:    "path traversal detected",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validatePath(tt.path)

			if tt.expectError {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if tt.errorMsg != "" && !strings.Contains(err.Error(), tt.errorMsg) {
					t.Errorf("Expected error to contain '%s', got: %v", tt.errorMsg, err)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			}
		})
	}
}

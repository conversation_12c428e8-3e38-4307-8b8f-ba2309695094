# Decision Tree Models Package

This package provides robust, type-safe data structures for implementing decision trees in Go. These structures form the foundation for both classification and regression trees with comprehensive validation, error handling, and performance optimization.

## Features

### Core Functionality
- **Type-Safe Design**: Comprehensive validation and structured error handling
- **Flexible Architecture**: Support for numeric, categorical, and date features
- **Performance Optimized**: Efficient memory usage and fast operations
- **Comprehensive Validation**: Input validation with clear error messages
- **Feature Management**: Advanced feature handling with importance calculation
- **Tree Statistics**: Built-in metrics for depth, node count, and tree analysis
- **Functional Options**: Flexible configuration using the options pattern

### Enhanced Error Handling
- **Structured Errors**: `ModelError` type with detailed context
- **Input Validation**: All constructors validate parameters
- **Tree Structure Validation**: Comprehensive tree integrity checking
- **Clear Error Messages**: Actionable error descriptions for debugging

## Quick Start

### Basic Usage
```go
package main

import (
    "fmt"
    "log"
    "your-project/models"
)

func main() {
    // Create a decision tree with validation
    tree, err := models.NewDecisionTree(models.CategoricalFeature, 10, 2)
    if err != nil {
        log.Fatal(err)
    }

    // Add features with validation
    ageFeature, err := tree.AddFeature("age", models.NumericFeature)
    if err != nil {
        log.Fatal(err)
    }

    colorFeature, err := tree.AddFeature("color", models.CategoricalFeature)
    if err != nil {
        log.Fatal(err)
    }

    // Set numeric range with validation  
    err = ageFeature.SetRange(0, 100)
    if err != nil {
        log.Fatal(err)
    }

    // Add categorical values with validation
    added, err := colorFeature.AddCategoricalValue("red")
    if err != nil {
        log.Fatal(err)
    }
    if added {
        fmt.Println("Added 'red' to color feature")
    }

    // Create tree nodes with validation
    rootNode, err := models.NewDecisionNode(ageFeature, 30.0)
    if err != nil {
        log.Fatal(err)
    }

    youngLeaf, err := models.NewLeafNode("young", 
        map[interface{}]int{"young": 6, "old": 1}, 7)
    if err != nil {
        log.Fatal(err)
    }

    oldLeaf, err := models.NewLeafNode("old",
        map[interface{}]int{"young": 2, "old": 5}, 7)
    if err != nil {
        log.Fatal(err)
    }

    // Set children with validation
    err = rootNode.SetLeftChild(youngLeaf)
    if err != nil {
        log.Fatal(err)
    }

    err = rootNode.SetRightChild(oldLeaf)
    if err != nil {
        log.Fatal(err)
    }

    tree.Root = rootNode

    // Calculate tree statistics
    tree.UpdateStatistics()
    fmt.Printf("Tree depth: %d, Nodes: %d, Leaves: %d\n", 
        tree.GetDepth(), tree.GetNodeCount(), tree.GetLeafCount())

    // Calculate feature importance
    importance := tree.CalculateFeatureImportance()
    for name, imp := range importance {
        fmt.Printf("Feature '%s': importance=%.4f, usage=%d\n", 
            name, imp.Importance, imp.UsageCount)
    }
}
```

### Advanced Configuration with Options
```go
// Create tree with functional options
tree, err := models.NewDecisionTreeWithOptions(
    models.WithMaxDepth(15),
    models.WithMinSamples(5),
    models.WithTargetType(models.NumericFeature),
    models.WithCriterion(models.MSECriterion),
    models.WithMaxFeatures(10),
    models.WithRandomState(42),
)
if err != nil {
    log.Fatal(err)
}
```

### Error Handling Best Practices
```go
// Handle structured errors with type assertion
_, err := models.NewFeature("", models.NumericFeature, 0)
if err != nil {
    if modelErr, ok := err.(*models.ModelError); ok {
        fmt.Printf("Model error in %s for field '%s': %s\n", 
            modelErr.Op, modelErr.Field, modelErr.Reason)
        
        // Access underlying error if needed
        if modelErr.Err != nil {
            fmt.Printf("Underlying error: %v\n", modelErr.Err)
        }
    } else {
        fmt.Printf("Unexpected error type: %v\n", err)
    }
}
```

## Data Structures

### Feature Structure

The `Feature` structure represents dataset attributes with enhanced validation and type safety:

#### Supported Feature Types
- **Numeric Features**: Continuous values with optional range constraints
- **Categorical Features**: Discrete string values with duplicate prevention
- **Date Features**: Timestamp values treated as numeric with special handling

#### Key Properties
```go
type Feature struct {
    Name         string        // Validated, non-empty name
    Type         FeatureType   // Validated feature type
    ColumnNumber int           // Non-negative column index
    
    // Type-specific storage
    CategoricalValues []string    // For categorical features
    NumericRange      *Range      // For numeric features  
    DateRange         *DateRange  // For date features
    
    // Legacy fields for backward compatibility
    Values       []interface{}
    Min, Max     float64
}
```

#### Feature Operations
```go
// Create with validation
feature, err := models.NewFeature("temperature", models.NumericFeature, 0)

// Set range with validation
err = feature.SetRange(-10.0, 50.0)

// Add categorical values with validation
added, err := feature.AddCategoricalValue("sunny")

// Check range membership
inRange, err := feature.IsInRange(25.0)

// Get categorical values (returns copy)
values := feature.GetCategoricalValues()

// Comprehensive validation
err = feature.Validate()
```

### TreeNode Structure

Enhanced tree nodes with comprehensive validation and statistics:

#### Node Types
- **Decision Nodes**: Internal nodes that split data based on features
- **Leaf Nodes**: Terminal nodes with predictions and class distributions

#### Key Enhancements
```go
type TreeNode struct {
    Type              NodeType
    Feature           *Feature
    Threshold         float64
    Categories        map[interface{}]*TreeNode
    Left, Right       *TreeNode
    Prediction        interface{}
    ClassDistribution map[interface{}]int
    Samples           int
    Confidence        float64  // Confidence in prediction
    Impurity          float64  // Node impurity measure
}
```

#### Node Operations
```go
// Create with validation
decisionNode, err := models.NewDecisionNode(feature, 30.0)
leafNode, err := models.NewLeafNode("class_A", distribution, samples)

// Set children with validation
err = decisionNode.SetLeftChild(leftChild)
err = decisionNode.SetRightChild(rightChild)
err = decisionNode.SetChild("category", childNode)

// Calculate statistics
purity := node.GetPurity()
impurity := node.GetImpurity()
majorityClass := node.GetMajorityClass()

// Update statistics after changes
node.UpdateStatistics()

// Add samples dynamically
err = node.AddSample("class_B")

// Comprehensive validation
err = node.Validate()
```

### DecisionTree Structure

Enhanced tree management with advanced features:

#### Configuration Options
```go
type TreeConfig struct {
    MaxDepth      int            // Maximum tree depth
    MinSamples    int            // Minimum samples to split
    TargetType    FeatureType    // Target variable type
    Criterion     SplitCriterion // Splitting criterion
    RandomState   *rand.Rand     // For reproducible results
    MaxFeatures   int            // Max features per split
}
```

#### Tree Operations
```go
// Feature management with validation
feature, err := tree.AddFeature("age", models.NumericFeature)
feature, err := tree.GetFeatureByIndex(0)
feature := tree.GetFeatureByName("age")
exists := tree.HasFeature("age")

// Tree statistics
depth := tree.GetDepth()
nodeCount := tree.GetNodeCount()
leafCount := tree.GetLeafCount()
tree.UpdateStatistics()

// Feature importance analysis
importance := tree.CalculateFeatureImportance()

// Tree validation
err = tree.Validate()

// Tree cloning
clonedTree, err := tree.Clone()
```

## Configuration Options

### Split Criteria
```go
const (
    GiniCriterion    SplitCriterion = "gini"     // For classification
    EntropyCriterion SplitCriterion = "entropy"  // For classification  
    MSECriterion     SplitCriterion = "mse"      // For regression
)
```

### Functional Options
```go
// Depth and sample constraints
WithMaxDepth(15)
WithMinSamples(5)

// Feature and target configuration
WithTargetType(models.NumericFeature)
WithMaxFeatures(10)

// Splitting and randomization
WithCriterion(models.EntropyCriterion)
WithRandomState(42)
```

## Error Handling

### Structured Error Types
```go
type ModelError struct {
    Op     string  // Operation that failed
    Field  string  // Field that caused the error
    Value  string  // Value that was invalid
    Reason string  // Human-readable reason
    Err    error   // Underlying error (if any)
}
```

### Common Error Scenarios
- **Invalid Input**: Empty names, negative values, invalid types
- **Constraint Violations**: Min > Max, duplicate values, type mismatches
- **Tree Structure**: Invalid children, circular references, depth violations
- **Feature Management**: Duplicate names, invalid indices, type conflicts

### Error Handling Patterns
```go
// Type assertion for detailed error info
if modelErr, ok := err.(*models.ModelError); ok {
    switch modelErr.Op {
    case "create_feature":
        // Handle feature creation errors
    case "validate_tree":
        // Handle tree validation errors
    default:
        // Handle other model errors
    }
}

// Error unwrapping for root causes
if rootErr := errors.Unwrap(err); rootErr != nil {
    // Handle underlying error
}
```

## Performance Considerations

### Memory Optimization
- **Pre-allocated Collections**: Maps and slices sized appropriately
- **Copy Prevention**: Methods return copies to prevent external modification
- **Efficient Indexing**: Both map and slice access for features
- **Statistics Caching**: Computed values cached until tree changes

### Benchmark Results
Run benchmarks to measure performance:
```bash
# All benchmarks
go test -bench=. -benchmem

# Specific operations
go test -bench=BenchmarkFeatureCreation -benchmem
go test -bench=BenchmarkTreeNodeOperations -benchmem
go test -bench=BenchmarkFeatureImportance -benchmem
```

### Performance Tips
1. **Pre-allocate**: Use estimated sizes for collections
2. **Validate Once**: Validate inputs at creation, not on every operation
3. **Cache Statistics**: Update tree statistics only when structure changes
4. **Use Indices**: Access features by index when possible for repeated operations

## Validation Rules

### Feature Validation
- **Names**: Non-empty, non-whitespace strings
- **Types**: Must be valid FeatureType values
- **Column Numbers**: Non-negative integers
- **Categorical Values**: Non-empty, no duplicates
- **Numeric Ranges**: Min < Max, no infinite/NaN values

### Tree Node Validation
- **Decision Nodes**: Must have valid feature and appropriate children
- **Leaf Nodes**: Must have non-nil prediction and consistent class distribution
- **Statistics**: Confidence and impurity must be in [0,1] range
- **Sample Counts**: Non-negative, consistent with distributions

### Tree Structure Validation
- **Depth**: Must not exceed configured maximum
- **Features**: All referenced features must exist
- **Consistency**: Node statistics must be consistent with children
- **Integrity**: No circular references or orphaned nodes

## Testing

### Test Categories
- **Unit Tests**: Individual component testing with validation
- **Integration Tests**: Multi-component workflow testing
- **Error Handling Tests**: Comprehensive error scenario coverage
- **Property Tests**: Invariant validation across operations
- **Benchmark Tests**: Performance measurement and optimization

### Running Tests
```bash
# All tests with coverage
go test -cover -v ./...

# Race condition detection
go test -race ./...

# Benchmarks with memory allocation info
go test -bench=. -benchmem -v

# Generate coverage report
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## Advanced Features

### Feature Importance Calculation
```go
// Calculate importance based on impurity reduction
importance := tree.CalculateFeatureImportance()
for name, info := range importance {
    fmt.Printf("%s: %.4f (used %d times)\n", 
        name, info.Importance, info.UsageCount)
}
```

### Tree Cloning
```go
// Create independent copy of tree structure
clonedTree, err := originalTree.Clone()
if err != nil {
    log.Fatal(err)
}

// Modifications to clone don't affect original
clonedTree.AddFeature("new_feature", models.NumericFeature)
```

### Statistics and Metrics
```go
// Comprehensive tree analysis
fmt.Printf("Tree Statistics:\n")
fmt.Printf("  Depth: %d (max: %d)\n", tree.GetDepth(), tree.Config.MaxDepth)
fmt.Printf("  Nodes: %d (leaves: %d)\n", tree.GetNodeCount(), tree.GetLeafCount())
fmt.Printf("  Features: %d\n", tree.GetFeatureCount())

// Node-level statistics
fmt.Printf("Node Statistics:\n")
fmt.Printf("  Purity: %.3f\n", node.GetPurity())
fmt.Printf("  Impurity: %.3f\n", node.GetImpurity())
fmt.Printf("  Confidence: %.3f\n", node.Confidence)
fmt.Printf("  Samples: %d\n", node.Samples)
```

## Design Principles

### Type Safety
- Comprehensive input validation at all entry points
- Structured error types with detailed context
- Clear separation between different feature types
- Consistent error handling patterns throughout

### Performance
- Efficient memory usage with pre-allocation
- Fast feature lookup with both map and index access
- Cached statistics to avoid recomputation
- Minimal memory copying in operations

### Maintainability
- Clean separation of concerns between components
- Comprehensive validation with clear error messages
- Extensive test coverage including edge cases
- Consistent naming and documentation patterns

### Extensibility
- Functional options pattern for flexible configuration
- Interface-based design for easy extension
- Pluggable components for different tree algorithms
- Forward-compatible structure with version support

## Migration from Previous Versions

### Backward Compatibility
The enhanced version maintains backward compatibility with existing code:

```go
// Old style (still works)
feature := &Feature{
    Name: "age",
    Type: NumericFeature,
    ColumnNumber: 0,
}

// New style (recommended)
feature, err := NewFeature("age", NumericFeature, 0)
if err != nil {
    log.Fatal(err)
}
```

### Migration Recommendations
1. **Add Error Handling**: Update calls to return error values
2. **Use Constructors**: Replace direct struct creation with validated constructors
3. **Handle ModelError**: Add type assertions for detailed error information
4. **Update Tests**: Add error case testing for validation scenarios
5. **Use Options**: Migrate to functional options for tree configuration

## Dependencies

- Go 1.18+ (for generics support in future versions)
- Standard library only (no external dependencies)
- Compatible with Go modules

## Contributing

When contributing to this package:

1. **Maintain Backward Compatibility**: Existing APIs should continue to work
2. **Add Comprehensive Tests**: Include unit tests, error cases, and benchmarks
3. **Update Documentation**: Keep README and code comments current
4. **Follow Validation Patterns**: Use structured errors and input validation
5. **Run All Tests**: Ensure `go test -race ./...` passes
6. **Benchmark Changes**: Verify performance doesn't regress

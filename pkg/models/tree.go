// Package models contains core data structures for the decision tree implementation.
package models

import (
	"fmt"
	"math/rand"
	"strings"

	"github.com/berrijam/mulberri/pkg/logger"
)

// SplitCriterion defines the criteria used for splitting nodes
type SplitCriterion string

const (
	GiniCriterion    SplitCriterion = "gini"
	EntropyCriterion SplitCriterion = "entropy"
	MSECriterion     SplitCriterion = "mse" // Mean Squared Error for regression
)

// TreeConfig holds configuration for decision tree creation
type TreeConfig struct {
	MaxDepth    int            `json:"max_depth"`
	MinSamples  int            `json:"min_samples"`
	TargetType  FeatureType    `json:"target_type"`
	Criterion   SplitCriterion `json:"criterion"`
	RandomState *rand.Rand     `json:"-"` // Not serializable
	MaxFeatures int            `json:"max_features"`
}

// TreeOption defines functional options for tree configuration
type TreeOption func(*TreeConfig) error

// WithMaxDepth sets the maximum depth of the tree
func WithMaxDepth(depth int) TreeOption {
	return func(config *TreeConfig) error {
		if depth <= 0 {
			return &ModelError{
				Op:     "configure_tree",
				Field:  "max_depth",
				Value:  fmt.Sprintf("%d", depth),
				Reason: "max depth must be positive",
			}
		}
		config.MaxDepth = depth
		return nil
	}
}

// WithMinSamples sets the minimum number of samples required to split
func WithMinSamples(samples int) TreeOption {
	return func(config *TreeConfig) error {
		if samples <= 0 {
			return &ModelError{
				Op:     "configure_tree",
				Field:  "min_samples",
				Value:  fmt.Sprintf("%d", samples),
				Reason: "min samples must be positive",
			}
		}
		config.MinSamples = samples
		return nil
	}
}

// WithTargetType sets the target variable type
func WithTargetType(targetType FeatureType) TreeOption {
	return func(config *TreeConfig) error {
		if !isValidFeatureType(targetType) {
			return &ModelError{
				Op:     "configure_tree",
				Field:  "target_type",
				Value:  string(targetType),
				Reason: "invalid target type",
			}
		}
		config.TargetType = targetType
		return nil
	}
}

// WithCriterion sets the split criterion
func WithCriterion(criterion SplitCriterion) TreeOption {
	return func(config *TreeConfig) error {
		switch criterion {
		case GiniCriterion, EntropyCriterion, MSECriterion:
			config.Criterion = criterion
			return nil
		default:
			return &ModelError{
				Op:     "configure_tree",
				Field:  "criterion",
				Value:  string(criterion),
				Reason: "invalid split criterion, must be 'gini', 'entropy', or 'mse'",
			}
		}
	}
}

// WithMaxFeatures sets the maximum number of features to consider for splits
func WithMaxFeatures(maxFeatures int) TreeOption {
	return func(config *TreeConfig) error {
		if maxFeatures <= 0 {
			return &ModelError{
				Op:     "configure_tree",
				Field:  "max_features",
				Value:  fmt.Sprintf("%d", maxFeatures),
				Reason: "max features must be positive",
			}
		}
		config.MaxFeatures = maxFeatures
		return nil
	}
}

// WithRandomState sets the random state for reproducible results
func WithRandomState(seed int64) TreeOption {
	return func(config *TreeConfig) error {
		config.RandomState = rand.New(rand.NewSource(seed))
		return nil
	}
}

// DecisionTree represents a decision tree model
type DecisionTree struct {
	Root            *TreeNode           `json:"root"`              // Root node of the tree
	Features        map[string]*Feature `json:"features"`          // Features used in the tree
	FeaturesByIndex []*Feature          `json:"features_by_index"` // Features indexed by column number
	TargetType      FeatureType         `json:"target_type"`       // Type of the target variable
	TargetColumn    string              `json:"target_column"`     // Name of the target column
	Config          TreeConfig          `json:"config"`            // Tree configuration

	// Tree statistics
	NodeCount int `json:"node_count"` // Total number of nodes
	LeafCount int `json:"leaf_count"` // Number of leaf nodes
	Depth     int `json:"depth"`      // Actual depth of the tree
}

// NewDecisionTree creates a new decision tree with basic parameters
func NewDecisionTree(targetType FeatureType, maxDepth, minSamples int) (*DecisionTree, error) {
	if !isValidFeatureType(targetType) {
		return nil, &ModelError{
			Op:     "create_tree",
			Field:  "target_type",
			Value:  string(targetType),
			Reason: "invalid target type",
		}
	}

	if maxDepth <= 0 {
		return nil, &ModelError{
			Op:     "create_tree",
			Field:  "max_depth",
			Value:  fmt.Sprintf("%d", maxDepth),
			Reason: "max depth must be positive",
		}
	}

	if minSamples <= 0 {
		return nil, &ModelError{
			Op:     "create_tree",
			Field:  "min_samples",
			Value:  fmt.Sprintf("%d", minSamples),
			Reason: "min samples must be positive",
		}
	}

	// Set default criterion based on target type
	criterion := GiniCriterion
	if targetType == NumericFeature {
		criterion = MSECriterion
	}

	return &DecisionTree{
		Features:        make(map[string]*Feature),
		FeaturesByIndex: make([]*Feature, 0),
		TargetType:      targetType,
		TargetColumn:    "", // Empty for backward compatibility
		Config: TreeConfig{
			MaxDepth:    maxDepth,
			MinSamples:  minSamples,
			TargetType:  targetType,
			Criterion:   criterion,
			MaxFeatures: -1, // Use all features by default
		},
		NodeCount: 0,
		LeafCount: 0,
		Depth:     0,
	}, nil
}

// NewDecisionTreeWithTarget creates a new decision tree with target column information
func NewDecisionTreeWithTarget(targetType FeatureType, targetColumn string, maxDepth, minSamples int) (*DecisionTree, error) {
	if !isValidFeatureType(targetType) {
		return nil, &ModelError{
			Op:     "create_tree",
			Field:  "target_type",
			Value:  string(targetType),
			Reason: "invalid target type",
		}
	}

	if maxDepth <= 0 {
		return nil, &ModelError{
			Op:     "create_tree",
			Field:  "max_depth",
			Value:  fmt.Sprintf("%d", maxDepth),
			Reason: "max depth must be positive",
		}
	}

	if minSamples <= 0 {
		return nil, &ModelError{
			Op:     "create_tree",
			Field:  "min_samples",
			Value:  fmt.Sprintf("%d", minSamples),
			Reason: "min samples must be positive",
		}
	}

	// Set default criterion based on target type
	criterion := GiniCriterion
	if targetType == NumericFeature {
		criterion = MSECriterion
	}

	return &DecisionTree{
		Features:        make(map[string]*Feature),
		FeaturesByIndex: make([]*Feature, 0),
		TargetType:      targetType,
		TargetColumn:    targetColumn,
		Config: TreeConfig{
			MaxDepth:    maxDepth,
			MinSamples:  minSamples,
			TargetType:  targetType,
			Criterion:   criterion,
			MaxFeatures: -1, // Use all features by default
		},
		NodeCount: 0,
		LeafCount: 0,
		Depth:     0,
	}, nil
}

// NewDecisionTreeWithOptions creates a new decision tree using functional options
func NewDecisionTreeWithOptions(options ...TreeOption) (*DecisionTree, error) {
	// Default configuration
	config := &TreeConfig{
		MaxDepth:    10,
		MinSamples:  2,
		TargetType:  CategoricalFeature,
		Criterion:   GiniCriterion,
		MaxFeatures: -1,
	}

	// Apply options
	for _, option := range options {
		if err := option(config); err != nil {
			return nil, fmt.Errorf("invalid tree option: %w", err)
		}
	}

	tree := &DecisionTree{
		Features:        make(map[string]*Feature),
		FeaturesByIndex: make([]*Feature, 0),
		TargetType:      config.TargetType,
		TargetColumn:    "", // Will be set later if needed
		Config:          *config,
		NodeCount:       0,
		LeafCount:       0,
		Depth:           0,
	}

	return tree, nil
}

// SetTargetColumn sets the target column name for the tree
func (dt *DecisionTree) SetTargetColumn(targetColumn string) {
	dt.TargetColumn = targetColumn
}

// Validate performs comprehensive validation of the decision tree
func (dt *DecisionTree) Validate() error {
	if dt.Config.MaxDepth <= 0 {
		return &ModelError{
			Op:     "validate_tree",
			Field:  "max_depth",
			Value:  fmt.Sprintf("%d", dt.Config.MaxDepth),
			Reason: "max depth must be positive",
		}
	}

	if dt.Config.MinSamples <= 0 {
		return &ModelError{
			Op:     "validate_tree",
			Field:  "min_samples",
			Value:  fmt.Sprintf("%d", dt.Config.MinSamples),
			Reason: "min samples must be positive",
		}
	}

	if !isValidFeatureType(dt.TargetType) {
		return &ModelError{
			Op:     "validate_tree",
			Field:  "target_type",
			Value:  string(dt.TargetType),
			Reason: "invalid target type",
		}
	}

	// Validate features
	for name, feature := range dt.Features {
		if err := feature.Validate(); err != nil {
			return &ModelError{
				Op:     "validate_tree_features",
				Field:  "feature",
				Value:  name,
				Reason: fmt.Sprintf("feature validation failed: %v", err),
				Err:    err,
			}
		}
	}

	// Validate feature index consistency
	if len(dt.FeaturesByIndex) != len(dt.Features) {
		return &ModelError{
			Op:     "validate_tree",
			Field:  "features_index",
			Reason: "feature count mismatch between map and index",
		}
	}

	// Validate tree structure if grown
	if dt.Root != nil {
		return dt.validateTreeStructure(dt.Root, 0)
	}

	return nil
}

// validateTreeStructure recursively validates the tree structure
func (dt *DecisionTree) validateTreeStructure(node *TreeNode, depth int) error {
	if node == nil {
		return nil
	}

	if depth > dt.Config.MaxDepth {
		return &ModelError{
			Op:     "validate_tree_structure",
			Field:  "depth",
			Value:  fmt.Sprintf("%d", depth),
			Reason: fmt.Sprintf("node depth exceeds max depth %d", dt.Config.MaxDepth),
		}
	}

	// Validate the node itself
	if err := node.Validate(); err != nil {
		return &ModelError{
			Op:     "validate_tree_structure",
			Field:  "node",
			Reason: fmt.Sprintf("node validation failed at depth %d: %v", depth, err),
			Err:    err,
		}
	}

	// Recursively validate children
	if !node.IsLeaf() {
		if node.Left != nil {
			if err := dt.validateTreeStructure(node.Left, depth+1); err != nil {
				return err
			}
		}

		if node.Right != nil {
			if err := dt.validateTreeStructure(node.Right, depth+1); err != nil {
				return err
			}
		}

		for category, child := range node.Categories {
			if child != nil {
				if err := dt.validateTreeStructure(child, depth+1); err != nil {
					return &ModelError{
						Op:     "validate_tree_structure",
						Field:  "category_child",
						Value:  fmt.Sprintf("%v", category),
						Reason: fmt.Sprintf("category child validation failed: %v", err),
						Err:    err,
					}
				}
			}
		}
	}

	return nil
}

// AddFeature adds a feature to the decision tree with validation
func (dt *DecisionTree) AddFeature(name string, featureType FeatureType) (*Feature, error) {
	logger.Debug( fmt.Sprintf("Adding feature: name='%s', type=%v", name, featureType))

	trimmedName := strings.TrimSpace(name)
	if trimmedName == "" {
		logger.Error("Feature name cannot be empty or whitespace-only")
		return nil, &ModelError{
			Op:     "add_feature",
			Field:  "name",
			Reason: "feature name cannot be empty or whitespace-only",
		}
	}

	// Check for duplicate names
	if _, exists := dt.Features[trimmedName]; exists {
		logger.Error(fmt.Sprintf("Feature with name '%s' already exists", trimmedName))
		return nil, &ModelError{
			Op:     "add_feature",
			Field:  "name",
			Value:  trimmedName,
			Reason: "feature with this name already exists",
		}
	}

	// Create feature with current index
	index := len(dt.Features)
	feature, err := NewFeature(trimmedName, featureType, index)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to create feature '%s': %v", trimmedName, err))
		return nil, &ModelError{
			Op:     "add_feature",
			Field:  "feature_creation",
			Reason: fmt.Sprintf("failed to create feature: %v", err),
			Err:    err,
		}
	}

	// Add to both storage methods
	dt.Features[trimmedName] = feature
	dt.FeaturesByIndex = append(dt.FeaturesByIndex, feature)

	logger.Debug( fmt.Sprintf("Successfully added feature '%s' at index %d", trimmedName, index))
	return feature, nil
}

// GetFeatureByName returns a feature by its name
func (dt *DecisionTree) GetFeatureByName(name string) *Feature {
	return dt.Features[name]
}

// GetFeatureByIndex returns a feature by its column index
func (dt *DecisionTree) GetFeatureByIndex(index int) (*Feature, error) {
	if index < 0 || index >= len(dt.FeaturesByIndex) {
		return nil, &ModelError{
			Op:     "get_feature_by_index",
			Field:  "index",
			Value:  fmt.Sprintf("%d", index),
			Reason: fmt.Sprintf("index out of range [0, %d)", len(dt.FeaturesByIndex)),
		}
	}
	return dt.FeaturesByIndex[index], nil
}

// GetFeatureCount returns the number of features in the tree
func (dt *DecisionTree) GetFeatureCount() int {
	return len(dt.Features)
}

// HasFeature checks if a feature exists in the tree
func (dt *DecisionTree) HasFeature(name string) bool {
	_, exists := dt.Features[name]
	return exists
}

// IsGrown checks if the tree has been trained (has a root node)
func (dt *DecisionTree) IsGrown() bool {
	return dt.Root != nil
}

// GetDepth returns the actual depth of the tree
func (dt *DecisionTree) GetDepth() int {
	if dt.Root == nil {
		return 0
	}
	return dt.calculateNodeDepth(dt.Root)
}

// calculateNodeDepth recursively calculates the depth of a subtree
func (dt *DecisionTree) calculateNodeDepth(node *TreeNode) int {
	if node == nil || node.IsLeaf() {
		return 1
	}

	maxChildDepth := 0

	// Check left and right children for numeric/date features
	if node.Left != nil {
		if depth := dt.calculateNodeDepth(node.Left); depth > maxChildDepth {
			maxChildDepth = depth
		}
	}
	if node.Right != nil {
		if depth := dt.calculateNodeDepth(node.Right); depth > maxChildDepth {
			maxChildDepth = depth
		}
	}

	// Check categorical children
	for _, child := range node.Categories {
		if child != nil {
			if depth := dt.calculateNodeDepth(child); depth > maxChildDepth {
				maxChildDepth = depth
			}
		}
	}

	return maxChildDepth + 1
}

// GetNodeCount returns the total number of nodes in the tree
func (dt *DecisionTree) GetNodeCount() int {
	if dt.Root == nil {
		return 0
	}
	return dt.countNodes(dt.Root)
}

// countNodes recursively counts all nodes in the tree
func (dt *DecisionTree) countNodes(node *TreeNode) int {
	if node == nil {
		return 0
	}

	count := 1 // Count this node

	// Count children
	if !node.IsLeaf() {
		count += dt.countNodes(node.Left)
		count += dt.countNodes(node.Right)

		for _, child := range node.Categories {
			count += dt.countNodes(child)
		}
	}

	return count
}

// GetLeafCount returns the number of leaf nodes in the tree
func (dt *DecisionTree) GetLeafCount() int {
	if dt.Root == nil {
		return 0
	}
	return dt.countLeaves(dt.Root)
}

// countLeaves recursively counts leaf nodes in the tree
func (dt *DecisionTree) countLeaves(node *TreeNode) int {
	if node == nil {
		return 0
	}

	if node.IsLeaf() {
		return 1
	}

	count := 0
	count += dt.countLeaves(node.Left)
	count += dt.countLeaves(node.Right)

	for _, child := range node.Categories {
		count += dt.countLeaves(child)
	}

	return count
}

// UpdateStatistics recalculates tree statistics
func (dt *DecisionTree) UpdateStatistics() {
	logger.Debug( "Updating tree statistics")

	dt.NodeCount = dt.GetNodeCount()
	dt.LeafCount = dt.GetLeafCount()
	dt.Depth = dt.GetDepth()

	logger.Debug( fmt.Sprintf("Tree statistics updated: %d nodes, %d leaves, depth %d", dt.NodeCount, dt.LeafCount, dt.Depth))
}

// FeatureImportance represents the importance of a feature in the tree
type FeatureImportance struct {
	Feature    *Feature `json:"feature"`
	Importance float64  `json:"importance"`
	UsageCount int      `json:"usage_count"`
}

// CalculateFeatureImportance calculates feature importance based on tree structure
func (dt *DecisionTree) CalculateFeatureImportance() map[string]*FeatureImportance {
	importance := make(map[string]*FeatureImportance)

	// Initialize importance for all features in the tree
	for name, feature := range dt.Features {
		importance[name] = &FeatureImportance{
			Feature:    feature,
			Importance: 0.0,
			UsageCount: 0,
		}
	}

	// If no features exist, return empty map
	if len(dt.Features) == 0 {
		return importance
	}

	if dt.Root != nil {
		totalSamples := float64(dt.Root.Samples)
		if totalSamples > 0 {
			dt.calculateNodeImportance(dt.Root, importance, totalSamples)
		}
	}

	return importance
}

// calculateNodeImportance recursively calculates feature importance
func (dt *DecisionTree) calculateNodeImportance(node *TreeNode, importance map[string]*FeatureImportance, totalSamples float64) {
	if node == nil || node.IsLeaf() || node.Feature == nil {
		return
	}

	// Calculate weighted impurity decrease for this split
	if node.Samples > 0 {
		nodeSampleRatio := float64(node.Samples) / totalSamples

		// Calculate weighted impurity of children
		childImpurity := 0.0
		childSamples := 0

		if node.Left != nil && node.Left.Samples > 0 {
			leftRatio := float64(node.Left.Samples) / float64(node.Samples)
			childImpurity += leftRatio * node.Left.Impurity
			childSamples += node.Left.Samples
		}

		if node.Right != nil && node.Right.Samples > 0 {
			rightRatio := float64(node.Right.Samples) / float64(node.Samples)
			childImpurity += rightRatio * node.Right.Impurity
			childSamples += node.Right.Samples
		}

		for _, child := range node.Categories {
			if child != nil && child.Samples > 0 {
				childRatio := float64(child.Samples) / float64(node.Samples)
				childImpurity += childRatio * child.Impurity
				childSamples += child.Samples
			}
		}

		// Calculate importance contribution
		impurityDecrease := node.Impurity - childImpurity
		featureImportance := nodeSampleRatio * impurityDecrease

		// Update feature importance
		if featureInfo, exists := importance[node.Feature.Name]; exists {
			featureInfo.Importance += featureImportance
			featureInfo.UsageCount++
		}
	}

	// Recursively process children
	dt.calculateNodeImportance(node.Left, importance, totalSamples)
	dt.calculateNodeImportance(node.Right, importance, totalSamples)

	for _, child := range node.Categories {
		dt.calculateNodeImportance(child, importance, totalSamples)
	}
}

// GetFeatureNames returns a list of all feature names
func (dt *DecisionTree) GetFeatureNames() []string {
	names := make([]string, 0, len(dt.Features))
	for name := range dt.Features {
		names = append(names, name)
	}
	return names
}

// RemoveFeature removes a feature from the tree (only if tree is not grown)
func (dt *DecisionTree) RemoveFeature(name string) error {
	if dt.IsGrown() {
		return &ModelError{
			Op:     "remove_feature",
			Field:  "tree_state",
			Reason: "cannot remove features from a grown tree",
		}
	}

	feature, exists := dt.Features[name]
	if !exists {
		return &ModelError{
			Op:     "remove_feature",
			Field:  "name",
			Value:  name,
			Reason: "feature does not exist",
		}
	}

	// Remove from map
	delete(dt.Features, name)

	// Remove from index and reindex remaining features
	newIndex := make([]*Feature, 0, len(dt.FeaturesByIndex)-1)
	newColumnNumber := 0

	for _, f := range dt.FeaturesByIndex {
		if f != feature {
			f.ColumnNumber = newColumnNumber
			newIndex = append(newIndex, f)
			newColumnNumber++
		}
	}

	dt.FeaturesByIndex = newIndex

	return nil
}

// Clone creates a deep copy of the tree structure (without the actual tree nodes)
func (dt *DecisionTree) Clone() (*DecisionTree, error) {
	newTree := &DecisionTree{
		Features:        make(map[string]*Feature),
		FeaturesByIndex: make([]*Feature, 0, len(dt.FeaturesByIndex)),
		TargetType:      dt.TargetType,
		Config:          dt.Config, // Shallow copy of config
		NodeCount:       0,         // Reset statistics
		LeafCount:       0,
		Depth:           0,
	}

	// Clone features
	for name, feature := range dt.Features {
		newFeature, err := NewFeature(feature.Name, feature.Type, feature.ColumnNumber)
		if err != nil {
			return nil, &ModelError{
				Op:     "clone_tree",
				Field:  "feature",
				Value:  name,
				Reason: fmt.Sprintf("failed to clone feature: %v", err),
				Err:    err,
			}
		}

		// Copy feature properties
		newFeature.Values = make([]interface{}, len(feature.Values))
		copy(newFeature.Values, feature.Values)

		newFeature.CategoricalValues = make([]string, len(feature.CategoricalValues))
		copy(newFeature.CategoricalValues, feature.CategoricalValues)

		if feature.NumericRange != nil {
			newFeature.NumericRange = &Range{
				Min: feature.NumericRange.Min,
				Max: feature.NumericRange.Max,
			}
		}

		newFeature.Min = feature.Min
		newFeature.Max = feature.Max

		newTree.Features[name] = newFeature
		newTree.FeaturesByIndex = append(newTree.FeaturesByIndex, newFeature)
	}

	return newTree, nil
}

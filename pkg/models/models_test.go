package models

import (
	"math"
	"testing"
)

func TestFeature(t *testing.T) {
	// Test creation of different feature types
	numFeature, err := NewFeature("age", NumericFeature, 0)
	if err != nil {
		t.Fatalf("Failed to create numeric feature: %v", err)
	}
	if numFeature.Name != "age" || numFeature.Type != NumericFeature || numFeature.ColumnNumber != 0 {
		t.Errorf("Numeric feature not initialized correctly")
	}

	catFeature, err := NewFeature("color", CategoricalFeature, 1)
	if err != nil {
		t.Fatalf("Failed to create categorical feature: %v", err)
	}
	if catFeature.Name != "color" || catFeature.Type != CategoricalFeature || catFeature.ColumnNumber != 1 {
		t.Errorf("Categorical feature not initialized correctly")
	}

	datetimeFeature, err := NewFeature("datetime", DateTimeFeature, 2)
	if err != nil {
		t.Fatalf("Failed to create datetime feature: %v", err)
	}
	if datetimeFeature.Name != "datetime" || datetimeFeature.Type != DateTimeFeature || datetimeFeature.ColumnNumber != 2 {
		t.Errorf("DateTime feature not initialized correctly")
	}

	// Test setting range for numeric feature
	err = numFeature.SetRange(0, 100)
	if err != nil {
		t.Errorf("Failed to set range: %v", err)
	}
	if numFeature.Min != 0 || numFeature.Max != 100 {
		t.Errorf("Feature range not set correctly")
	}

	// Test setting range for datetime feature
	err = datetimeFeature.SetRange(20230101000000, 20231231235959)
	if err != nil {
		t.Errorf("Failed to set range for datetime feature: %v", err)
	}
	if datetimeFeature.Min != 20230101000000 || datetimeFeature.Max != 20231231235959 {
		t.Errorf("DateTime feature range not set correctly")
	}

	// Test adding values for categorical feature
	added, err := catFeature.AddValue("red")
	if err != nil {
		t.Errorf("Failed to add value to categorical feature: %v", err)
	}
	if !added {
		t.Errorf("Should have added new value to categorical feature")
	}

	added, err = catFeature.AddValue("blue")
	if err != nil {
		t.Errorf("Failed to add second value to categorical feature: %v", err)
	}
	if !added {
		t.Errorf("Should have added second value to categorical feature")
	}

	// Test adding duplicate value
	added, err = catFeature.AddValue("red")
	if err != nil {
		t.Errorf("Should not error on duplicate value: %v", err)
	}
	if added {
		t.Errorf("Should not add duplicate value to categorical feature")
	}

	// Test adding value to non-categorical feature
	_, err = numFeature.AddValue(42)
	if err == nil {
		t.Errorf("Should error when adding values to numeric feature")
	}

	// Verify values were added correctly
	if len(catFeature.Values) != 2 || catFeature.Values[0] != "red" || catFeature.Values[1] != "blue" {
		t.Errorf("Categorical feature values not stored correctly")
	}

	// Test modern categorical values
	if len(catFeature.CategoricalValues) != 2 {
		t.Errorf("Expected 2 categorical values, got %d", len(catFeature.CategoricalValues))
	}
}

func TestNewRange(t *testing.T) {
	// Test valid range
	r, err := NewRange(0, 100)
	if err != nil {
		t.Errorf("Failed to create valid range: %v", err)
	}
	if r.Min != 0 || r.Max != 100 {
		t.Errorf("Range values not set correctly")
	}

	// Test invalid range - min > max
	_, err = NewRange(100, 0)
	if err == nil {
		t.Error("Should error when min > max")
	}

	// Test invalid range - min == max
	_, err = NewRange(50, 50)
	if err == nil {
		t.Error("Should error when min == max")
	}

	// Test range contains
	if !r.Contains(50) {
		t.Error("Range should contain 50")
	}
	if r.Contains(150) {
		t.Error("Range should not contain 150")
	}
}

func TestFeatureValidate(t *testing.T) {
	// Test valid feature validation
	feature, _ := NewFeature("test", NumericFeature, 0)
	err := feature.Validate()
	if err != nil {
		t.Errorf("Valid feature should pass validation: %v", err)
	}

	// Test invalid name
	feature.Name = ""
	err = feature.Validate()
	if err == nil {
		t.Error("Should error on empty name")
	}

	// Test invalid column number
	feature.Name = "test"
	feature.ColumnNumber = -1
	err = feature.Validate()
	if err == nil {
		t.Error("Should error on negative column number")
	}

	// Test invalid type
	feature.ColumnNumber = 0
	feature.Type = FeatureType("invalid")
	err = feature.Validate()
	if err == nil {
		t.Error("Should error on invalid type")
	}
}

func TestFeatureCategoricalValidation(t *testing.T) {
	feature, _ := NewFeature("color", CategoricalFeature, 0)

	// Test empty categorical values
	err := feature.Validate()
	if err != nil {
		t.Errorf("Empty categorical feature should be valid: %v", err)
	}

	// Add valid values
	feature.AddCategoricalValue("red")
	feature.AddCategoricalValue("blue")
	err = feature.Validate()
	if err != nil {
		t.Errorf("Valid categorical feature should pass validation: %v", err)
	}

	// Test empty value in CategoricalValues
	feature.CategoricalValues = append(feature.CategoricalValues, "")
	err = feature.Validate()
	if err == nil {
		t.Error("Should error on empty categorical value")
	}

	// Test duplicate values in CategoricalValues
	feature.CategoricalValues = []string{"red", "blue", "red"}
	err = feature.Validate()
	if err == nil {
		t.Error("Should error on duplicate categorical values")
	}

	// Test legacy Values validation
	feature.CategoricalValues = []string{"red", "blue"}
	feature.Values = []interface{}{nil}
	err = feature.Validate()
	if err == nil {
		t.Error("Should error on nil value in Values")
	}

	// Test empty string in Values
	feature.Values = []interface{}{""}
	err = feature.Validate()
	if err == nil {
		t.Error("Should error on empty string in Values")
	}

	// Test duplicate in Values
	feature.Values = []interface{}{"red", "blue", "red"}
	err = feature.Validate()
	if err == nil {
		t.Error("Should error on duplicate values in Values")
	}
}

func TestFeatureNumericValidation(t *testing.T) {
	feature, _ := NewFeature("age", NumericFeature, 0)

	// Test valid numeric feature
	err := feature.Validate()
	if err != nil {
		t.Errorf("Valid numeric feature should pass validation: %v", err)
	}

	// Test with NumericRange
	feature.NumericRange = &Range{Min: 0, Max: 100}
	err = feature.Validate()
	if err != nil {
		t.Errorf("Valid numeric range should pass validation: %v", err)
	}

	// Test invalid NumericRange - min > max
	feature.NumericRange = &Range{Min: 100, Max: 0}
	err = feature.Validate()
	if err == nil {
		t.Error("Should error when NumericRange min > max")
	}

	// Test invalid NumericRange - min == max
	feature.NumericRange = &Range{Min: 50, Max: 50}
	err = feature.Validate()
	if err == nil {
		t.Error("Should error when NumericRange min == max")
	}

	// Test with legacy Min/Max
	feature.NumericRange = nil
	feature.Min = 0
	feature.Max = 100
	err = feature.Validate()
	if err != nil {
		t.Errorf("Valid legacy range should pass validation: %v", err)
	}

	// Test invalid legacy range
	feature.Min = 100
	feature.Max = 0
	err = feature.Validate()
	if err == nil {
		t.Error("Should error when legacy min > max")
	}

	// Test legacy range min == max
	feature.Min = 50
	feature.Max = 50
	err = feature.Validate()
	if err == nil {
		t.Error("Should error when legacy min == max")
	}
}

func TestFeatureAddValue(t *testing.T) {
	catFeature, _ := NewFeature("color", CategoricalFeature, 0)

	// Test adding valid value
	added, err := catFeature.AddValue("red")
	if err != nil {
		t.Errorf("Failed to add value: %v", err)
	}
	if !added {
		t.Error("Should have added new value")
	}

	// Test adding duplicate
	added, err = catFeature.AddValue("red")
	if err != nil {
		t.Errorf("Should not error on duplicate: %v", err)
	}
	if added {
		t.Error("Should not add duplicate value")
	}

	// Test adding nil value
	_, err = catFeature.AddValue(nil)
	if err == nil {
		t.Error("Should error on nil value")
	}

	// Test adding empty string
	_, err = catFeature.AddValue("")
	if err == nil {
		t.Error("Should error on empty string")
	}

	// Test adding whitespace value
	_, err = catFeature.AddValue("   ")
	if err == nil {
		t.Error("Should error on whitespace-only value")
	}

	// Test on non-categorical feature
	numFeature, _ := NewFeature("age", NumericFeature, 0)
	_, err = numFeature.AddValue("test")
	if err == nil {
		t.Error("Should error when adding value to numeric feature")
	}
}

func TestFeatureAddCategoricalValue(t *testing.T) {
	catFeature, _ := NewFeature("color", CategoricalFeature, 0)

	// Test adding valid value
	added, err := catFeature.AddCategoricalValue("red")
	if err != nil {
		t.Errorf("Failed to add categorical value: %v", err)
	}
	if !added {
		t.Error("Should have added new categorical value")
	}

	// Test adding duplicate
	added, err = catFeature.AddCategoricalValue("red")
	if err != nil {
		t.Errorf("Should not error on duplicate: %v", err)
	}
	if added {
		t.Error("Should not add duplicate value")
	}

	// Test adding empty value
	_, err = catFeature.AddCategoricalValue("")
	if err == nil {
		t.Error("Should error on empty categorical value")
	}

	// Test adding whitespace value
	_, err = catFeature.AddCategoricalValue("   ")
	if err == nil {
		t.Error("Should error on whitespace-only categorical value")
	}

	// Test on non-categorical feature
	numFeature, _ := NewFeature("age", NumericFeature, 0)
	_, err = numFeature.AddCategoricalValue("test")
	if err == nil {
		t.Error("Should error when adding categorical value to numeric feature")
	}
}

func TestTreeNodeValidate(t *testing.T) {
	// Test valid leaf node
	leafNode, _ := NewLeafNode("A", map[interface{}]int{"A": 5, "B": 3}, 8)
	err := leafNode.Validate()
	if err != nil {
		t.Errorf("Valid leaf node should pass validation: %v", err)
	}

	// Test invalid confidence
	leafNode.Confidence = 1.5
	err = leafNode.Validate()
	if err == nil {
		t.Error("Should error on confidence > 1.0")
	}

	leafNode.Confidence = -0.1
	err = leafNode.Validate()
	if err == nil {
		t.Error("Should error on confidence < 0.0")
	}

	// Test invalid impurity
	leafNode.Confidence = 0.5
	leafNode.Impurity = 1.5
	err = leafNode.Validate()
	if err == nil {
		t.Error("Should error on impurity > 1.0")
	}

	leafNode.Impurity = -0.1
	err = leafNode.Validate()
	if err == nil {
		t.Error("Should error on impurity < 0.0")
	}

	// Test decision node validation with children
	feature, _ := NewFeature("age", NumericFeature, 0)
	decisionNode, _ := NewDecisionNode(feature, 30.0)

	// Add children to make it a valid decision node
	leftChild, _ := NewLeafNode("young", map[interface{}]int{"young": 5}, 5)
	rightChild, _ := NewLeafNode("old", map[interface{}]int{"old": 3}, 3)
	decisionNode.SetLeftChild(leftChild)
	decisionNode.SetRightChild(rightChild)

	err = decisionNode.Validate()
	if err != nil {
		t.Errorf("Valid decision node should pass validation: %v", err)
	}

	// Test decision node without feature
	decisionNode.Feature = nil
	err = decisionNode.Validate()
	if err == nil {
		t.Error("Should error on decision node without feature")
	}
}

func TestTreeNodeOperations(t *testing.T) {
	// Test AddSample
	leafNode, _ := NewLeafNode("A", map[interface{}]int{"A": 5}, 5)

	err := leafNode.AddSample("B")
	if err != nil {
		t.Errorf("Failed to add sample: %v", err)
	}

	if leafNode.Samples != 6 {
		t.Errorf("Expected 6 samples after adding one, got %d", leafNode.Samples)
	}

	if leafNode.ClassDistribution["B"] != 1 {
		t.Errorf("Expected 1 sample of class B, got %d", leafNode.ClassDistribution["B"])
	}

	// Test adding nil sample
	err = leafNode.AddSample(nil)
	if err == nil {
		t.Error("Should error when adding nil sample")
	}

	// Test UpdateStatistics
	leafNode.UpdateStatistics()
	expectedConfidence := 5.0 / 6.0 // A is still majority
	if math.Abs(leafNode.Confidence-expectedConfidence) > 0.001 {
		t.Errorf("Expected confidence %f, got %f", expectedConfidence, leafNode.Confidence)
	}

	// Test GetChildCount
	feature, _ := NewFeature("age", NumericFeature, 0)
	decisionNode, _ := NewDecisionNode(feature, 30.0)

	if decisionNode.GetChildCount() != 0 {
		t.Errorf("Expected 0 children, got %d", decisionNode.GetChildCount())
	}

	leftChild, _ := NewLeafNode("young", map[interface{}]int{"young": 5}, 5)
	rightChild, _ := NewLeafNode("old", map[interface{}]int{"old": 5}, 5)

	decisionNode.SetLeftChild(leftChild)
	decisionNode.SetRightChild(rightChild)

	if decisionNode.GetChildCount() != 2 {
		t.Errorf("Expected 2 children, got %d", decisionNode.GetChildCount())
	}

	// Test GetChildren
	children := decisionNode.GetChildren()
	if len(children) != 2 {
		t.Errorf("Expected 2 children, got %d", len(children))
	}

	// Test leaf node children
	leafChildren := leafNode.GetChildren()
	if leafChildren != nil {
		t.Error("Leaf node should have no children")
	}
}

func TestTreeNodeChildOperations(t *testing.T) {
	// Test setting children on leaf node (should error)
	leafNode, _ := NewLeafNode("A", map[interface{}]int{"A": 5}, 5)
	childNode, _ := NewLeafNode("B", map[interface{}]int{"B": 3}, 3)

	err := leafNode.SetLeftChild(childNode)
	if err == nil {
		t.Error("Should error when setting left child on leaf node")
	}

	err = leafNode.SetRightChild(childNode)
	if err == nil {
		t.Error("Should error when setting right child on leaf node")
	}

	err = leafNode.SetChild("category", childNode)
	if err == nil {
		t.Error("Should error when setting categorical child on leaf node")
	}

	// Test setting children on decision nodes
	numFeature, _ := NewFeature("age", NumericFeature, 0)
	numDecisionNode, _ := NewDecisionNode(numFeature, 30.0)

	err = numDecisionNode.SetLeftChild(childNode)
	if err != nil {
		t.Errorf("Failed to set left child on numeric decision node: %v", err)
	}

	err = numDecisionNode.SetRightChild(childNode)
	if err != nil {
		t.Errorf("Failed to set right child on numeric decision node: %v", err)
	}

	// Test setting categorical child on numeric node (should error)
	err = numDecisionNode.SetChild("category", childNode)
	if err == nil {
		t.Error("Should error when setting categorical child on numeric decision node")
	}

	// Test categorical decision node
	catFeature, _ := NewFeature("color", CategoricalFeature, 0)
	catDecisionNode, _ := NewCategoricalDecisionNode(catFeature)

	err = catDecisionNode.SetChild("red", childNode)
	if err != nil {
		t.Errorf("Failed to set categorical child: %v", err)
	}

	// Test setting left/right child on categorical node (should error)
	err = catDecisionNode.SetLeftChild(childNode)
	if err == nil {
		t.Error("Should error when setting left child on categorical decision node")
	}

	err = catDecisionNode.SetRightChild(childNode)
	if err == nil {
		t.Error("Should error when setting right child on categorical decision node")
	}

	// Test setting child with nil category
	err = catDecisionNode.SetChild(nil, childNode)
	if err == nil {
		t.Error("Should error when setting child with nil category")
	}

	// Test setting children on node without feature
	nodeWithoutFeature := &TreeNode{Type: DecisionNode}
	err = nodeWithoutFeature.SetChild("test", childNode)
	if err == nil {
		t.Error("Should error when setting child on node without feature")
	}
}

func TestDecisionTreeFeatureManagement(t *testing.T) {
	tree, _ := NewDecisionTree(CategoricalFeature, 5, 2)

	// Test adding feature with empty name
	_, err := tree.AddFeature("", NumericFeature)
	if err == nil {
		t.Error("Should error on empty feature name")
	}

	// Test adding feature with whitespace name
	_, err = tree.AddFeature("   ", NumericFeature)
	if err == nil {
		t.Error("Should error on whitespace feature name")
	}

	// Test adding duplicate feature
	tree.AddFeature("age", NumericFeature)
	_, err = tree.AddFeature("age", NumericFeature)
	if err == nil {
		t.Error("Should error on duplicate feature name")
	}

	// Test GetFeatureByIndex with invalid index
	_, err = tree.GetFeatureByIndex(-1)
	if err == nil {
		t.Error("Should error on negative index")
	}

	_, err = tree.GetFeatureByIndex(100)
	if err == nil {
		t.Error("Should error on index out of range")
	}

	// Test GetFeatureNames
	tree.AddFeature("color", CategoricalFeature)
	names := tree.GetFeatureNames()
	if len(names) != 2 {
		t.Errorf("Expected 2 feature names, got %d", len(names))
	}

	// Test RemoveFeature on grown tree
	feature, _ := NewFeature("test", NumericFeature, 0)
	rootNode, _ := NewDecisionNode(feature, 30.0)
	tree.Root = rootNode

	err = tree.RemoveFeature("age")
	if err == nil {
		t.Error("Should error when removing feature from grown tree")
	}

	// Test RemoveFeature on non-grown tree
	tree2, _ := NewDecisionTree(CategoricalFeature, 5, 2)
	tree2.AddFeature("age", NumericFeature)
	tree2.AddFeature("color", CategoricalFeature)

	err = tree2.RemoveFeature("age")
	if err != nil {
		t.Errorf("Failed to remove feature: %v", err)
	}

	if tree2.GetFeatureCount() != 1 {
		t.Errorf("Expected 1 feature after removal, got %d", tree2.GetFeatureCount())
	}

	// Test removing non-existent feature
	err = tree2.RemoveFeature("nonexistent")
	if err == nil {
		t.Error("Should error when removing non-existent feature")
	}
}

func TestTreeValidate(t *testing.T) {
	tree, _ := NewDecisionTree(CategoricalFeature, 5, 2)

	// Test valid empty tree
	err := tree.Validate()
	if err != nil {
		t.Errorf("Valid empty tree should pass validation: %v", err)
	}

	// Test invalid tree configuration
	tree.Config.MaxDepth = 0
	err = tree.Validate()
	if err == nil {
		t.Error("Should error on invalid max depth")
	}

	tree.Config.MaxDepth = 5
	tree.Config.MinSamples = 0
	err = tree.Validate()
	if err == nil {
		t.Error("Should error on invalid min samples")
	}

	tree.Config.MinSamples = 2
	tree.TargetType = FeatureType("invalid")
	err = tree.Validate()
	if err == nil {
		t.Error("Should error on invalid target type")
	}

	// Test with invalid feature
	tree.TargetType = CategoricalFeature
	invalidFeature := &Feature{
		Name:         "",
		Type:         NumericFeature,
		ColumnNumber: 0,
	}
	tree.Features["invalid"] = invalidFeature
	tree.FeaturesByIndex = append(tree.FeaturesByIndex, invalidFeature)

	err = tree.Validate()
	if err == nil {
		t.Error("Should error on invalid feature")
	}

	// Test feature count mismatch
	tree.Features = make(map[string]*Feature)
	tree.FeaturesByIndex = []*Feature{invalidFeature}

	err = tree.Validate()
	if err == nil {
		t.Error("Should error on feature count mismatch")
	}
}

func TestIsValidFeatureType(t *testing.T) {
	// Test valid types
	validTypes := []FeatureType{NumericFeature, CategoricalFeature, DateTimeFeature}
	for _, ft := range validTypes {
		if !isValidFeatureType(ft) {
			t.Errorf("Feature type %s should be valid", ft)
		}
	}

	// Test invalid type
	if isValidFeatureType(FeatureType("invalid")) {
		t.Error("Invalid feature type should not be valid")
	}
}

func TestFeatureValidation(t *testing.T) {
	// Test invalid feature creation
	tests := []struct {
		name         string
		featureName  string
		featureType  FeatureType
		columnNumber int
		expectError  bool
		errorField   string
	}{
		{
			name:         "empty name",
			featureName:  "",
			featureType:  NumericFeature,
			columnNumber: 0,
			expectError:  true,
			errorField:   "name",
		},
		{
			name:         "whitespace name",
			featureName:  "   ",
			featureType:  NumericFeature,
			columnNumber: 0,
			expectError:  true,
			errorField:   "name",
		},
		{
			name:         "invalid type",
			featureName:  "test",
			featureType:  FeatureType("invalid"),
			columnNumber: 0,
			expectError:  true,
			errorField:   "type",
		},
		{
			name:         "negative column",
			featureName:  "test",
			featureType:  NumericFeature,
			columnNumber: -1,
			expectError:  true,
			errorField:   "column_number",
		},
		{
			name:         "valid feature",
			featureName:  "test",
			featureType:  NumericFeature,
			columnNumber: 0,
			expectError:  false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			_, err := NewFeature(test.featureName, test.featureType, test.columnNumber)

			if test.expectError {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}

				if modelErr, ok := err.(*ModelError); ok {
					if modelErr.Field != test.errorField {
						t.Errorf("Expected error field '%s', got '%s'", test.errorField, modelErr.Field)
					}
				} else {
					t.Errorf("Expected ModelError, got %T", err)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			}
		})
	}
}

func TestFeatureRangeValidation(t *testing.T) {
	feature, err := NewFeature("test", NumericFeature, 0)
	if err != nil {
		t.Fatalf("Failed to create feature: %v", err)
	}

	// Test invalid ranges
	tests := []struct {
		name        string
		min, max    float64
		expectError bool
	}{
		{"valid range", 0, 100, false},
		{"min greater than max", 100, 0, true},
		{"min equals max", 50, 50, true},
		{"negative range", -100, -50, false},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := feature.SetRange(test.min, test.max)

			if test.expectError {
				if err == nil {
					t.Error("Expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			}
		})
	}
}

func TestTreeNode(t *testing.T) {
	// Test decision node for numeric feature
	ageFeature, err := NewFeature("age", NumericFeature, 0)
	if err != nil {
		t.Fatalf("Failed to create age feature: %v", err)
	}

	numNode, err := NewDecisionNode(ageFeature, 30.0)
	if err != nil {
		t.Fatalf("Failed to create decision node: %v", err)
	}

	if numNode.Type != DecisionNode || numNode.Feature != ageFeature || numNode.Threshold != 30.0 {
		t.Errorf("Numeric decision node not initialized correctly")
	}

	if numNode.IsLeaf() {
		t.Errorf("Decision node incorrectly identified as leaf")
	}

	// Test categorical decision node
	colorFeature, err := NewFeature("color", CategoricalFeature, 1)
	if err != nil {
		t.Fatalf("Failed to create color feature: %v", err)
	}

	catNode, err := NewCategoricalDecisionNode(colorFeature)
	if err != nil {
		t.Fatalf("Failed to create categorical decision node: %v", err)
	}

	if catNode.Type != DecisionNode || catNode.Feature != colorFeature {
		t.Errorf("Categorical decision node not initialized correctly")
	}

	// Test leaf node
	distribution := map[interface{}]int{"red": 5, "blue": 3}
	leafNode, err := NewLeafNode("red", distribution, 8)
	if err != nil {
		t.Fatalf("Failed to create leaf node: %v", err)
	}

	if leafNode.Type != LeafNode || leafNode.Prediction != "red" || leafNode.Samples != 8 {
		t.Errorf("Leaf node not initialized correctly")
	}

	if !leafNode.IsLeaf() {
		t.Errorf("Leaf node not identified as leaf")
	}

	// Test majority class
	majority := leafNode.GetMajorityClass()
	if majority != "red" {
		t.Errorf("Incorrect majority class, expected 'red', got %v", majority)
	}

	// Test purity calculation
	purity := leafNode.GetPurity()
	if purity != 0.625 { // 5/8 = 0.625
		t.Errorf("Incorrect purity, expected 0.625, got %v", purity)
	}

	// Test confidence (should be same as purity for leaf nodes)
	if leafNode.Confidence != 0.625 {
		t.Errorf("Incorrect confidence, expected 0.625, got %v", leafNode.Confidence)
	}

	// Test empty distribution
	emptyNode, err := NewLeafNode("unknown", map[interface{}]int{}, 0)
	if err != nil {
		t.Fatalf("Failed to create empty leaf node: %v", err)
	}

	if emptyNode.GetMajorityClass() != nil {
		t.Errorf("Empty node should have nil majority class")
	}

	if emptyNode.GetPurity() != 0.0 {
		t.Errorf("Empty node should have zero purity")
	}
}

func TestTreeNodeValidation(t *testing.T) {
	// Test invalid decision node creation
	tests := []struct {
		name        string
		setup       func() (*TreeNode, error)
		expectError bool
		errorField  string
	}{
		{
			name: "nil feature",
			setup: func() (*TreeNode, error) {
				return NewDecisionNode(nil, 30.0)
			},
			expectError: true,
			errorField:  "feature",
		},
		{
			name: "categorical feature with threshold",
			setup: func() (*TreeNode, error) {
				feature, _ := NewFeature("color", CategoricalFeature, 0)
				return NewDecisionNode(feature, 30.0)
			},
			expectError: true,
			errorField:  "feature_type",
		},
		{
			name: "nil prediction in leaf",
			setup: func() (*TreeNode, error) {
				return NewLeafNode(nil, map[interface{}]int{"a": 1}, 1)
			},
			expectError: true,
			errorField:  "prediction",
		},
		{
			name: "negative samples",
			setup: func() (*TreeNode, error) {
				return NewLeafNode("test", map[interface{}]int{}, -1)
			},
			expectError: true,
			errorField:  "samples",
		},
		{
			name: "distribution mismatch",
			setup: func() (*TreeNode, error) {
				return NewLeafNode("test", map[interface{}]int{"a": 5}, 3) // 5 != 3
			},
			expectError: true,
			errorField:  "class_distribution",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			_, err := test.setup()

			if test.expectError {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}

				if modelErr, ok := err.(*ModelError); ok {
					if modelErr.Field != test.errorField {
						t.Errorf("Expected error field '%s', got '%s'", test.errorField, modelErr.Field)
					}
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			}
		})
	}
}

func TestDecisionTree(t *testing.T) {
	// Test tree creation
	tree, err := NewDecisionTree(CategoricalFeature, 5, 10)
	if err != nil {
		t.Fatalf("Failed to create decision tree: %v", err)
	}

	if tree.TargetType != CategoricalFeature || tree.Config.MaxDepth != 5 || tree.Config.MinSamples != 10 {
		t.Errorf("Decision tree not initialized correctly")
	}

	if tree.IsGrown() {
		t.Errorf("New tree should not be marked as grown")
	}

	// Test feature addition
	ageFeature, err := tree.AddFeature("age", NumericFeature)
	if err != nil {
		t.Fatalf("Failed to add feature: %v", err)
	}
	if ageFeature.Name != "age" || ageFeature.Type != NumericFeature || ageFeature.ColumnNumber != 0 {
		t.Errorf("Feature not added correctly to tree")
	}

	colorFeature, err := tree.AddFeature("color", CategoricalFeature)
	if err != nil {
		t.Fatalf("Failed to add second feature: %v", err)
	}
	if colorFeature.Name != "color" || colorFeature.Type != CategoricalFeature || colorFeature.ColumnNumber != 1 {
		t.Errorf("Second feature not added correctly to tree")
	}

	// Test feature retrieval
	if retrievedFeature := tree.GetFeatureByName("age"); retrievedFeature != ageFeature {
		t.Errorf("Failed to retrieve feature by name")
	}

	retrievedByIndex, err := tree.GetFeatureByIndex(0)
	if err != nil {
		t.Errorf("Failed to retrieve feature by index: %v", err)
	}
	if retrievedByIndex != ageFeature {
		t.Errorf("Failed to retrieve correct feature by index")
	}

	// Test feature counting
	if count := tree.GetFeatureCount(); count != 2 {
		t.Errorf("Incorrect feature count, expected 2, got %d", count)
	}

	// Test feature existence
	if !tree.HasFeature("age") {
		t.Errorf("Tree should have 'age' feature")
	}

	if tree.HasFeature("nonexistent") {
		t.Errorf("Tree should not have 'nonexistent' feature")
	}

	// Test building a simple tree manually
	rootNode, err := NewDecisionNode(ageFeature, 30.0)
	if err != nil {
		t.Fatalf("Failed to create root node: %v", err)
	}

	youngLeaf, err := NewLeafNode("young", map[interface{}]int{"young": 6, "old": 1}, 7)
	if err != nil {
		t.Fatalf("Failed to create young leaf: %v", err)
	}

	oldLeaf, err := NewLeafNode("old", map[interface{}]int{"young": 2, "old": 5}, 7)
	if err != nil {
		t.Fatalf("Failed to create old leaf: %v", err)
	}

	err = rootNode.SetLeftChild(youngLeaf)
	if err != nil {
		t.Errorf("Failed to set left child: %v", err)
	}

	err = rootNode.SetRightChild(oldLeaf)
	if err != nil {
		t.Errorf("Failed to set right child: %v", err)
	}

	tree.Root = rootNode

	// Test tree is now grown
	if !tree.IsGrown() {
		t.Errorf("Tree with root should be marked as grown")
	}

	// Test tree statistics
	tree.UpdateStatistics()
	if tree.GetDepth() != 2 {
		t.Errorf("Expected tree depth 2, got %d", tree.GetDepth())
	}

	if tree.GetNodeCount() != 3 {
		t.Errorf("Expected 3 nodes, got %d", tree.GetNodeCount())
	}

	if tree.GetLeafCount() != 2 {
		t.Errorf("Expected 2 leaves, got %d", tree.GetLeafCount())
	}
}

func TestDecisionTreeValidation(t *testing.T) {
	// Test invalid tree creation
	tests := []struct {
		name        string
		targetType  FeatureType
		maxDepth    int
		minSamples  int
		expectError bool
		errorField  string
	}{
		{
			name:        "invalid target type",
			targetType:  FeatureType("invalid"),
			maxDepth:    5,
			minSamples:  2,
			expectError: true,
			errorField:  "target_type",
		},
		{
			name:        "zero max depth",
			targetType:  CategoricalFeature,
			maxDepth:    0,
			minSamples:  2,
			expectError: true,
			errorField:  "max_depth",
		},
		{
			name:        "negative max depth",
			targetType:  CategoricalFeature,
			maxDepth:    -1,
			minSamples:  2,
			expectError: true,
			errorField:  "max_depth",
		},
		{
			name:        "zero min samples",
			targetType:  CategoricalFeature,
			maxDepth:    5,
			minSamples:  0,
			expectError: true,
			errorField:  "min_samples",
		},
		{
			name:        "valid tree",
			targetType:  CategoricalFeature,
			maxDepth:    5,
			minSamples:  2,
			expectError: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			_, err := NewDecisionTree(test.targetType, test.maxDepth, test.minSamples)

			if test.expectError {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}

				if modelErr, ok := err.(*ModelError); ok {
					if modelErr.Field != test.errorField {
						t.Errorf("Expected error field '%s', got '%s'", test.errorField, modelErr.Field)
					}
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			}
		})
	}
}

func TestDecisionTreeWithOptions(t *testing.T) {
	// Test tree creation with options
	tree, err := NewDecisionTreeWithOptions(
		WithMaxDepth(8),
		WithMinSamples(5),
		WithTargetType(NumericFeature),
		WithCriterion(MSECriterion),
		WithMaxFeatures(10),
		WithRandomState(42),
	)
	if err != nil {
		t.Fatalf("Failed to create tree with options: %v", err)
	}

	if tree.Config.MaxDepth != 8 {
		t.Errorf("Expected max depth 8, got %d", tree.Config.MaxDepth)
	}

	if tree.Config.MinSamples != 5 {
		t.Errorf("Expected min samples 5, got %d", tree.Config.MinSamples)
	}

	if tree.TargetType != NumericFeature {
		t.Errorf("Expected numeric target type, got %s", tree.TargetType)
	}

	if tree.Config.Criterion != MSECriterion {
		t.Errorf("Expected MSE criterion, got %s", tree.Config.Criterion)
	}

	// Test invalid options
	_, err = NewDecisionTreeWithOptions(
		WithMaxDepth(0), // Invalid
	)
	if err == nil {
		t.Error("Expected error for invalid max depth option")
	}
}

func TestFeatureImportance(t *testing.T) {
	tree, err := NewDecisionTree(CategoricalFeature, 5, 2)
	if err != nil {
		t.Fatalf("Failed to create tree: %v", err)
	}

	// Add features
	ageFeature, err := tree.AddFeature("age", NumericFeature)
	if err != nil {
		t.Fatalf("Failed to add age feature: %v", err)
	}

	colorFeature, err := tree.AddFeature("color", CategoricalFeature)
	if err != nil {
		t.Fatalf("Failed to add color feature: %v", err)
	}

	// Verify features were added
	if tree.GetFeatureCount() != 2 {
		t.Fatalf("Expected 2 features in tree, got %d", tree.GetFeatureCount())
	}

	// Build a simple tree
	rootNode, err := NewDecisionNode(ageFeature, 30.0)
	if err != nil {
		t.Fatalf("Failed to create root node: %v", err)
	}

	rootNode.Samples = 100
	rootNode.Impurity = 0.5
	rootNode.ClassDistribution = map[interface{}]int{"A": 50, "B": 50}

	leftLeaf, err := NewLeafNode("A", map[interface{}]int{"A": 40, "B": 10}, 50)
	if err != nil {
		t.Fatalf("Failed to create left leaf: %v", err)
	}

	rightLeaf, err := NewLeafNode("B", map[interface{}]int{"A": 10, "B": 40}, 50)
	if err != nil {
		t.Fatalf("Failed to create right leaf: %v", err)
	}

	rootNode.Left = leftLeaf
	rootNode.Right = rightLeaf
	tree.Root = rootNode

	// Calculate feature importance
	importance := tree.CalculateFeatureImportance()

	// Debug: print what we got
	t.Logf("Feature importance results: %d features", len(importance))
	for name, imp := range importance {
		t.Logf("  %s: importance=%.6f, usage=%d", name, imp.Importance, imp.UsageCount)
	}

	// Verify we have importance for both features
	if len(importance) != 2 {
		t.Errorf("Expected importance for 2 features, got %d", len(importance))
		// List the features we do have
		var names []string
		for name := range importance {
			names = append(names, name)
		}
		t.Errorf("Features with importance: %v", names)

		// List the features in the tree
		treeFeatures := tree.GetFeatureNames()
		t.Errorf("Features in tree: %v", treeFeatures)
	}

	// Test age feature (should be used)
	ageImportance, exists := importance["age"]
	if !exists {
		t.Error("Expected importance for 'age' feature")
	} else {
		if ageImportance.UsageCount != 1 {
			t.Errorf("Expected usage count 1 for age, got %d", ageImportance.UsageCount)
		}
		if ageImportance.Importance <= 0 {
			t.Errorf("Expected positive importance for age, got %f", ageImportance.Importance)
		}
	}

	// Test color feature (should not be used)
	colorImportance, exists := importance["color"]
	if !exists {
		t.Error("Expected importance for 'color' feature")
	} else {
		if colorImportance.UsageCount != 0 {
			t.Errorf("Expected usage count 0 for color, got %d", colorImportance.UsageCount)
		}
		if colorImportance.Importance != 0 {
			t.Errorf("Expected zero importance for unused color, got %f", colorImportance.Importance)
		}
	}

	// Verify that the color feature reference is correct
	if colorImportance != nil && colorImportance.Feature != colorFeature {
		t.Error("Color importance should reference the correct feature")
	}
}

func TestModelError(t *testing.T) {
	// Test error message formatting
	err := &ModelError{
		Op:     "test_operation",
		Field:  "test_field",
		Value:  "test_value",
		Reason: "test reason",
	}

	expected := "model test_operation error for field 'test_field': test reason"
	if err.Error() != expected {
		t.Errorf("Expected error message '%s', got '%s'", expected, err.Error())
	}

	// Test error without field
	err2 := &ModelError{
		Op:     "test_operation",
		Reason: "test reason",
	}

	expected2 := "model test_operation error: test reason"
	if err2.Error() != expected2 {
		t.Errorf("Expected error message '%s', got '%s'", expected2, err2.Error())
	}
}

func TestTreeClone(t *testing.T) {
	// Create original tree
	original, err := NewDecisionTree(CategoricalFeature, 5, 2)
	if err != nil {
		t.Fatalf("Failed to create original tree: %v", err)
	}

	// Add features
	_, err = original.AddFeature("age", NumericFeature)
	if err != nil {
		t.Fatalf("Failed to add feature: %v", err)
	}

	colorFeature, err := original.AddFeature("color", CategoricalFeature)
	if err != nil {
		t.Fatalf("Failed to add color feature: %v", err)
	}

	// Add categorical values
	colorFeature.AddCategoricalValue("red")
	colorFeature.AddCategoricalValue("blue")

	// Clone the tree
	cloned, err := original.Clone()
	if err != nil {
		t.Fatalf("Failed to clone tree: %v", err)
	}

	// Verify clone has same structure
	if cloned.GetFeatureCount() != original.GetFeatureCount() {
		t.Errorf("Cloned tree should have same feature count")
	}

	// Verify features are separate objects
	originalColor := original.GetFeatureByName("color")
	clonedColor := cloned.GetFeatureByName("color")

	if originalColor == clonedColor {
		t.Error("Cloned features should be separate objects")
	}

	// Verify feature properties are copied
	if len(clonedColor.CategoricalValues) != len(originalColor.CategoricalValues) {
		t.Error("Cloned feature should have same categorical values")
	}

	// Verify modifying clone doesn't affect original
	cloned.AddFeature("new_feature", NumericFeature)
	if original.GetFeatureCount() == cloned.GetFeatureCount() {
		t.Error("Adding feature to clone should not affect original")
	}
}

func TestAddCategoricalValue(t *testing.T) {
	feature, err := NewFeature("color", CategoricalFeature, 0)
	if err != nil {
		t.Fatalf("Failed to create feature: %v", err)
	}

	// Test adding valid values
	added, err := feature.AddCategoricalValue("red")
	if err != nil {
		t.Errorf("Failed to add categorical value: %v", err)
	}
	if !added {
		t.Error("Should have added new categorical value")
	}

	// Test adding duplicate
	added, err = feature.AddCategoricalValue("red")
	if err != nil {
		t.Errorf("Should not error on duplicate: %v", err)
	}
	if added {
		t.Error("Should not add duplicate value")
	}

	// Test adding empty value
	_, err = feature.AddCategoricalValue("")
	if err == nil {
		t.Error("Should error on empty categorical value")
	}

	// Test adding whitespace value
	_, err = feature.AddCategoricalValue("   ")
	if err == nil {
		t.Error("Should error on whitespace-only categorical value")
	}

	// Test on non-categorical feature
	numFeature, _ := NewFeature("age", NumericFeature, 0)
	_, err = numFeature.AddCategoricalValue("test")
	if err == nil {
		t.Error("Should error when adding categorical value to numeric feature")
	}
}

func TestFeatureHelperMethods(t *testing.T) {
	// Test categorical feature helpers
	catFeature, _ := NewFeature("color", CategoricalFeature, 0)
	catFeature.AddCategoricalValue("red")
	catFeature.AddCategoricalValue("blue")

	values := catFeature.GetCategoricalValues()
	if len(values) != 2 {
		t.Errorf("Expected 2 categorical values, got %d", len(values))
	}

	if !catFeature.HasCategoricalValue("red") {
		t.Error("Should have categorical value 'red'")
	}

	if catFeature.HasCategoricalValue("green") {
		t.Error("Should not have categorical value 'green'")
	}

	if catFeature.GetValueCount() != 2 {
		t.Errorf("Expected value count 2, got %d", catFeature.GetValueCount())
	}

	// Test numeric feature range checking
	numFeature, _ := NewFeature("age", NumericFeature, 0)
	numFeature.SetRange(0, 100)

	inRange, err := numFeature.IsInRange(50)
	if err != nil {
		t.Errorf("Failed to check range: %v", err)
	}
	if !inRange {
		t.Error("50 should be in range [0, 100]")
	}

	inRange, err = numFeature.IsInRange(150)
	if err != nil {
		t.Errorf("Failed to check range: %v", err)
	}
	if inRange {
		t.Error("150 should not be in range [0, 100]")
	}

	// Test range checking on non-numeric feature
	_, err = catFeature.IsInRange(50)
	if err == nil {
		t.Error("Should error when checking range on categorical feature")
	}
}

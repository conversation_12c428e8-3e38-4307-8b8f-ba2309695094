# Logger Package

This package provides a high-performance structured logging system for Mulberri using <PERSON>ber's Zap logger. It features environment-specific configurations, automatic log rotation, caller information, and metadata support for decision tree operations. It includes both instance-based and global package-level logging functions with YAML-based configuration.

## Key Features

- **Environment-aware logging**: Different formats for development and production
- **High performance**: Built on Uber's Zap logger for minimal overhead
- **Automatic rotation**: Uses lumberjack for log file rotation and compression
- **Caller information**: Automatically includes file, function, and line number
- **Structured logging**: JSON format in production, console format in development
- **Backward compatibility**: Maintains the same API as the original logger

## Features

- **YAML Configuration**: Runtime configuration without recompilation
- **Structured Logging**: Consistent log format with timestamps, severity levels, and source location
- **Log Rotation**: Automatic rotation based on file size (default 10MB) or daily
- **Console Output**: Optional mirroring of logs to stdout/stderr with color support
- **Thread Safety**: Concurrent-safe logging operations
- **Global Logger**: Package-level functions for easy access
- **Fallback Defaults**: Graceful degradation when configuration files are missing

## Configuration

### YAML Configuration File

The logger uses a YAML configuration file located at `config/logger.yaml`:

```yaml
# Logger Configuration
logger:
  log_folder: "logs"          # Directory for log files
  max_size: 10               # Max file size in MB before rotation
  environment: "development"  # Environment: "development" or "production"
  enable_console: true       # Output to stdout/stderr
  app_name: "mulberri"       # Prefix for log filenames
  enable_colors: true        # Use colored console output (development only)
```

## Environment Modes

### Development Mode (`environment: "development"`)
- **Console format**: Human-readable output with colors
- **Verbose logging**: Includes DEBUG level messages
- **Caller information**: Shows file:function:line for debugging
- **Color coding**: Different colors for different log levels

### Production Mode (`environment: "production"`)
- **JSON format**: Structured, machine-readable logs
- **Optimized performance**: Minimal overhead for high-throughput applications
- **Caller information**: Included for debugging production issues
- **Log aggregation friendly**: Easy to parse by log management systems

### Fallback Constants

If the YAML file is missing or invalid, the logger uses these built-in defaults:

```go
const (
    DefaultLogFolder     = "logs"
    DefaultMaxSize       = 10
    DefaultEnableConsole = true
    DefaultAppName       = "mulberri"
    DefaultEnableColors  = true
    DefaultConfigPath    = "config/logger.yaml"
)
```

## Usage

### Global Logger (Recommended)

The simplest way to use the logger is through the global package functions. The global logger automatically loads configuration from `config/logger.yaml` if it exists, or falls back to built-in defaults:

```go
import "github.com/berrijam/mulberri/pkg/logger"

// Use global logger - automatically loads from config/logger.yaml or uses defaults
logger.Info("Application started")
logger.Debug("Processing file: data.csv")
logger.Warn("Feature 'income' has missing values")
logger.Error("Failed to process request")
logger.Fatal( "Critical error occurred") // Calls os.Exit(1). Used for errors that terminate execution
```

> **Note:**  
> The previous `logger.Log(level, ...)` method is deprecated. Use the specific log level methods (`Info`, `Debug`, `Warn`, `Error`, `Fatal`) for clarity and type safety.

**Automatic Configuration Loading:**
- First tries to load `config/logger.yaml`
- If file doesn't exist or is invalid, uses built-in defaults
- No explicit configuration needed for basic usage

### YAML-Based Configuration

```go
import "github.com/berrijam/mulberri/pkg/logger"

// Configure global logger from YAML file
err := logger.SetGlobalConfigFromYAML("config/logger.yaml")
if err != nil {
    panic(err)
}
defer logger.CloseGlobal()

// Use configured global logger
logger.Info("Application configured")
```

## Log Levels

- **Debug**: Detailed troubleshooting information
- **Info**: General operational information
- **Warn**: Potential issues that don't prevent operation
- **Error**: Runtime errors that don't require immediate action
- **Fatal**: Critical errors that require termination (calls `os.Exit(1)`)

## Log Format

Logs are written in a structured format with the following components:

### File Output Format
```
2025-06-21 19:07:21.703| INFO|main.go:main:42 | Application started
2025-06-21 19:07:21.704| DEBUG|processor.go:ProcessFile:15 | Processing file: data.csv
2025-06-21 19:07:21.705| WARN|validator.go:ValidateData:28 | Feature 'income' has missing values
2025-06-21 19:07:21.706| ERROR|handler.go:HandleRequest:67 | Failed to process request
2025-06-21 19:07:21.707| FATAL|main.go:main:50 | Critical error occurred
```

### Console Output Format (with colors)
When `enable_colors: true`, console output includes color coding:
- **Timestamp**: Cyan
- **DEBUG**: Magenta
- **INFO**: Green
- **WARN**: Yellow
- **ERROR/FATAL**: Red

### Format Components
```
[TIMESTAMP]| [LEVEL]|[FILENAME]:[FUNCTION]:[LINE] | [MESSAGE]
```

Where:
- **TIMESTAMP**: `YYYY-MM-DD HH:MM:SS.mmm` format
- **LEVEL**: Log severity (DEBUG, INFO, WARN, ERROR, FATAL)
- **FILENAME**: Source file name (e.g., `main.go`)
- **FUNCTION**: Function name (e.g., `ProcessFile`)
- **LINE**: Line number in source code
- **MESSAGE**: The actual log message

## Log Rotation

- **Size-based**: Rotates when file exceeds MaxSize (default 10MB)
- **Time-based**: Rotates daily at midnight
- **File naming**: `mulberri-{YYYY-MM-DD}.log`

## Thread Safety

All logger operations are thread-safe and can be used concurrently from multiple goroutines.

## Integration with Mulberri

This logger is designed to be used throughout the Mulberri codebase. Use the global logger functions for simplicity, or create specific logger instances for different
package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"
	"time"
)

// TestLevel_String tests the String method for all log levels
func TestLevel_String(t *testing.T) {
	tests := []struct {
		level    Level
		expected string
	}{
		{LevelDebug, "DEBUG  "},
		{LevelInfo, "INFO   "},
		{LevelWarning, "WARN   "},
		{LevelError, "ERROR  "},
		{LevelFatal, "FATAL  "},
		{Level(99), "UNKNOWN"}, // Invalid level
	}

	for _, test := range tests {
		result := test.level.String()
		if result != test.expected {
			t.Errorf("Level(%d).String() = %s, expected %s", test.level, result, test.expected)
		}
	}
}

// TestParseLevel tests the ParseLevel function
func TestParseLevel(t *testing.T) {
	tests := []struct {
		input    string
		expected Level
	}{
		{"DEBUG", LevelDebug},
		{"debug", LevelDebug},
		{"  DEBUG  ", LevelDebug},
		{"INFO", LevelInfo},
		{"info", LevelInfo},
		{"WARN", LevelWarning},
		{"WARNING", LevelWarning},
		{"warn", LevelWarning},
		{"warning", LevelWarning},
		{"ERROR", LevelError},
		{"error", LevelError},
		{"FATAL", LevelFatal},
		{"fatal", LevelFatal},
		{"INVALID", LevelInfo}, // Default to Info
		{"", LevelInfo},        // Empty string defaults to Info
	}

	for _, test := range tests {
		result := ParseLevel(test.input)
		if result != test.expected {
			t.Errorf("ParseLevel(%q) = %v, expected %v", test.input, result, test.expected)
		}
	}
}

// TestDefaultConfig tests the default configuration
func TestDefaultConfig(t *testing.T) {
	config := getDefaultConfig()

	if config.LogFolder != "logs" {
		t.Errorf("DefaultConfig().LogFolder = %s, expected 'logs'", config.LogFolder)
	}
	if config.MaxSize != 10 {
		t.Errorf("DefaultConfig().MaxSize = %d, expected 10", config.MaxSize)
	}

	if !config.EnableConsole {
		t.Error("DefaultConfig().EnableConsole should be true")
	}
	if config.AppName != "mulberri" {
		t.Errorf("DefaultConfig().AppName = %s, expected 'mulberri'", config.AppName)
	}
	if !config.EnableColors {
		t.Error("DefaultConfig().EnableColors should be true")
	}
}

// TestNewWithConfig tests logger creation with various configurations
func TestNewWithConfig(t *testing.T) {
	// Clean up test directory
	testDir := "test_logs"
	defer os.RemoveAll(testDir)

	t.Run("ValidConfig", func(t *testing.T) {
		config := Config{
			LogFolder:     testDir,
			MaxSize:       50,
			EnableConsole: true,
			AppName:       "test",
			EnableColors:  false,
			Environment:   Development,
			MaxBackups:    7,
			MaxAge:        5,
		}

		logger, err := NewWithConfig(config)
		if err != nil {
			t.Fatalf("NewWithConfig() failed: %v", err)
		}
		defer logger.Close()

		if logger.config.LogFolder != testDir {
			t.Errorf("Logger.config.LogFolder = %s, expected %s", logger.config.LogFolder, testDir)
		}
	})

	t.Run("DefaultConfig", func(t *testing.T) {
		config := getDefaultConfig()

		logger, err := NewWithConfig(config)
		if err != nil {
			t.Fatalf("NewWithConfig() with default config failed: %v", err)
		}
		defer logger.Close()
		defer os.RemoveAll("logs") // Clean up default logs dir

		if logger.config.LogFolder != "logs" {
			t.Errorf("Default config should have LogFolder 'logs', got %s", logger.config.LogFolder)
		}
		if logger.config.MaxSize != 10 {
			t.Errorf("Default config should have MaxSize 10, got %d", logger.config.MaxSize)
		}
		if logger.config.AppName != "mulberri" {
			t.Errorf("Default config should have AppName 'mulberri', got %s", logger.config.AppName)
		}
	})

	t.Run("InvalidDirectory", func(t *testing.T) {
		config := Config{
			LogFolder:  "/root/invalid_permission_dir", // Should fail on most systems
			MaxBackups: 7,
			MaxAge:     5,
		}

		_, err := NewWithConfig(config)
		if err == nil {
			t.Error("NewWithConfig() should fail with invalid directory permissions")
		}
	})
}

// TestLogger_ConvenienceMethods tests Debug, Info, Warn, Error methods
func TestLogger_ConvenienceMethods(t *testing.T) {
	testDir := "test_logs_convenience"
	defer os.RemoveAll(testDir)

	config := Config{
		LogFolder:     testDir,
		EnableConsole: false,
		AppName:       "test",
		EnableColors:  false,
		Environment:   Development,
		MaxBackups:    7,
		MaxAge:        5,
	}

	logger, err := NewWithConfig(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}
	defer logger.Close()

	// Test convenience methods
	logger.Debug("Debug method test")
	logger.Info("Info method test")
	logger.Warn("Warn method test")
	logger.Error("Error method test")

	// Read and verify content
	files, err := os.ReadDir(testDir)
	if err != nil {
		t.Fatalf("Failed to read test directory: %v", err)
	}

	content, err := os.ReadFile(filepath.Join(testDir, files[0].Name()))
	if err != nil {
		t.Fatalf("Failed to read log file: %v", err)
	}

	contentStr := string(content)
	expectedMessages := []string{
		"Debug method test",
		"Info method test",
		"Warn method test",
		"Error method test",
	}

	for _, msg := range expectedMessages {
		if !strings.Contains(contentStr, msg) {
			t.Errorf("Log file should contain '%s'", msg)
		}
	}
}

// TestLogger_Rotation tests log file rotation
func TestLogger_Rotation(t *testing.T) {
	testDir := "test_logs_rotation"
	defer os.RemoveAll(testDir)

	t.Run("LoggingBehavior", func(t *testing.T) {
		behaviorDir := testDir + "_behavior"
		defer os.RemoveAll(behaviorDir)

		config := Config{
			LogFolder:     behaviorDir,
			MaxSize:       100,
			EnableConsole: false,
			AppName:       "behavior_test",
			Environment:   Development,
			MaxBackups:    7,
			MaxAge:        5,
		}

		logger, err := NewWithConfig(config)
		if err != nil {
			t.Fatalf("Failed to create logger: %v", err)
		}
		defer logger.Close()

		// Write test messages
		logger.Info("Test message 1")
		logger.Debug("Debug message")
		logger.Warn("Warning message")
		logger.Error("Error message")

		// Give time for writes to complete
		time.Sleep(100 * time.Millisecond)

		// Check that log file was created
		files, err := os.ReadDir(behaviorDir)
		if err != nil {
			t.Fatalf("Failed to read test directory: %v", err)
		}

		if len(files) == 0 {
			t.Fatal("No log files were created")
		}

		// Read log content and verify messages
		logFile := filepath.Join(behaviorDir, files[0].Name())
		content, err := os.ReadFile(logFile)
		if err != nil {
			t.Fatalf("Failed to read log file: %v", err)
		}

		logContent := string(content)
		expectedMessages := []string{"Test message 1", "Debug message", "Warning message", "Error message"}
		for _, msg := range expectedMessages {
			if !strings.Contains(logContent, msg) {
				t.Errorf("Log file does not contain expected message: %s", msg)
			}
		}
	})

	t.Run("SizeBasedRotation", func(t *testing.T) {
		sizeDir := testDir + "_size"
		defer os.RemoveAll(sizeDir)

		config := Config{
			LogFolder:     sizeDir,
			MaxSize:       1, // 1KB for easy testing
			EnableConsole: false,
			AppName:       "size_test",
			Environment:   Development,
			MaxBackups:    7,
			MaxAge:        5,
		}

		logger, err := NewWithConfig(config)
		if err != nil {
			t.Fatalf("Failed to create logger: %v", err)
		}
		defer logger.Close()

		// Write enough data to potentially trigger rotation
		largeMessage := strings.Repeat("This is a large test message to fill up the log file and trigger size-based rotation. ", 20)
		for i := range 50 {
			logger.Info(fmt.Sprintf("%s Message number: %d", largeMessage, i))
		}

		// Give time for writes and potential rotation
		time.Sleep(200 * time.Millisecond)

		// Verify that logging still works after potential rotation
		logger.Info("Final test message after high volume logging")

		// Give time for final write
		time.Sleep(100 * time.Millisecond)

		// Check that log files were created
		files, err := os.ReadDir(sizeDir)
		if err != nil {
			t.Fatalf("Failed to read test directory: %v", err)
		}

		if len(files) == 0 {
			t.Fatal("No log files were created")
		}

		// Verify final message exists in logs
		foundFinalMessage := false
		for _, file := range files {
			if strings.HasSuffix(file.Name(), ".log") {
				logFile := filepath.Join(sizeDir, file.Name())
				content, err := os.ReadFile(logFile)
				if err != nil {
					continue
				}
				if strings.Contains(string(content), "Final test message") {
					foundFinalMessage = true
					break
				}
			}
		}

		if !foundFinalMessage {
			t.Error("Final test message should be found in log files")
		}
	})

	t.Run("EnvironmentModes", func(t *testing.T) {
		envDir := testDir + "_env"
		defer os.RemoveAll(envDir)

		// Test Development Mode
		t.Run("DevelopmentMode", func(t *testing.T) {
			devDir := envDir + "_dev"
			defer os.RemoveAll(devDir)

			config := Config{
				LogFolder:     devDir,
				MaxSize:       100,
				EnableConsole: false,
				AppName:       "dev_test",
				Environment:   Development,
				MaxBackups:    7,
				MaxAge:        5,
			}

			logger, err := NewWithConfig(config)
			if err != nil {
				t.Fatalf("Failed to create logger: %v", err)
			}
			defer logger.Close()

			logger.Info("Development mode test")
			logger.Debug("Debug message in development")

			// Give time for writes
			time.Sleep(100 * time.Millisecond)

			// Check log file content
			files, err := os.ReadDir(devDir)
			if err != nil {
				t.Fatalf("Failed to read test directory: %v", err)
			}

			if len(files) == 0 {
				t.Fatal("No log files were created")
			}

			logFile := filepath.Join(devDir, files[0].Name())
			content, err := os.ReadFile(logFile)
			if err != nil {
				t.Fatalf("Failed to read log file: %v", err)
			}

			logContent := string(content)
			if !strings.Contains(logContent, "Development mode test") {
				t.Error("Log should contain development mode test message")
			}
			if !strings.Contains(logContent, "Debug message in development") {
				t.Error("Development mode should include debug messages")
			}
		})

		// Test Production Mode
		t.Run("ProductionMode", func(t *testing.T) {
			prodDir := envDir + "_prod"
			defer os.RemoveAll(prodDir)

			config := Config{
				LogFolder:     prodDir,
				MaxSize:       100,
				EnableConsole: false,
				AppName:       "prod_test",
				Environment:   Production,
				MaxBackups:    7,
				MaxAge:        5,
			}

			logger, err := NewWithConfig(config)
			if err != nil {
				t.Fatalf("Failed to create logger: %v", err)
			}
			defer logger.Close()

			logger.Info("Production mode test")
			logger.Error("Error message in production")

			// Give time for writes
			time.Sleep(100 * time.Millisecond)

			// Check log file content
			files, err := os.ReadDir(prodDir)
			if err != nil {
				t.Fatalf("Failed to read test directory: %v", err)
			}

			if len(files) == 0 {
				t.Fatal("No log files were created")
			}

			logFile := filepath.Join(prodDir, files[0].Name())
			content, err := os.ReadFile(logFile)
			if err != nil {
				t.Fatalf("Failed to read log file: %v", err)
			}

			logContent := string(content)
			if !strings.Contains(logContent, "Production mode test") {
				t.Error("Log should contain production mode test message")
			}
			// Production logs should use the same custom format as console (not JSON)
			if !strings.Contains(logContent, "|") {
				t.Errorf("Production logs should use the custom format with pipe separators. Got: %s", logContent)
			}
		})
	})
}

// TestLogger_ColorOutput tests colored output
func TestLogger_ColorOutput(t *testing.T) {
	testDir := "test_logs_color"
	defer os.RemoveAll(testDir)

	config := Config{
		LogFolder:     testDir,
		EnableConsole: false,
		AppName:       "test",
		EnableColors:  true, // Enable colors
		Environment:   Development,
		MaxBackups:    7,
		MaxAge:        5,
	}

	logger, err := NewWithConfig(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}
	defer logger.Close()

	logger.Debug("Debug with colors")
	logger.Info("Info with colors")
	logger.Warn("Warning with colors")

	// Test without colors
	logger.config.EnableColors = false
	logger.Info("Info without colors")

	files, err := os.ReadDir(testDir)
	if err != nil {
		t.Fatalf("Failed to read test directory: %v", err)
	}

	content, err := os.ReadFile(filepath.Join(testDir, files[0].Name()))
	if err != nil {
		t.Fatalf("Failed to read log file: %v", err)
	}

	contentStr := string(content)

	// Check that both colored and non-colored messages are present
	if !strings.Contains(contentStr, "Info without colors") {
		t.Error("Should contain non-colored message")
	}
}

// TestGlobalFunctions tests the package-level global functions
func TestGlobalFunctions(t *testing.T) {
	// Reset global state
	globalLogger = nil
	globalOnce = sync.Once{}
	defer CloseGlobal()
	defer os.RemoveAll("logs") // Clean up default logs directory

	// Test global convenience functions - they will initialize with defaults
	Info("Global info message")
	Debug("Global debug message")
	Warn("Global warning message")
	Error("Global error message")
	Info("Message after config change")

	// Give time for writes to complete
	time.Sleep(100 * time.Millisecond)

	// Verify messages were written to default logs directory
	files, err := os.ReadDir("logs")
	if err != nil {
		t.Fatalf("Failed to read default logs directory: %v", err)
	}

	if len(files) == 0 {
		t.Fatal("No log files created in default logs directory")
	}

	// Check filename contains the default app name
	filename := files[0].Name()
	if !strings.Contains(filename, "mulberri") {
		t.Errorf("Filename should contain 'mulberri', got %s", filename)
	}

	content, err := os.ReadFile(filepath.Join("logs", filename))
	if err != nil {
		t.Fatalf("Failed to read global log file: %v", err)
	}

	contentStr := string(content)

	// Debug: print the actual content to understand what's in the file
	t.Logf("Log file content:\n%s", contentStr)

	// Verify all global functions were logged
	expectedMessages := []string{
		"Global info message",
		"Global debug message",
		"Global warning message",
		"Global error message",
		"Message after config change",
	}

	for _, msg := range expectedMessages {
		if !strings.Contains(contentStr, msg) {
			t.Errorf("Global log should contain '%s'", msg)
		}
	}
}

// TestGlobalConvenienceFunctions specifically tests each global function for coverage
func TestGlobalConvenienceFunctions(t *testing.T) {
	// Reset global state
	globalLogger = nil
	globalOnce = sync.Once{}
	defer CloseGlobal()
	defer os.RemoveAll("logs")

	// Test each global convenience function individually to ensure coverage
	t.Run("GlobalDebug", func(t *testing.T) {
		Debug("Test global Debug function")
	})

	t.Run("GlobalInfo", func(t *testing.T) {
		Info("Test global Info function")
	})

	t.Run("GlobalWarn", func(t *testing.T) {
		Warn("Test global Warn function")
	})

	t.Run("GlobalError", func(t *testing.T) {
		Error("Test global Error function")
	})

	// Give time for writes to complete
	time.Sleep(100 * time.Millisecond)

	// Verify all messages were written to default logs directory
	files, err := os.ReadDir("logs")
	if err != nil {
		t.Fatalf("Failed to read default logs directory: %v", err)
	}

	if len(files) == 0 {
		t.Fatal("No log files created")
	}

	content, err := os.ReadFile(filepath.Join("logs", files[0].Name()))
	if err != nil {
		t.Fatalf("Failed to read log file: %v", err)
	}

	contentStr := string(content)
	expectedMessages := []string{
		"Test global Debug function",
		"Test global Info function",
		"Test global Warn function",
		"Test global Error function",
	}

	for _, msg := range expectedMessages {
		if !strings.Contains(contentStr, msg) {
			t.Errorf("Log file should contain '%s'", msg)
		}
	}
}

// TestInitGlobalLogger tests the global logger initialization
func TestInitGlobalLogger(t *testing.T) {
	t.Run("DefaultInit", func(t *testing.T) {
		// Reset global state
		globalLogger = nil
		globalOnce = sync.Once{}
		defer CloseGlobal()
		defer os.RemoveAll("logs")

		// Test the main initGlobalLogger function (uses DefaultConfig)
		initGlobalLogger()

		if globalLogger == nil {
			t.Error("Global logger should be initialized")
		}

		// Verify it uses default config
		if globalLogger.config.AppName != "mulberri" {
			t.Errorf("Should use default app name 'mulberri', got %s", globalLogger.config.AppName)
		}

		if globalLogger.config.LogFolder != "logs" {
			t.Errorf("Should use default log dir 'logs', got %s", globalLogger.config.LogFolder)
		}
	})

	t.Run("SuccessfulInit", func(t *testing.T) {
		// Reset global state
		globalLogger = nil
		globalOnce = sync.Once{}
		defer CloseGlobal()

		// Test successful initialization with custom config
		testConfig := Config{
			LogFolder:     "test_init_logs",
			AppName:       "init_test",
			EnableConsole: false,
		}
		defer os.RemoveAll("test_init_logs")

		// This should succeed
		initGlobalLoggerWithConfig(testConfig)

		if globalLogger == nil {
			t.Error("Global logger should be initialized")
		}

		// Verify custom config was applied
		if globalLogger.config.AppName != "init_test" {
			t.Errorf("Should use custom app name 'init_test', got %s", globalLogger.config.AppName)
		}
	})

	t.Run("FailedInit", func(t *testing.T) {
		if os.Getenv("TEST_INIT_FAIL") == "1" {
			// This runs in the subprocess
			// Reset global state
			globalLogger = nil
			globalOnce = sync.Once{}

			// Try to initialize with invalid config that should fail
			invalidConfig := Config{
				LogFolder: "/root/definitely_invalid_permission_dir_that_should_fail",
			}

			// This should call os.Exit(1) due to permission error
			initGlobalLoggerWithConfig(invalidConfig)
			return
		}

		// Main test - spawn subprocess to test the error path
		cmd := os.Args[0]
		args := []string{cmd, "-test.run=TestInitGlobalLogger/FailedInit"}
		env := append(os.Environ(), "TEST_INIT_FAIL=1")

		proc, err := os.StartProcess(cmd, args, &os.ProcAttr{
			Env:   env,
			Files: []*os.File{os.Stdin, os.Stdout, os.Stderr},
		})
		if err != nil {
			t.Fatalf("Failed to start subprocess: %v", err)
		}

		state, err := proc.Wait()
		if err != nil {
			t.Fatalf("Subprocess error: %v", err)
		}

		// Should exit with code 1 due to initialization failure
		if !state.Exited() || state.ExitCode() != 1 {
			t.Errorf("Failed init should exit with code 1, got %v", state.ExitCode())
		}
	})
}

// TestGetGlobalLogger tests the global logger getter
func TestGetGlobalLogger(t *testing.T) {
	// Reset global state
	globalLogger = nil
	globalOnce = sync.Once{}
	defer CloseGlobal()
	defer os.RemoveAll("logs")

	// First call should initialize
	logger1 := getGlobalLogger()
	if logger1 == nil {
		t.Error("getGlobalLogger should return initialized logger")
	}

	// Second call should return same instance
	logger2 := getGlobalLogger()
	if logger1 != logger2 {
		t.Error("getGlobalLogger should return same instance on subsequent calls")
	}

	// Test that it works
	logger1.Info("Test message from global logger")
}

// TestLogger_ConcurrentAccess tests thread safety
func TestLogger_ConcurrentAccess(t *testing.T) {
	testDir := "test_logs_concurrent"
	defer os.RemoveAll(testDir)

	config := Config{
		LogFolder:     testDir,
		EnableConsole: false,
		AppName:       "concurrent",
	}

	logger, err := NewWithConfig(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}
	defer logger.Close()

	// Test concurrent writes
	const numGoroutines = 10
	const messagesPerGoroutine = 10

	var wg sync.WaitGroup
	wg.Add(numGoroutines)

	for i := range numGoroutines {
		go func(id int) {
			defer wg.Done()
			for j := range messagesPerGoroutine {
				logger.Info(fmt.Sprintf("Concurrent message from goroutine %d, message %d", id, j))
			}
		}(i)
	}

	wg.Wait()

	// Verify all messages were written
	files, err := os.ReadDir(testDir)
	if err != nil {
		t.Fatalf("Failed to read test directory: %v", err)
	}

	content, err := os.ReadFile(filepath.Join(testDir, files[0].Name()))
	if err != nil {
		t.Fatalf("Failed to read log file: %v", err)
	}

	// Count the number of log entries
	lines := strings.Split(string(content), "\n")
	nonEmptyLines := 0
	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			nonEmptyLines++
		}
	}

	expectedMessages := numGoroutines * messagesPerGoroutine
	if nonEmptyLines != expectedMessages {
		t.Errorf("Expected %d log messages, got %d", expectedMessages, nonEmptyLines)
	}
}

// TestLogger_Fatal tests the Fatal method using subprocess to handle os.Exit
func TestLogger_Fatal(t *testing.T) {
	if os.Getenv("TEST_FATAL") == "1" {
		// This runs in the subprocess
		testDir := "test_fatal_logs"
		config := Config{
			LogFolder:     testDir,
			EnableConsole: false,
			AppName:       "fatal_test",
		}

		logger, err := NewWithConfig(config)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Failed to create logger: %v", err)
			os.Exit(2)
		}

		// This should call os.Exit(1)
		logger.Fatal("Fatal test message")
		return
	}

	// Main test - spawn subprocess
	testDir := "test_fatal_logs"
	defer os.RemoveAll(testDir)

	cmd := os.Args[0]
	args := []string{cmd, "-test.run=TestLogger_Fatal"}
	env := append(os.Environ(), "TEST_FATAL=1")

	proc, err := os.StartProcess(cmd, args, &os.ProcAttr{
		Env:   env,
		Files: []*os.File{os.Stdin, os.Stdout, os.Stderr},
	})
	if err != nil {
		t.Fatalf("Failed to start subprocess: %v", err)
	}

	state, err := proc.Wait()
	if err != nil {
		t.Fatalf("Subprocess error: %v", err)
	}

	// Fatal should exit with code 1
	if !state.Exited() || state.ExitCode() != 1 {
		t.Errorf("Fatal should exit with code 1, got %v", state.ExitCode())
	}

	// Check that log was written before exit
	files, err := os.ReadDir(testDir)
	if err == nil && len(files) > 0 {
		content, err := os.ReadFile(filepath.Join(testDir, files[0].Name()))
		if err == nil {
			contentStr := string(content)
			if !strings.Contains(contentStr, "Fatal test message") {
				t.Error("Fatal log should contain the fatal message")
			}
		}
	}
}

// TestGlobalFatal tests the global Fatal function
func TestGlobalFatal(t *testing.T) {
	if os.Getenv("TEST_GLOBAL_FATAL") == "1" {
		// This runs in the subprocess
		defer CloseGlobal()

		// This should call os.Exit(1)
		Fatal("Global fatal test message")
		return
	}

	// Main test - spawn subprocess
	defer os.RemoveAll("logs")

	cmd := os.Args[0]
	args := []string{cmd, "-test.run=TestGlobalFatal"}
	env := append(os.Environ(), "TEST_GLOBAL_FATAL=1")

	proc, err := os.StartProcess(cmd, args, &os.ProcAttr{
		Env:   env,
		Files: []*os.File{os.Stdin, os.Stdout, os.Stderr},
	})
	if err != nil {
		t.Fatalf("Failed to start subprocess: %v", err)
	}

	state, err := proc.Wait()
	if err != nil {
		t.Fatalf("Subprocess error: %v", err)
	}

	// Fatal should exit with code 1
	if !state.Exited() || state.ExitCode() != 1 {
		t.Errorf("Global fatal should exit with code 1, got %v", state.ExitCode())
	}
}

// BenchmarkLogger_Info benchmarks the Info logging method
func BenchmarkLogger_Info(b *testing.B) {
	testDir := "bench_logs"
	defer os.RemoveAll(testDir)

	config := Config{
		LogFolder:     testDir,
		EnableConsole: false,
		AppName:       "bench",
	}

	logger, err := NewWithConfig(config)
	if err != nil {
		b.Fatalf("Failed to create logger: %v", err)
	}
	defer logger.Close()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		logger.Info(fmt.Sprintf("Benchmark message %d", i))
	}
}

// BenchmarkGlobalInfo benchmarks the global Info function
func BenchmarkGlobalInfo(b *testing.B) {
	defer CloseGlobal()
	defer os.RemoveAll("logs")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Info(fmt.Sprintf("Global benchmark message %d", i))
	}
}

// TestYAMLConfig tests YAML configuration loading
func TestYAMLConfig(t *testing.T) {
	// Test with non-existent file
	t.Run("NonExistentFile", func(t *testing.T) {
		config, err := LoadConfigFromYAML("nonexistent.yaml")
		if err != nil {
			t.Errorf("LoadConfigFromYAML should not fail with non-existent file, got: %v", err)
		}
		// Should return default config
		if config.LogFolder != DefaultLogFolder {
			t.Errorf("Expected default LogFolder %s, got %s", DefaultLogFolder, config.LogFolder)
		}
	})

	// Test NewFromYAML with default config
	t.Run("NewFromYAMLDefault", func(t *testing.T) {
		defer os.RemoveAll("logs")

		logger, err := NewFromYAML("nonexistent.yaml")
		if err != nil {
			t.Errorf("NewFromYAML should not fail with non-existent file, got: %v", err)
		}
		defer logger.Close()

		if logger.config.AppName != DefaultAppName {
			t.Errorf("Expected default AppName %s, got %s", DefaultAppName, logger.config.AppName)
		}
	})

	// Test with valid YAML file
	t.Run("ValidYAMLFile", func(t *testing.T) {
		// Create a temporary YAML config file
		yamlContent := `
logger:
  log_folder: "test_yaml_logs"
  max_size: 20
  min_level: "debug"
  enable_console: false
  app_name: "yaml_test"
  enable_colors: false
`
		tmpFile, err := os.CreateTemp("", "test_config*.yaml")
		if err != nil {
			t.Fatalf("Failed to create temp file: %v", err)
		}
		defer os.Remove(tmpFile.Name())
		defer os.RemoveAll("test_yaml_logs")

		_, err = tmpFile.WriteString(yamlContent)
		if err != nil {
			t.Fatalf("Failed to write to temp file: %v", err)
		}
		tmpFile.Close()

		// Test LoadConfigFromYAML with valid file
		config, err := LoadConfigFromYAML(tmpFile.Name())
		if err != nil {
			t.Fatalf("LoadConfigFromYAML should succeed with valid file: %v", err)
		}

		// Verify all config values were loaded correctly
		if config.LogFolder != "test_yaml_logs" {
			t.Errorf("Expected LogFolder 'test_yaml_logs', got %s", config.LogFolder)
		}
		if config.MaxSize != 20 {
			t.Errorf("Expected MaxSize 20, got %d", config.MaxSize)
		}

		if config.EnableConsole != false {
			t.Errorf("Expected EnableConsole false, got %v", config.EnableConsole)
		}
		if config.AppName != "yaml_test" {
			t.Errorf("Expected AppName 'yaml_test', got %s", config.AppName)
		}
		if config.EnableColors != false {
			t.Errorf("Expected EnableColors false, got %v", config.EnableColors)
		}

		// Test NewFromYAML with valid file
		logger, err := NewFromYAML(tmpFile.Name())
		if err != nil {
			t.Fatalf("NewFromYAML should succeed with valid file: %v", err)
		}
		defer logger.Close()

		if logger.config.AppName != "yaml_test" {
			t.Errorf("Expected AppName 'yaml_test', got %s", logger.config.AppName)
		}
	})

	// Test with partial YAML file (test default value functions)
	t.Run("PartialYAMLFile", func(t *testing.T) {
		// Create YAML with only some fields to test default value functions
		yamlContent := `
logger:
  log_folder: ""
  max_size: 0
  min_level: ""
  app_name: ""
`
		tmpFile, err := os.CreateTemp("", "test_partial_config*.yaml")
		if err != nil {
			t.Fatalf("Failed to create temp file: %v", err)
		}
		defer os.Remove(tmpFile.Name())

		_, err = tmpFile.WriteString(yamlContent)
		if err != nil {
			t.Fatalf("Failed to write to temp file: %v", err)
		}
		tmpFile.Close()

		config, err := LoadConfigFromYAML(tmpFile.Name())
		if err != nil {
			t.Fatalf("LoadConfigFromYAML should succeed with partial file: %v", err)
		}

		// Should use defaults for empty/zero values
		if config.LogFolder != DefaultLogFolder {
			t.Errorf("Expected default LogFolder %s, got %s", DefaultLogFolder, config.LogFolder)
		}
		if config.MaxSize != DefaultMaxSize {
			t.Errorf("Expected default MaxSize %d, got %d", DefaultMaxSize, config.MaxSize)
		}
		if config.AppName != DefaultAppName {
			t.Errorf("Expected default AppName %s, got %s", DefaultAppName, config.AppName)
		}
	})

	// Test with YAML file containing bool pointer values
	t.Run("YAMLWithBoolPointers", func(t *testing.T) {
		// Create YAML that will test getBoolOrDefault with actual values
		yamlContent := `
logger:
  log_folder: "test_bool_logs"
  max_size: 15
  min_level: "warn"
  enable_console: true
  app_name: "bool_test"
  enable_colors: true
`
		tmpFile, err := os.CreateTemp("", "test_bool_config*.yaml")
		if err != nil {
			t.Fatalf("Failed to create temp file: %v", err)
		}
		defer os.Remove(tmpFile.Name())
		defer os.RemoveAll("test_bool_logs")

		_, err = tmpFile.WriteString(yamlContent)
		if err != nil {
			t.Fatalf("Failed to write to temp file: %v", err)
		}
		tmpFile.Close()

		config, err := LoadConfigFromYAML(tmpFile.Name())
		if err != nil {
			t.Fatalf("LoadConfigFromYAML should succeed with bool file: %v", err)
		}

		// Should use actual values from YAML
		if config.LogFolder != "test_bool_logs" {
			t.Errorf("Expected LogFolder 'test_bool_logs', got %s", config.LogFolder)
		}
		if config.MaxSize != 15 {
			t.Errorf("Expected MaxSize 15, got %d", config.MaxSize)
		}

		if !config.EnableConsole {
			t.Error("Expected EnableConsole true")
		}
		if config.AppName != "bool_test" {
			t.Errorf("Expected AppName 'bool_test', got %s", config.AppName)
		}
		if !config.EnableColors {
			t.Error("Expected EnableColors true")
		}
	})

	// Test with invalid YAML file
	t.Run("InvalidYAMLFile", func(t *testing.T) {
		// Create invalid YAML content
		yamlContent := `
logger:
  log_folder: "test"
  invalid_yaml: [unclosed bracket
`
		tmpFile, err := os.CreateTemp("", "test_invalid_config*.yaml")
		if err != nil {
			t.Fatalf("Failed to create temp file: %v", err)
		}
		defer os.Remove(tmpFile.Name())

		_, err = tmpFile.WriteString(yamlContent)
		if err != nil {
			t.Fatalf("Failed to write to temp file: %v", err)
		}
		tmpFile.Close()

		// Should return default config and not error (based on LoadConfigFromYAML behavior)
		config, err := LoadConfigFromYAML(tmpFile.Name())
		if err != nil {
			t.Errorf("LoadConfigFromYAML should not error with invalid YAML, got: %v", err)
		}

		// Should return default config
		if config.LogFolder != DefaultLogFolder {
			t.Errorf("Expected default LogFolder %s, got %s", DefaultLogFolder, config.LogFolder)
		}
	})

	// Test with unreadable file (permission error)
	t.Run("UnreadableFile", func(t *testing.T) {
		tmpFile, err := os.CreateTemp("", "test_unreadable*.yaml")
		if err != nil {
			t.Fatalf("Failed to create temp file: %v", err)
		}
		tmpFile.Close()

		// Make file unreadable
		err = os.Chmod(tmpFile.Name(), 0000)
		if err != nil {
			t.Fatalf("Failed to change file permissions: %v", err)
		}
		defer os.Remove(tmpFile.Name())

		// Should return default config and not error
		config, err := LoadConfigFromYAML(tmpFile.Name())
		if err != nil {
			t.Errorf("LoadConfigFromYAML should not error with unreadable file, got: %v", err)
		}

		// Should return default config
		if config.LogFolder != DefaultLogFolder {
			t.Errorf("Expected default LogFolder %s, got %s", DefaultLogFolder, config.LogFolder)
		}
	})
}

// TestGlobalYAMLConfig tests global YAML configuration functions
func TestGlobalYAMLConfig(t *testing.T) {
	defer CloseGlobal()
	defer os.RemoveAll("logs")

	t.Run("SetGlobalConfigFromYAML", func(t *testing.T) {
		// Reset global logger but KEEP globalOnce
		if globalLogger != nil {
			globalLogger.Close()
		}
		globalLogger = nil

		err := SetGlobalConfigFromYAML("nonexistent.yaml")
		if err != nil {
			t.Errorf("SetGlobalConfigFromYAML should not fail with non-existent file, got: %v", err)
		}

		// Test that it works
		Info("Test YAML config")
	})

	t.Run("SetGlobalConfigFromYAMLWithValidFile", func(t *testing.T) {
		// Reset global logger but KEEP globalOnce
		if globalLogger != nil {
			globalLogger.Close()
		}
		globalLogger = nil

		// Create a valid YAML file
		yamlContent := `
logger:
  log_folder: "test_global_yaml_logs"
  app_name: "global_yaml_test"
  enable_console: false
`
		tmpFile, err := os.CreateTemp("", "test_global_config*.yaml")
		if err != nil {
			t.Fatalf("Failed to create temp file: %v", err)
		}
		defer os.Remove(tmpFile.Name())
		defer os.RemoveAll("test_global_yaml_logs")

		_, err = tmpFile.WriteString(yamlContent)
		if err != nil {
			t.Fatalf("Failed to write to temp file: %v", err)
		}
		tmpFile.Close()

		err = SetGlobalConfigFromYAML(tmpFile.Name())
		if err != nil {
			t.Fatalf("SetGlobalConfigFromYAML should succeed with valid file: %v", err)
		}

		// Test that config was applied
		Info("Test global YAML config application")

		// Verify the config was applied
		files, err := os.ReadDir("test_global_yaml_logs")
		if err != nil {
			t.Fatalf("Should create log directory: %v", err)
		}

		if len(files) == 0 {
			t.Fatal("Should create log files")
		}

		// Check filename contains the app name from YAML
		filename := files[0].Name()
		if !strings.Contains(filename, "global_yaml_test") {
			t.Errorf("Filename should contain 'global_yaml_test', got %s", filename)
		}
	})

	t.Run("SetGlobalConfigFromYAMLError", func(t *testing.T) {
		// Reset global logger but KEEP globalOnce
		if globalLogger != nil {
			globalLogger.Close()
		}
		globalLogger = nil

		// Create YAML file with invalid log folder
		yamlContent := `
logger:
  log_folder: "/root/invalid_permission_dir_for_yaml_test"
  app_name: "yaml_error_test"
`
		tmpFile, err := os.CreateTemp("", "test_error_config*.yaml")
		if err != nil {
			t.Fatalf("Failed to create temp file: %v", err)
		}
		defer os.Remove(tmpFile.Name())

		_, err = tmpFile.WriteString(yamlContent)
		if err != nil {
			t.Fatalf("Failed to write to temp file: %v", err)
		}
		tmpFile.Close()

		err = SetGlobalConfigFromYAML(tmpFile.Name())
		if err == nil {
			t.Error("SetGlobalConfigFromYAML should fail with invalid log folder")
		}
	})
}

func TestZapLogger_BasicFunctionality(t *testing.T) {
	testDir := "test_logs_zap"
	defer os.RemoveAll(testDir)

	config := Config{
		LogFolder:     testDir,
		MaxSize:       1, // Small size to test rotation
		EnableConsole: false,
		AppName:       "test",
		EnableColors:  false,
		Environment:   Development,
	}

	logger, err := NewWithConfig(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}
	defer logger.Close()

	// Test all log levels
	logger.Debug("Debug message")
	logger.Info("Info message")
	logger.Warn("Warning message")
	logger.Error("Error message")

	// Give time for writes to complete
	time.Sleep(100 * time.Millisecond)

	// Check that log file was created
	files, err := os.ReadDir(testDir)
	if err != nil {
		t.Fatalf("Failed to read test directory: %v", err)
	}

	if len(files) == 0 {
		t.Fatal("No log files were created")
	}

	// Check that log file contains our messages
	logFile := filepath.Join(testDir, files[0].Name())
	content, err := os.ReadFile(logFile)
	if err != nil {
		t.Fatalf("Failed to read log file: %v", err)
	}

	logContent := string(content)
	expectedMessages := []string{"Debug message", "Info message", "Warning message", "Error message"}
	for _, msg := range expectedMessages {
		if !contains(logContent, msg) {
			t.Errorf("Log file does not contain expected message: %s", msg)
		}
	}
}

// TestZapLogger_ProductionMode tests production environment configuration
func TestZapLogger_ProductionMode(t *testing.T) {
	testDir := "test_logs_prod"
	defer os.RemoveAll(testDir)

	config := Config{
		LogFolder:     testDir,
		MaxSize:       10,
		EnableConsole: false,
		AppName:       "prod_test",
		EnableColors:  false,
		Environment:   Production,
	}

	logger, err := NewWithConfig(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}
	defer logger.Close()

	logger.Info("Production log message")
	logger.Error("Production error message")

	// Give time for writes to complete
	time.Sleep(100 * time.Millisecond)

	// Check that log file was created
	files, err := os.ReadDir(testDir)
	if err != nil {
		t.Fatalf("Failed to read test directory: %v", err)
	}

	if len(files) == 0 {
		t.Fatal("No log files were created")
	}

	// In production mode, logs should be in JSON format
	logFile := filepath.Join(testDir, files[0].Name())
	content, err := os.ReadFile(logFile)
	if err != nil {
		t.Fatalf("Failed to read log file: %v", err)
	}

	logContent := string(content)
	// Check for custom format structure indicators (pipe separators and caller info)
	if !contains(logContent, "|") {
		t.Errorf("Production logs should use the custom format with pipe separators. Got: %s", logContent)
	}
}

// TestZapLogger_GlobalFunctions tests the global logger functions
func TestZapLogger_GlobalFunctions(t *testing.T) {
	testDir := "test_logs_global"
	defer os.RemoveAll(testDir)

	config := Config{
		LogFolder:     testDir,
		MaxSize:       10,
		EnableConsole: false,
		AppName:       "global_test",
		EnableColors:  false,
		Environment:   Development,
	}

	err := SetGlobalConfig(config)
	if err != nil {
		t.Fatalf("Failed to set global config: %v", err)
	}
	defer CloseGlobal()

	// Test global functions
	Debug("Global debug message")
	Info("Global info message")
	Warn("Global warning message")
	Error("Global error message")

	// Give time for writes to complete
	time.Sleep(100 * time.Millisecond)

	// Check that log file was created
	files, err := os.ReadDir(testDir)
	if err != nil {
		t.Fatalf("Failed to read test directory: %v", err)
	}

	if len(files) == 0 {
		t.Fatal("No log files were created")
	}

	// Check that log file contains our messages
	logFile := filepath.Join(testDir, files[0].Name())
	content, err := os.ReadFile(logFile)
	if err != nil {
		t.Fatalf("Failed to read log file: %v", err)
	}

	logContent := string(content)
	expectedMessages := []string{"Global debug message", "Global info message", "Global warning message", "Global error message"}
	for _, msg := range expectedMessages {
		if !contains(logContent, msg) {
			t.Errorf("Log file does not contain expected message: %s", msg)
		}
	}
}

// TestZapLogger_YAMLConfig tests loading configuration from YAML
func TestZapLogger_YAMLConfig(t *testing.T) {
	// Test with the default config file
	config, err := LoadConfigFromYAML("../../config/logger.yaml")
	if err != nil {
		t.Fatalf("Failed to load YAML config: %v", err)
	}

	if config.LogFolder != "logs" {
		t.Errorf("Expected log folder 'logs', got '%s'", config.LogFolder)
	}

	if config.AppName != "mulberri" {
		t.Errorf("Expected app name 'mulberri', got '%s'", config.AppName)
	}

	if config.Environment != Development {
		t.Errorf("Expected environment 'development', got '%s'", config.Environment)
	}
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			containsAt(s, substr))))
}

func containsAt(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

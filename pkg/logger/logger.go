// Package logger provides a structured logging system for Mulberri with
// rotation, error tracking, and metadata support for decision tree operations.
// It includes both instance-based and global package-level logging functions.
// Uses zap for high-performance logging with environment-specific configurations.
package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/buffer"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"gopkg.in/yaml.v3"
)

// Default configuration constants for the logger.
const (
	DefaultLogFolder     = "logs"
	DefaultMaxSize       = 10
	DefaultEnableConsole = true
	DefaultAppName       = "mulberri"
	DefaultEnableColors  = true
	DefaultConfigPath    = "config/logger.yaml"
)

// Level represents the severity level of a log message (for backward compatibility).
type Level int

// Log levels in increasing order of severity (for backward compatibility).
const (
	LevelDebug Level = iota
	LevelInfo
	LevelWarning
	LevelError
	LevelFatal
)

// String returns the string representation of a log Level (for backward compatibility).
func (l Level) String() string {
	switch l {
	case LevelDebug:
		return "DEBUG  "
	case LevelInfo:
		return "INFO   "
	case LevelWarning:
		return "WARN   "
	case LevelError:
		return "ERROR  "
	case LevelFatal:
		return "FATAL  "
	default:
		return "UNKNOWN"
	}
}

// ParseLevel parses a string and returns the corresponding Level (for backward compatibility).
func ParseLevel(levelStr string) Level {
	switch strings.ToUpper(strings.TrimSpace(levelStr)) {
	case "DEBUG":
		return LevelDebug
	case "INFO":
		return LevelInfo
	case "WARN", "WARNING":
		return LevelWarning
	case "ERROR":
		return LevelError
	case "FATAL":
		return LevelFatal
	default:
		return LevelInfo
	}
}

// Environment represents the application environment
type Environment string

const (
	Development Environment = "development"
	Production  Environment = "production"
)

// Config holds logger configuration options.
type Config struct {
	LogFolder     string
	MaxSize       int64
	EnableConsole bool
	AppName       string
	EnableColors  bool
	Environment   Environment
	MaxBackups    int
	MaxAge        int
}

// yamlConfig is used for unmarshalling YAML configuration files.
type yamlConfig struct {
	Logger struct {
		LogFolder     string `yaml:"log_folder"`
		MaxSize       int64  `yaml:"max_size"`
		EnableConsole *bool  `yaml:"enable_console"`
		AppName       string `yaml:"app_name"`
		EnableColors  *bool  `yaml:"enable_colors"`
		Environment   string `yaml:"environment"`
		MaxBackups    int    `yaml:"max_backups"`
		MaxAge        int    `yaml:"max_age"`
	} `yaml:"logger"`
}

// Logger wraps zap.Logger to provide the same API as the original logger
type Logger struct {
	config    Config
	zapLogger *zap.Logger
	sugar     *zap.SugaredLogger
}

// Global logger instance and sync.Once for initialization.
var globalLogger *Logger
var globalOnce sync.Once
var globalMu sync.RWMutex

// getDefaultConfig returns a Config struct with default values.
func getDefaultConfig() Config {
	return Config{
		LogFolder:     DefaultLogFolder,
		MaxSize:       DefaultMaxSize,
		EnableConsole: DefaultEnableConsole,
		AppName:       DefaultAppName,
		EnableColors:  DefaultEnableColors,
		Environment:   Development,
		MaxBackups:    7,
		MaxAge:        5,
	}
}

// LoadConfigFromYAML loads logger configuration from a YAML file.
func LoadConfigFromYAML(configPath string) (Config, error) {
	if configPath == "" {
		configPath = DefaultConfigPath
	}
	config, err := loadConfigFromYAMLFile(configPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Warning: Failed to load logger config from %s: %v. Using defaults.\n", configPath, err)
		return getDefaultConfig(), nil
	}
	return config, nil
}

// loadConfigFromYAMLFile reads and parses a YAML config file into Config.
func loadConfigFromYAMLFile(configPath string) (Config, error) {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return Config{}, fmt.Errorf("config file does not exist: %s", configPath)
	}
	data, err := os.ReadFile(configPath)
	if err != nil {
		return Config{}, fmt.Errorf("failed to read config file: %w", err)
	}
	var yamlCfg yamlConfig
	err = yaml.Unmarshal(data, &yamlCfg)
	if err != nil {
		return Config{}, fmt.Errorf("failed to parse YAML config: %w", err)
	}

	env := Development
	if yamlCfg.Logger.Environment == "production" {
		env = Production
	}

	return Config{
		LogFolder:     getStringOrDefault(yamlCfg.Logger.LogFolder, DefaultLogFolder),
		MaxSize:       getInt64OrDefault(yamlCfg.Logger.MaxSize, DefaultMaxSize),
		EnableConsole: getBoolOrDefault(yamlCfg.Logger.EnableConsole, DefaultEnableConsole),
		AppName:       getStringOrDefault(yamlCfg.Logger.AppName, DefaultAppName),
		EnableColors:  getBoolOrDefault(yamlCfg.Logger.EnableColors, DefaultEnableColors),
		Environment:   env,
		MaxBackups:    getIntOrDefault(yamlCfg.Logger.MaxBackups, 7),
		MaxAge:        getIntOrDefault(yamlCfg.Logger.MaxAge, 5),
	}, nil
}

// Helper functions for config parsing
func getStringOrDefault(value, defaultValue string) string {
	if value == "" {
		return defaultValue
	}
	return value
}

func getIntOrDefault(value, defaultValue int) int {
	if value < 0 {
		return defaultValue
	}
	return value
}
func getInt64OrDefault(value, defaultValue int64) int64 {
	if value <= 0 {
		return defaultValue
	}
	return value
}

func getBoolOrDefault(value *bool, defaultValue bool) bool {
	if value == nil {
		return defaultValue
	}
	return *value
}

// customConsoleEncoder creates a custom encoder that formats logs exactly as specified
type customConsoleEncoder struct {
	zapcore.Encoder
	enableColors bool
}

func newCustomConsoleEncoder() zapcore.Encoder {
	return &customConsoleEncoder{enableColors: true}
}

func newCustomConsoleEncoderNoColors() zapcore.Encoder {
	return &customConsoleEncoder{enableColors: false}
}

// customFileEncoder creates a custom encoder for file output (same format as console but no colors)
type customFileEncoder struct {
	zapcore.Encoder
}

func newCustomFileEncoder() zapcore.Encoder {
	return &customFileEncoder{}
}

func (enc *customFileEncoder) Clone() zapcore.Encoder {
	return &customFileEncoder{}
}

func (enc *customFileEncoder) EncodeEntry(entry zapcore.Entry, fields []zapcore.Field) (*buffer.Buffer, error) {
	buf := buffer.NewPool().Get()

	// Time
	buf.AppendString(entry.Time.Format("2006-01-02 15:04:05"))
	buf.AppendString("| ")

	// Level with proper spacing (file encoder - no colors)
	var levelStr string
	switch entry.Level {
	case zapcore.DebugLevel:
		levelStr = "DEBUG"
	case zapcore.InfoLevel:
		levelStr = "INFO "
	case zapcore.WarnLevel:
		levelStr = "WARN "
	case zapcore.ErrorLevel:
		levelStr = "ERROR"
	case zapcore.FatalLevel:
		levelStr = "FATAL"
	default:
		levelStr = "UNKNOWN"
	}
	buf.AppendString(levelStr)
	buf.AppendString("|")

	// Caller info
	if entry.Caller.Defined {
		// Get the function name from the caller
		funcName := entry.Caller.Function
		if funcName != "" {
			// Extract just the function name (remove package path)
			if idx := strings.LastIndex(funcName, "."); idx >= 0 {
				funcName = funcName[idx+1:]
			}
		} else {
			funcName = "unknown"
		}

		// Get the file name (remove directory path)
		fileName := entry.Caller.File
		if idx := strings.LastIndex(fileName, "/"); idx >= 0 {
			fileName = fileName[idx+1:]
		}

		buf.AppendString(fmt.Sprintf("%s:%s:%d", fileName, funcName, entry.Caller.Line))
	}
	buf.AppendString(" | ")

	// Message
	buf.AppendString(entry.Message)
	buf.AppendString("\n")

	return buf, nil
}

func (enc *customConsoleEncoder) Clone() zapcore.Encoder {
	return &customConsoleEncoder{enableColors: enc.enableColors}
}

func (enc *customConsoleEncoder) EncodeEntry(entry zapcore.Entry, fields []zapcore.Field) (*buffer.Buffer, error) {
	buf := buffer.NewPool().Get()

	// Time
	buf.AppendString("\033[36m")
	buf.AppendString(entry.Time.Format("2006-01-02 15:04:05"))
	buf.AppendString("\033[0m")
	buf.AppendString("| ")

	// Level with proper spacing and optional colors
	var levelStr string
	if enc.enableColors {
		// Add colors for different log levels
		switch entry.Level {
		case zapcore.DebugLevel:
			levelStr = "\033[35mDEBUG\033[0m"
		case zapcore.InfoLevel:
			levelStr = "\033[32mINFO \033[0m"
		case zapcore.WarnLevel:
			levelStr = "\033[33mWARN \033[0m"
		case zapcore.ErrorLevel:
			levelStr = "\033[31mERROR\033[0m"
		case zapcore.FatalLevel:
			levelStr = "\033[31mFATAL\033[0m"
		default:
			levelStr = "UNKNOWN"
		}
	} else {
		// No colors
		switch entry.Level {
		case zapcore.DebugLevel:
			levelStr = "DEBUG"
		case zapcore.InfoLevel:
			levelStr = "INFO "
		case zapcore.WarnLevel:
			levelStr = "WARN "
		case zapcore.ErrorLevel:
			levelStr = "ERROR"
		case zapcore.FatalLevel:
			levelStr = "FATAL"
		default:
			levelStr = "UNKNOWN"
		}
	}
	buf.AppendString(levelStr)
	buf.AppendString("|")

	// Caller info
	if entry.Caller.Defined {
		// Get the function name from the caller
		funcName := entry.Caller.Function
		if funcName != "" {
			// Extract just the function name (remove package path)
			if idx := strings.LastIndex(funcName, "."); idx >= 0 {
				funcName = funcName[idx+1:]
			}
		} else {
			funcName = "unknown"
		}

		// Get the file name (remove directory path)
		fileName := entry.Caller.File
		if idx := strings.LastIndex(fileName, "/"); idx >= 0 {
			fileName = fileName[idx+1:]
		}

		buf.AppendString(fmt.Sprintf("%s:%s:%d", fileName, funcName, entry.Caller.Line))
	}
	buf.AppendString(" | ")

	// Message
	buf.AppendString(entry.Message)
	buf.AppendString("\n")

	return buf, nil
}

// createZapConfig creates a zap configuration based on environment and config
func createZapConfig(config Config) zap.Config {
	var zapConfig zap.Config

	if config.Environment == Production {
		// Production: JSON format, no caller info by default, structured
		zapConfig = zap.NewProductionConfig()
		zapConfig.DisableCaller = false // Enable caller info as requested
		zapConfig.DisableStacktrace = true
	} else {
		// Development: Console format, caller info, more verbose
		zapConfig = zap.NewDevelopmentConfig()
		zapConfig.DisableCaller = false
		zapConfig.DisableStacktrace = false
	}

	// Set encoding based on environment and color preference
	if config.Environment == Development && config.EnableColors {
		zapConfig.Encoding = "console"
		zapConfig.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	} else if config.Environment == Development {
		zapConfig.Encoding = "console"
		zapConfig.EncoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	} else {
		zapConfig.Encoding = "json"
	}

	// Configure time format for file output (JSON)
	zapConfig.EncoderConfig.TimeKey = "timestamp"
	zapConfig.EncoderConfig.EncodeTime = zapcore.TimeEncoderOfLayout("2006-01-02 15:04:05.000")
	zapConfig.EncoderConfig.CallerKey = "caller"
	zapConfig.EncoderConfig.EncodeCaller = zapcore.ShortCallerEncoder

	return zapConfig
}

// createLumberjackWriter creates a lumberjack writer for log rotation
func createLumberjackWriter(config Config) *lumberjack.Logger {
	currentDate := time.Now().Format("2006-01-02")
	logFile := filepath.Join(config.LogFolder, fmt.Sprintf("%s-%s.log", config.AppName, currentDate))
	return &lumberjack.Logger{
		Filename:   logFile,
		MaxSize:    int(config.MaxSize), // MB
		MaxBackups: config.MaxBackups,   // Keep 30 backup files
		MaxAge:     config.MaxAge,       // Keep logs for 30 days
		Compress:   true,                // Compress old log files
	}
}

// NewWithConfig creates a new Logger with the given configuration.
func NewWithConfig(config Config) (*Logger, error) {
	// Create log directory
	if err := os.MkdirAll(config.LogFolder, 0755); err != nil {
		return nil, fmt.Errorf("failed to create log directory: %w", err)
	}

	// Create zap config
	zapConfig := createZapConfig(config)

	// Create cores for different outputs
	var cores []zapcore.Core

	// File output with rotation - use same format as console but without colors
	fileWriter := createLumberjackWriter(config)
	fileEncoder := newCustomFileEncoder() // Custom encoder without colors
	fileCore := zapcore.NewCore(fileEncoder, zapcore.AddSync(fileWriter), zapConfig.Level)
	cores = append(cores, fileCore)

	// Console output (if enabled)
	if config.EnableConsole {
		var consoleEncoder zapcore.Encoder
		if config.EnableColors {
			consoleEncoder = newCustomConsoleEncoder() // With colors
		} else {
			consoleEncoder = newCustomConsoleEncoderNoColors() // Without colors
		}
		consoleCore := zapcore.NewCore(consoleEncoder, zapcore.AddSync(os.Stdout), zapConfig.Level)
		cores = append(cores, consoleCore)
	}

	// Combine cores
	core := zapcore.NewTee(cores...)

	// Create logger with caller info
	zapLogger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(2))

	return &Logger{
		config:    config,
		zapLogger: zapLogger,
		sugar:     zapLogger.Sugar(),
	}, nil
}

// NewFromYAML creates a new Logger from a YAML configuration file.
func NewFromYAML(configPath string) (*Logger, error) {
	config, err := LoadConfigFromYAML(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	return NewWithConfig(config)
}


// Debug logs a debug-level message.
func (l *Logger) Debug(msg string) {
	l.sugar.Debug(msg)
}

// Info logs an info-level message.
func (l *Logger) Info(msg string) {
	l.sugar.Info(msg)
}

// Warn logs a warning-level message.
func (l *Logger) Warn(msg string) {
	l.sugar.Warn(msg)
}

// Error logs an error-level message.
func (l *Logger) Error(msg string) {
	l.sugar.Error(msg)
}

// Fatal logs a fatal-level message and exits.
func (l *Logger) Fatal(msg string) {
	l.sugar.Fatal(msg) // 
}

// Close closes the logger and syncs any buffered log entries.
func (l *Logger) Close() {
	if l.zapLogger != nil {
		l.zapLogger.Sync()
	}
}


// initGlobalLogger initializes the global logger with config from YAML.
func initGlobalLogger() {
	config, _ := LoadConfigFromYAML(DefaultConfigPath)
	initGlobalLoggerWithConfig(config)
}

// initGlobalLoggerWithConfig initializes the global logger with a given config.
func initGlobalLoggerWithConfig(config Config) {
	var err error
	globalLogger, err = NewWithConfig(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize global logger: %v\n", err)
		os.Exit(1)
	}
}

// getGlobalLogger returns the singleton global logger, initializing if needed.
func getGlobalLogger() *Logger {
	globalMu.RLock()
	if globalLogger != nil {
		defer globalMu.RUnlock()
		return globalLogger
	}
	globalMu.RUnlock()

	globalMu.Lock()
	defer globalMu.Unlock()
	if globalLogger == nil {
		globalOnce.Do(initGlobalLogger)
	}
	return globalLogger
}

// Global convenience functions that maintain the original API
func Debug(msg string) { getGlobalLogger().Debug(msg) }
func Info(msg string)  { getGlobalLogger().Info(msg) }
func Warn(msg string)  { getGlobalLogger().Warn(msg) }
func Error(msg string) { getGlobalLogger().Error(msg) }
func Fatal(msg string) { getGlobalLogger().Fatal(msg) }

// SetGlobalConfig sets the global logger configuration.
func SetGlobalConfig(config Config) error {
	globalMu.Lock()
	defer globalMu.Unlock()

	if globalLogger != nil {
		globalLogger.Close()
	}
	var err error
	globalLogger, err = NewWithConfig(config)
	return err
}

// SetGlobalConfigFromYAML sets the global logger config from a YAML file.
func SetGlobalConfigFromYAML(configPath string) error {
	globalMu.Lock()
	defer globalMu.Unlock()

	if globalLogger != nil {
		globalLogger.Close()
	}
	var err error
	globalLogger, err = NewFromYAML(configPath)
	return err
}

// CloseGlobal closes the global logger.
func CloseGlobal() {
	globalMu.Lock()
	defer globalMu.Unlock()
	if globalLogger != nil {
		globalLogger.Close()
		globalLogger = nil
	}
}

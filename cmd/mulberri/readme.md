# Package main

Package main provides the command-line interface and orchestration layer for the Mulberri decision tree application. This package integrates all components (CLI parsing, data loading, feature creation, tree building, and model serialization) into a complete workflow for training decision tree models from CSV data.

## Overview

The main package serves as the entry point for the Mulberri CLI application, providing a clean orchestration layer that delegates business logic to specialized packages while handling user interaction, progress reporting, and model persistence.

### Key Features

- **Clean Orchestration**: Delegates complex operations to specialized packages
- **Progress Reporting**: Real-time feedback during long-running operations  
- **Graceful Shutdown**: Proper signal handling for clean application termination
- **Model Persistence**: JSON serialization for trained decision trees
- **Comprehensive Error Handling**: Structured error reporting with context

### Architecture Principles

- **Single Responsibility**: Main package focuses only on workflow orchestration
- **Trust Packages**: Relies on existing package validation rather than duplicating checks
- **Minimal Business Logic**: Keeps application-specific logic to a minimum
- **Clean Separation**: Clear boundaries between CLI, workflow, and persistence concerns

## Command Line Interface

The application supports the following commands:

```bash
# Train a decision tree model
mulberri -c train -i data.csv -t target_column -o model.json

# Train with custom parameters
mulberri -c train -i data.csv -t target -o model.json \
  --max-depth 10 --min-samples 5 --criterion entropy --verbose

# Show version information
mulberri --version

# Show help
mulberri --help
```


```bash
# Make predictions using a trained model
./mulberri -c predict -m model.json -i data.csv -o predictions.csv

# Verbose mode for detailed output
./mulberri -c predict -m model.json --i data.csv -o predictions.csv --verbose
```

### Supported Parameters

- **Core Flags**:
  - `-c, --command`: Command to execute (train|predict)
  - `-i, --input`: Input CSV file path
  - `-t, --target`: Target column name (training only)
  - `-o, --output`: Output file path
  - `-m, --model`: Model file path (prediction only)

- **Tree Parameters**:
  - `--max-depth`: Maximum tree depth (default: 10)
  - `--min-samples`: Minimum samples to split a node (default: 2)
  - `--min-leaf`: Minimum samples at leaf node (default: 1)
  - `--criterion`: Splitting criterion: gini|entropy|mse (default: entropy)

- **Options**:
  - `--verbose`: Enable detailed progress output
  - `--help`: Show help message
  - `--version`: Show version information

## Package Structure

The main package is split into two focused files:

### main.go
Primary application logic and workflow orchestration:

- **Entry Point**: `main()` function with signal handling
- **Command Routing**: Routes CLI commands to appropriate handlers
- **Training Workflow**: `handleTrainCommand()` orchestrates the training pipeline
- **Dataset Implementation**: `CSVDataset` implements the training dataset interface
- **Progress Reporting**: `ProgressReporter` provides user feedback
- **Utility Functions**: Helper functions for configuration mapping and display

### serialization.go
Model persistence and JSON serialization:

- **Model Serialization**: Converts decision trees to JSON format
- **Model Deserialization**: Loads decision trees from JSON files
- **Type Conversion**: Handles interface{} to concrete type conversions
- **File Operations**: Directory creation and file I/O operations

## Core Types


**Methods**:
- `GetSize() int`: Returns number of samples in current dataset
- `GetFeatureValue(sampleIdx, feature) (interface{}, error)`: Retrieves feature value with type conversion
- `GetTarget(sampleIdx) (string, error)`: Gets target value for sample
- `GetIndices() []int`: Returns current sample indices
- `Subset(indices) Dataset[string]`: Creates subset view without data copying

**Features**:
- **Zero-copy subsetting**: Efficient subset operations using index views
- **Type conversion**: Automatic string-to-numeric conversion for numeric features
- **Error handling**: Comprehensive bounds checking and error reporting

### ProgressReporter

Provides user feedback during long-running operations:

```go
type ProgressReporter struct {
    verbose bool      // Whether to show detailed output
    stage   string    // Current processing stage
    start   time.Time // Start time for elapsed tracking
}
```

**Methods**:
- `StartStage(stage)`: Begin a new processing stage
- `UpdateProgress(message)`: Report progress within current stage
- `CompleteStage()`: Mark current stage as complete

**Features**:
- **Conditional output**: Respects verbose/quiet modes
- **Elapsed timing**: Tracks and reports time spent per stage
- **Stage tracking**: Organized progress reporting by workflow stage

### Serialization Types

JSON-compatible structures for model persistence:

```go
type SerializableDecisionTree struct {
    Root            *SerializableTreeNode      `json:"root"`
    Features        map[string]*models.Feature `json:"features"`
    FeaturesByIndex []*models.Feature          `json:"features_by_index"`
    TargetType      string                     `json:"target_type"`
    Config          models.TreeConfig          `json:"config"`
    NodeCount       int                        `json:"node_count"`
    LeafCount       int                        `json:"leaf_count"`  
    Depth           int                        `json:"depth"`
}

type SerializableTreeNode struct {
    Type              string                           `json:"type"`
    Feature           *models.Feature                  `json:"feature,omitempty"`
    Threshold         float64                          `json:"threshold"`
    Categories        map[string]*SerializableTreeNode `json:"categories,omitempty"`
    Left              *SerializableTreeNode            `json:"left,omitempty"`
    Right             *SerializableTreeNode            `json:"right,omitempty"`
    Prediction        interface{}                      `json:"prediction,omitempty"`
    ClassDistribution map[string]int                   `json:"class_distribution"`
    Samples           int                              `json:"samples"`
    Confidence        float64                          `json:"confidence"`
    Impurity          float64                          `json:"impurity"`
}
```

## Workflow Pipeline

### Training Workflow

The training workflow follows a structured pipeline:

1. **CSV Data Loading**
   - Uses `utils.ReadTrainingCSV()` for comprehensive validation
   - Validates file format, headers, and data consistency
   - Ensures target column completeness

2. **Feature Processing** 
   - Uses `utils.ExtractRecordsAndTarget()` to separate features from targets
   - Uses `training.CreateFeatures()` for automatic type detection
   - Creates properly typed feature objects

3. **Tree Building**
   - Creates `CSVDataset` instance for efficient data access
   - Configures C4.5 splitter with CLI parameters
   - Builds tree using builder package with validation

4. **Model Serialization**
   - Converts tree to JSON-serializable format
   - Creates output directory if needed
   - Saves model with proper error handling

### Error Handling Strategy

- **Package Trust**: Relies on existing package validation rather than duplicating checks
- **Error Propagation**: Preserves detailed error context from underlying packages
- **User-Friendly Messages**: Converts technical errors to actionable user feedback
- **Graceful Degradation**: Continues where possible, fails fast when necessary

## Signal Handling

### Graceful Shutdown

The application implements proper signal handling for production deployment:

```go
func setupGracefulShutdown() (context.Context, context.CancelFunc) {
    ctx, cancel := context.WithCancel(context.Background())
    
    sigChan := make(chan os.Signal, 1)
    signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)
    
    go func() {
        <-sigChan
        fmt.Println("\nShutting down gracefully...")
        cancel()
    }()
    
    return ctx, cancel
}
```

**Supported Signals**:
- `SIGINT` (Ctrl+C): User interrupt
- `SIGTERM`: Termination request from system/containers

**Behavior**:
- Immediate user feedback on signal receipt
- Context cancellation propagated to long-running operations
- Clean resource cleanup and proper exit codes
- Integration with Docker, Kubernetes, and process managers

## Configuration Mapping

The package provides clean mapping between CLI parameters and internal package options:

### Unified Criterion Configuration
```go
// CriterionMapping holds both splitter and builder options for a criterion
type CriterionMapping struct {
    SplitterOption training.SplitterOption
    BuilderOption  builder.BuilderOption
}

// mapCriterion provides unified criterion mapping for both splitter and builder
func mapCriterion(criterion string) CriterionMapping {
    switch criterion {
    case "gini":
        return CriterionMapping{
            SplitterOption: training.WithImpurityCriterion(training.GiniImpurity),
            BuilderOption:  builder.WithCriterion(models.GiniCriterion),
        }
    case "entropy":
        return CriterionMapping{
            SplitterOption: training.WithImpurityCriterion(training.EntropyImpurity),
            BuilderOption:  builder.WithCriterion(models.EntropyCriterion),
        }
    case "mse":
        return CriterionMapping{
            SplitterOption: training.WithImpurityCriterion(training.MSEImpurity),
            BuilderOption:  builder.WithCriterion(models.MSECriterion),
        }
    default:
        return CriterionMapping{
            SplitterOption: training.WithImpurityCriterion(training.EntropyImpurity),
            BuilderOption:  builder.WithCriterion(models.EntropyCriterion),
        }
    }
}
```

### Usage Example
```go
// Get unified criterion mapping for both splitter and builder
criterionMapping := mapCriterion(config.Criterion)

// Use for splitter configuration
splitter, err := training.NewC45Splitter[string](
    training.WithMinSamplesSplit(config.MinSamplesSplit),
    criterionMapping.SplitterOption,
)

// Use for builder configuration
treeBuilder, err := builder.NewTreeBuilderWithInference(splitter,
    builder.WithMaxDepth(config.MaxDepth),
    criterionMapping.BuilderOption,
)
```

## Usage Examples

### Basic Training
```bash
./mulberri -c train -i iris.csv -t species -o iris_model.json
```

### Advanced Training with Custom Parameters
```bash
./mulberri -c train \
  -i large_dataset.csv \
  -t target_column \
  -o model.json \
  --max-depth 15 \
  --min-samples 10 \
  --criterion gini \
  --verbose
```

### Programmatic Usage (for testing)
```go
package main

import (
    "context"
    "testing"
    "github.com/berrijam/mulberri/cmd/mulberri/cli"
)

func TestTrainingWorkflow(t *testing.T) {
    // Create test configuration
    config := &cli.Config{
        Command:         "train",
        InputFile:       "test_data.csv",
        TargetCol:       "target",
        OutputFile:      "test_model.json",
        MaxDepth:        5,
        MinSamplesSplit: 2,
        MinSamplesLeaf:  1,
        Criterion:       "entropy",
        Verbose:         false,
    }
    
    // Execute training workflow
    ctx := context.Background()
    err := handleTrainCommand(ctx, config)
    if err != nil {
        t.Fatalf("Training failed: %v", err)
    }
}
```

## Performance Characteristics

### Memory Usage
- **Efficient Subsetting**: Zero-copy dataset subsetting using index views
- **Streaming Processing**: Processes data in workflow stages without full duplication
- **Garbage Collection Friendly**: Minimal long-lived allocations

### I/O Operations
- **Sequential File Access**: Reads CSV files sequentially for optimal disk performance
- **Buffered Output**: Uses buffered JSON writing for large models
- **Directory Creation**: Creates output directories only when needed

### Concurrency
- **Thread-Safe Signal Handling**: Proper goroutine management for signals
- **Context Propagation**: Passes cancellation context through entire pipeline
- **Resource Cleanup**: Ensures proper cleanup on early termination

## Error Scenarios and Handling

### File System Errors
- **Missing Input Files**: Clear error messages with file path information
- **Permission Issues**: Detailed error reporting for read/write failures
- **Directory Creation**: Automatic directory creation with fallback error handling

### Data Validation Errors
- **Invalid CSV Format**: Leverages utils package validation with clear error context
- **Missing Target Column**: Specific error reporting for configuration issues
- **Inconsistent Data**: Comprehensive validation with row/column specificity

### Resource Constraints
- **Memory Limitations**: Graceful handling of large datasets
- **Disk Space**: Clear error reporting for insufficient storage
- **Processing Timeouts**: Context-based cancellation for long-running operations

## Testing

### Test Coverage
The package includes comprehensive tests covering:

- **Unit Tests**: All public functions and methods
- **Integration Tests**: Complete workflow testing
- **Error Path Testing**: All error conditions and edge cases
- **Signal Handling**: Context cancellation and cleanup

### Running Tests
```bash
# Run all tests
go test -v

# Run with coverage report
go test -cover -coverprofile=coverage.out
go tool cover -html=coverage.out -o coverage.html

# Run integration tests
go test -v -run TestCreateAndBuildTree

# Skip slow tests
go test -short
```

### Benchmarking
```bash
# Run benchmarks
go test -bench=. -benchmem

# Profile CPU usage
go test -cpuprofile=cpu.prof -bench=.
go tool pprof cpu.prof
```

## Dependencies

### Internal Dependencies
- `github.com/berrijam/mulberri/cmd/mulberri/cli`: Command-line parsing and validation
- `github.com/berrijam/mulberri/internals/builder`: Tree construction and validation
- `github.com/berrijam/mulberri/internals/training`: Feature creation and C4.5 splitting
- `github.com/berrijam/mulberri/pkg/models`: Core data structures
- `github.com/berrijam/mulberri/pkg/utils`: Data loading and validation

### External Dependencies
- Standard library only: `context`, `encoding/json`, `os`, `path/filepath`, `syscall`, etc.
- No external dependencies for core functionality

## Build and Deployment

### Building
```bash
# Development build
go build -o mulberri *.go

# Production build with version info
go build -ldflags "-X main.version=1.0.0 -X main.commit=$(git rev-parse HEAD) -X main.buildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)" -o mulberri *.go
```

### Container Deployment
```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o mulberri *.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/mulberri .
CMD ["./mulberri"]
```

### System Integration
- **systemd**: Service files for daemon deployment
- **Docker**: Container-ready with proper signal handling
- **Kubernetes**: Pod-friendly with graceful shutdown
- **CI/CD**: Automated testing and building

## Best Practices

### Error Handling
- Always check errors from underlying packages
- Provide context-specific error messages
- Use structured error types where appropriate
- Log errors at appropriate levels

### Resource Management
- Use defer for cleanup operations
- Implement proper signal handling
- Pass context through long-running operations
- Monitor memory usage in production

### Configuration
- Validate all configuration early
- Use sensible defaults
- Provide clear documentation for parameters
- Support environment variable overrides

### Testing
- Test all error paths
- Use table-driven tests for multiple scenarios
- Include integration tests for workflows
- Benchmark performance-critical paths

## Future Enhancements

### Planned Features
- **Prediction Command**: Model loading and batch prediction
- **Cross-Validation**: Built-in model validation
- **Feature Importance**: Analysis and reporting
- **Model Metrics**: Performance evaluation

### Performance Improvements
- **Streaming JSON**: Large model serialization
- **Parallel Processing**: Multi-core dataset processing
- **Memory Optimization**: Reduced allocations
- **Direct Data Access**: Efficient feature value retrieval

### Usability Enhancements
- **Interactive Mode**: Step-by-step guidance
- **Configuration Files**: YAML config support
- **Output Formats**: Multiple serialization formats
- **Logging**: Structured logging with levels

---

This documentation provides comprehensive coverage of the main package functionality, architecture, and usage patterns. For additional information about specific components, refer to the individual package documentation.
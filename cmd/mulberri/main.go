package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/berrijam/mulberri/cmd/mulberri/cli"
	"github.com/berrijam/mulberri/internals/prediction"
	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/internals/training/builder"
	"github.com/berrijam/mulberri/internals/utils"
	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// Version information - will be set during build
var (
	version   = "dev"
	commit    = "unknown"
	buildTime = "unknown"
)

func main() {
	// Setup graceful shutdown
	shutdownContext, cancelShutdown := setupGracefulShutdown()
	defer cancelShutdown()

	// Parse CLI arguments - CLI package handles all validation
	cliConfig, err := cli.ParseFlags()
	if err != nil {
		handleCLIError(err)
		os.Exit(1)
	}

	logger.Info("Mulberri application starting")

	// Route to appropriate command handler
	switch cliConfig.Command {
	case "train":
		if err := handleTrainCommand(shutdownContext, cliConfig); err != nil {
			handleTrainingError(err)
			os.Exit(1)
		}
	case "predict":
		if err := handlePredictCommand(shutdownContext, cliConfig); err != nil {
			handlePredictionError(err)
			os.Exit(1)
		}
	case "version":
		showVersion()
	default:
		logger.Error(fmt.Sprintf("Unknown command: %s", cliConfig.Command))
		cliConfig.ShowHelp()
		os.Exit(1)
	}
}

// setupGracefulShutdown configures signal handling for clean shutdown
func setupGracefulShutdown() (context.Context, context.CancelFunc) {
	ctx, cancel := context.WithCancel(context.Background())

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-sigChan
		logger.Info("Shutting down gracefully...")
		cancel()
	}()

	return ctx, cancel
}

// handleTrainCommand orchestrates the complete training workflow
func handleTrainCommand(shutdownContext context.Context, cliConfig *cli.Config) error {
	logger.Info(fmt.Sprintf("Starting training workflow with input: %s, target: %s, output: %s", cliConfig.InputFile, cliConfig.TargetCol, cliConfig.OutputFile))

	progress := NewProgressReporter(cliConfig.Verbose)

	// Stage 1: Load CSV data - utils package handles ALL validation
	progress.StartStage("Loading Data")
	csvHeaders, csvData, targetColumnIndex, err := utils.ReadTrainingCSV(cliConfig.InputFile, cliConfig.TargetCol)
	if err != nil {
		logger.Error(fmt.Sprintf("Data loading failed: %v", err))
		return fmt.Errorf("data loading failed: %w", err)
	}
	logger.Debug(fmt.Sprintf("Loaded %d rows with %d columns", len(csvData), len(csvHeaders)))
	progress.CompleteStage()

	// Stage 2: Extract features and targets - utils package handles validation
	progress.StartStage("Processing Features")
	featureRecords, targetValues, featureColumnHeaders, err := utils.ExtractRecordsAndTarget(csvData, csvHeaders, cliConfig.TargetCol, targetColumnIndex)
	if err != nil {
		logger.Error(fmt.Sprintf("Feature extraction failed: %v", err))
		return fmt.Errorf("feature extraction failed: %w", err)
	}

	// Create feature objects - prioritize feature info if available, fallback to automatic detection
	featureObjects, err := createFeatureObjects(featureRecords, featureColumnHeaders, cliConfig)
	if err != nil {
		logger.Error(fmt.Sprintf("Feature creation failed: %v", err))
		return fmt.Errorf("feature creation failed: %w", err)
	}

	logger.Debug(fmt.Sprintf("Created %d features from %d samples", len(featureObjects), len(featureRecords)))

	// Log feature information for debugging
	for _, feature := range featureObjects {
		logger.Info(fmt.Sprintf("Feature %s: type=%s, column=%d", feature.Name, feature.Type, feature.ColumnNumber))
	}

	if cliConfig.Verbose {
		progress.UpdateProgress(fmt.Sprintf("Created %d features", len(featureObjects)))
	}
	progress.CompleteStage()

	// Stage 3: Build decision tree - builder package handles ALL validation
	progress.StartStage("Building Tree")
	dataset := utils.NewCSVDatasetSmart(featureRecords, targetValues, featureObjects)
	logger.Debug(fmt.Sprintf("Created dataset with %d samples", dataset.GetSize()))

	tree, err := createAndBuildTree(shutdownContext, dataset, featureObjects, cliConfig)
	if err != nil {
		logger.Error(fmt.Sprintf("Tree building failed: %v", err))
		return fmt.Errorf("tree building failed: %w", err)
	}
	logger.Info(fmt.Sprintf("Successfully built tree with %d nodes, %d leaves, depth %d", tree.NodeCount, tree.LeafCount, tree.Depth))
	progress.CompleteStage()

	// Stage 4: Save model
	progress.StartStage("Saving Model")
	err = saveModel(tree, cliConfig.OutputFile, progress)
	if err != nil {
		logger.Error(fmt.Sprintf("Model saving failed: %v", err))
		return fmt.Errorf("model saving failed: %w", err)
	}
	progress.CompleteStage()

	// Success message
	showSuccessMessage(cliConfig, tree)
	logger.Info("Training workflow completed successfully")
	return nil
}

func handlePredictCommand(ctx context.Context, config *cli.Config) error {
	logger.Info("Start prediction command")
	dt, err := loadModel(config.ModelFile)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to load model: %v", err))
		return fmt.Errorf("failed to load model: %w", err)
	}

	progress := NewProgressReporter(config.Verbose)

	// Stage 1: Load prediction data from model
	progress.StartStage("Loading data")
	headers, predictionRecords, err := utils.LoadPredictionDataFromModel(config.InputFile, dt)
	logger.Info(fmt.Sprintf("Loaded %d rows with %d columns", len(predictionRecords), len(headers)))
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to load prediction data: %v", err))
		return fmt.Errorf("failed to load prediction data: %w", err)
	}
	progress.CompleteStage()

	// Stage 2: Make predictions using tree traversal
	progress.StartStage("Making Predictions")
	results, err := makePredictions(ctx, dt, predictionRecords, config, progress)
	if err != nil {
		logger.Error(fmt.Sprintf("Prediction failed: %v", err))
		return fmt.Errorf("prediction failed: %w", err)
	}
	progress.CompleteStage()

	// Stage 3: Save prediction results
	progress.StartStage("Saving Results")
	err = savePredictionResults(results, headers, config, progress)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to save results: %v", err))
		return fmt.Errorf("failed to save results: %w", err)
	}
	progress.CompleteStage()

	// Success message
	showPredictionSuccessMessage(config, len(results))
	logger.Info("Prediction workflow completed successfully")
	return nil
}

// createAndBuildTree creates configured splitter and builder, then builds the tree
func createAndBuildTree(ctx context.Context, dataset training.Dataset[string], features []*models.Feature, cliConfig *cli.Config) (*models.DecisionTree, error) {
	logger.Debug(fmt.Sprintf("Creating splitter with criterion: %s, min_samples_split: %d, min_samples_leaf: %d", cliConfig.Criterion, cliConfig.MinSamplesSplit, cliConfig.MinSamplesLeaf))

	// Get unified criterion mapping for both splitter and builder
	criterionMapping := mapCriterion(cliConfig.Criterion)

	// Create configured splitter - training package handles validation
	splitter, err := training.NewC45Splitter[string](
		training.WithMinSamplesSplit(cliConfig.MinSamplesSplit),
		training.WithMinSamplesLeaf(cliConfig.MinSamplesLeaf),
		training.WithLogging(cliConfig.Verbose),
		criterionMapping.SplitterOption,
	)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to create splitter: %v", err))
		return nil, err
	}

	logger.Debug(fmt.Sprintf("Creating tree builder with max_depth: %d", cliConfig.MaxDepth))

	// Create configured tree builder - builder package handles validation
	treeBuilder, err := builder.NewTreeBuilderWithInference(splitter,
		builder.WithMaxDepth(cliConfig.MaxDepth),
		builder.WithMinSamplesSplit(cliConfig.MinSamplesSplit),
		builder.WithMinSamplesLeaf(cliConfig.MinSamplesLeaf),
		builder.WithLogging(cliConfig.Verbose),
		criterionMapping.BuilderOption,
	)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to create tree builder: %v", err))
		return nil, err
	}

	logger.Debug("Starting tree building process")
	// Build tree - builder handles ALL validation
	tree, err := treeBuilder.BuildTree(ctx, dataset, features)
	if err != nil {
		logger.Error(fmt.Sprintf("Tree building process failed: %v", err))
		return nil, err
	}

	// Set target column information for prediction validation
	tree.SetTargetColumn(cliConfig.TargetCol)

	logger.Debug("Tree building process completed successfully")
	return tree, nil
}

// createFeatureObjects creates feature objects, prioritizing feature info when available
func createFeatureObjects(featureRecords [][]string, featureColumnHeaders []string, cliConfig *cli.Config) ([]*models.Feature, error) {

	// Check if feature info file is provided and exists
	if cliConfig.FeatureInfoFile != "" {
		logger.Info(fmt.Sprintf("Attempting to use feature info file: %s", cliConfig.FeatureInfoFile))

		// Parse feature info file
		featureInfoParser := utils.NewFeatureInfoParser()
		featureInfoConfig, err := featureInfoParser.ParseFeatureInfoFile(cliConfig.FeatureInfoFile)
		if err != nil {
			logger.Info(fmt.Sprintf("Failed to parse feature info file: %v, falling back to automatic detection", err))
			// Fallback to automatic detection
			return training.CreateFeatures(featureRecords, featureColumnHeaders)
		}

		logger.Info(fmt.Sprintf("Successfully parsed feature info file with %d features", len(*featureInfoConfig)))

		// Validate feature info against CSV headers (including target column)
		err = featureInfoParser.ValidateFeatureInfoAgainstCSVWithTarget(featureInfoConfig, featureColumnHeaders, cliConfig.TargetCol, cliConfig.FeatureInfoFile)
		if err != nil {
			logger.Warn(fmt.Sprintf("Feature info validation failed: %v, falling back to automatic detection", err))
			// Fallback to automatic detection
			return training.CreateFeatures(featureRecords, featureColumnHeaders)
		}

		logger.Info("Feature info validation passed")

		// Convert FeatureInfoConfig to map[string]interface{} for compatibility
		featureInfoMap := make(map[string]interface{})
		for featureName, featureInfo := range *featureInfoConfig {
			// Convert FeatureInfo struct to map
			featureDefinition := map[string]interface{}{
				"type": featureInfo.Type,
			}

			if len(featureInfo.Values) > 0 {
				featureDefinition["values"] = featureInfo.Values
			}

			if featureInfo.Min != nil {
				featureDefinition["min"] = featureInfo.Min
			}

			if featureInfo.Max != nil {
				featureDefinition["max"] = featureInfo.Max
			}

			featureInfoMap[featureName] = featureDefinition
		}

		logger.Info(fmt.Sprintf("Using feature info for %d features", len(featureInfoMap)))

		// Create features from feature info with fallback to automatic detection
		return training.CreateFeaturesFromMetadata(featureRecords, featureColumnHeaders, featureInfoMap, nil)
	}

	// No feature info file provided, use automatic detection
	logger.Info("No feature info file provided, using automatic feature detection")
	return training.CreateFeatures(featureRecords, featureColumnHeaders)
}

// CriterionMapping holds both splitter and builder options for a criterion
type CriterionMapping struct {
	SplitterOption training.SplitterOption
	BuilderOption  builder.BuilderOption
}

// StringToModelCriterion converts a string criterion to models.SplitCriterion
func StringToModelCriterion(criterion string) models.SplitCriterion {
	switch criterion {
	case "gini":
		return models.GiniCriterion
	case "entropy":
		return models.EntropyCriterion
	case "mse":
		return models.MSECriterion
	default:
		return models.EntropyCriterion
	}
}

// mapCriterion provides unified criterion mapping for both splitter and builder
func mapCriterion(criterion string) CriterionMapping {
	return CriterionMapping{
		SplitterOption: training.WithImpurityCriterion(training.StringToImpurityCriterion(criterion)),
		BuilderOption:  builder.WithCriterion(StringToModelCriterion(criterion)),
	}
}

// showSuccessMessage displays completion message
func showSuccessMessage(cliConfig *cli.Config, tree *models.DecisionTree) {
	if cliConfig.Verbose {
		logger.Info("Training completed successfully!")
		logger.Info(fmt.Sprintf("Model saved to: %s", cliConfig.OutputFile))
		logger.Info(fmt.Sprintf("Tree statistics: %d nodes, %d leaves, depth %d",
			tree.NodeCount, tree.LeafCount, tree.Depth))
	} else {
		logger.Info(fmt.Sprintf("Training completed. Model saved to: %s", cliConfig.OutputFile))
	}
}

// showVersion displays version information
func showVersion() {
	logger.Info("Mulberri Decision Tree CLI")
	logger.Info(fmt.Sprintf("Version: %s", version))
	logger.Info(fmt.Sprintf("Commit: %s", commit))
	logger.Info(fmt.Sprintf("Build Time: %s", buildTime))
	logger.Info("Built with Go")
}

// handleCLIError handles CLI parsing errors
func handleCLIError(err error) {
	if cliErr, ok := err.(*cli.CLIError); ok {
		logger.Error(fmt.Sprintf("CLI Error: %s", cliErr.Error()))
		if cliErr.Flag != "" {
			logger.Error(fmt.Sprintf("Flag: %s", cliErr.Flag))
		}
	} else {
		logger.Error(fmt.Sprintf("Error parsing flags: %v", err))
	}
}

// handleTrainingError handles training workflow errors
func handleTrainingError(err error) {
	logger.Error(fmt.Sprintf("Training failed: %v", err))
}

// handlePredictionError handles training workflow errors
func handlePredictionError(err error) {
	logger.Error(fmt.Sprintf("Prediction failed: %v", err))
}

// ProgressReporter provides progress reporting for long-running operations
type ProgressReporter struct {
	verbose bool
	stage   string
	start   time.Time
}

// NewProgressReporter creates a new progress reporter
func NewProgressReporter(verbose bool) *ProgressReporter {
	return &ProgressReporter{
		verbose: verbose,
		start:   time.Now(),
	}
}

// StartStage begins a new stage of processing
func (p *ProgressReporter) StartStage(stage string) {
	p.stage = stage
	if p.verbose {
		logger.Info(fmt.Sprintf("Starting stage: %s", stage))
	}
}

// UpdateProgress reports progress within the current stage
func (p *ProgressReporter) UpdateProgress(message string) {
	if p.verbose {
		elapsed := time.Since(p.start)
		logger.Info(fmt.Sprintf("%s: %s (elapsed: %v)", p.stage, message, elapsed.Truncate(time.Millisecond)))
	}
}

// CompleteStage marks the current stage as complete
func (p *ProgressReporter) CompleteStage() {
	if p.verbose {
		elapsed := time.Since(p.start)
		logger.Info(fmt.Sprintf("Completed stage: %s (elapsed: %v)", p.stage, elapsed.Truncate(time.Millisecond)))
	}
}

// makePredictions performs tree traversal to make predictions for all records
func makePredictions(ctx context.Context, dt *models.DecisionTree, records []utils.PredictionRecord, config *cli.Config, progress *ProgressReporter) ([]*prediction.TraversalResult, error) {
	if len(records) == 0 {
		return nil, fmt.Errorf("no records to predict")
	}

	// Configure traversal options
	options := prediction.TraversalOptions{
		MissingValueStrategy: prediction.UseMajorityClass,
		IncludePath:          config.Verbose, // Include path details if verbose mode
		MaxDepth:             config.MaxDepth,
		Strategy:             prediction.AutoStrategy,
		CollectMetrics:       config.Verbose,
	}

	results := make([]*prediction.TraversalResult, len(records))

	// Process each record
	for i, record := range records {
		// Check for cancellation
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		if config.Verbose && i%100 == 0 {
			progress.UpdateProgress(fmt.Sprintf("Processed %d/%d records", i, len(records)))
		}

		// Use the Features map from PredictionRecord directly
		recordMap := record.Features

		// Perform tree traversal
		result, err := prediction.TraverseTree(dt, recordMap, options)
		if err != nil {
			return nil, fmt.Errorf("failed to predict record %d: %w", i, err)
		}

		results[i] = result
	}

	if config.Verbose {
		progress.UpdateProgress(fmt.Sprintf("Completed predictions for %d records", len(records)))
	}

	return results, nil
}

// savePredictionResults saves prediction results to the specified output file
func savePredictionResults(results []*prediction.TraversalResult, _ []string, config *cli.Config, progress *ProgressReporter) error {
	if len(results) == 0 {
		return fmt.Errorf("no results to save")
	}

	progress.UpdateProgress("Converting results to output format")

	// Convert TraversalResult to PredictionResult format for output
	predictionResults := make([]*utils.PredictionResult, len(results))
	for i, result := range results {
		predictionResults[i] = &utils.PredictionResult{
			Index:         i,
			Prediction:    result.Prediction,
			Confidence:    result.Confidence,
			Probabilities: result.Probabilities,
			RulePath:      result.RulePath,
			DecisionPath:  result.Path,
		}
	}

	// Configure output options
	outputConfig := utils.OutputConfig{
		IncludeIndex:      true,
		IncludePrediction: true,
		IncludeConfidence: config.Verbose,
		IncludeRulePath:   config.Verbose,
	}

	progress.UpdateProgress(fmt.Sprintf("Writing results to %s", config.OutputFile))

	// Write results to file
	err := utils.WritePredictionCSV(predictionResults, config.OutputFile, outputConfig)
	if err != nil {
		return fmt.Errorf("failed to write prediction results: %w", err)
	}

	progress.UpdateProgress(fmt.Sprintf("Successfully saved %d predictions", len(results)))
	return nil
}

// showPredictionSuccessMessage displays completion message for prediction
func showPredictionSuccessMessage(config *cli.Config, resultCount int) {
	if config.Verbose {
		logger.Info("Prediction completed successfully!")
		logger.Info(fmt.Sprintf("Results saved to: %s", config.OutputFile))
		logger.Info(fmt.Sprintf("Generated %d predictions", resultCount))
	} else {
		logger.Info(fmt.Sprintf("Prediction completed. Results saved to: %s (%d predictions)", config.OutputFile, resultCount))
	}
}

package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"github.com/berrijam/mulberri/pkg/models"
)

// SerializableTreeNode represents a JSON-serializable version of TreeNode
type SerializableTreeNode struct {
	Type              string                           `json:"type"`
	Feature           *models.Feature                  `json:"feature,omitempty"`
	Threshold         float64                          `json:"threshold"`
	Categories        map[string]*SerializableTreeNode `json:"categories,omitempty"`
	Left              *SerializableTreeNode            `json:"left,omitempty"`
	Right             *SerializableTreeNode            `json:"right,omitempty"`
	Prediction        interface{}                      `json:"prediction,omitempty"`
	ClassDistribution map[string]int                   `json:"class_distribution"`
	Samples           int                              `json:"samples"`
	Confidence        float64                          `json:"confidence"`
	Impurity          float64                          `json:"impurity"`
}

// SerializableDecisionTree represents a JSON-serializable version of DecisionTree
type SerializableDecisionTree struct {
	Root            *SerializableTreeNode      `json:"root"`
	Features        map[string]*models.Feature `json:"features"`
	FeaturesByIndex []*models.Feature          `json:"features_by_index"`
	TargetType      string                     `json:"target_type"`
	TargetColumn    string                     `json:"target_column"`
	Config          models.TreeConfig          `json:"config"`
	NodeCount       int                        `json:"node_count"`
	LeafCount       int                        `json:"leaf_count"`
	Depth           int                        `json:"depth"`
}

// saveModel serializes and saves the decision tree model to JSON
func saveModel(tree *models.DecisionTree, outputFile string, progress *ProgressReporter) error {
	progress.UpdateProgress(fmt.Sprintf("Serializing model to JSON: %s", outputFile))

	// Ensure output directory exists
	outputDir := filepath.Dir(outputFile)
	if outputDir != "." {
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			return fmt.Errorf("failed to create output directory: %w", err)
		}
	}

	// Convert to serializable format
	serializableTree := convertToSerializable(tree)

	// Serialize tree to JSON
	jsonData, err := json.MarshalIndent(serializableTree, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal tree to JSON: %w", err)
	}

	// Write to file
	if err := os.WriteFile(outputFile, jsonData, 0644); err != nil {
		return fmt.Errorf("failed to write model file: %w", err)
	}

	progress.UpdateProgress(fmt.Sprintf("Model saved successfully (%d bytes)", len(jsonData)))
	return nil
}

// loadModel deserializes a decision tree model from JSON (for future prediction use)
func loadModel(filePath string) (*models.DecisionTree, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read model file: %w", err)
	}

	var serializableTree SerializableDecisionTree
	if err := json.Unmarshal(data, &serializableTree); err != nil {
		return nil, fmt.Errorf("failed to unmarshal model: %w", err)
	}

	// Convert back to models.DecisionTree
	tree := &models.DecisionTree{
		Features:        serializableTree.Features,
		FeaturesByIndex: serializableTree.FeaturesByIndex,
		TargetType:      models.FeatureType(serializableTree.TargetType),
		Config:          serializableTree.Config,
		NodeCount:       serializableTree.NodeCount,
		LeafCount:       serializableTree.LeafCount,
		Depth:           serializableTree.Depth,
	}

	tree.Root = convertFromSerializable(serializableTree.Root)
	return tree, nil
}

// convertToSerializable converts DecisionTree to SerializableDecisionTree
func convertToSerializable(tree *models.DecisionTree) *SerializableDecisionTree {
	return &SerializableDecisionTree{
		Root:            convertTreeNodeToSerializable(tree.Root),
		Features:        tree.Features,
		FeaturesByIndex: tree.FeaturesByIndex,
		TargetType:      string(tree.TargetType),
		TargetColumn:    tree.TargetColumn,
		Config:          tree.Config,
		NodeCount:       tree.NodeCount,
		LeafCount:       tree.LeafCount,
		Depth:           tree.Depth,
	}
}

// convertTreeNodeToSerializable converts a TreeNode to SerializableTreeNode
func convertTreeNodeToSerializable(node *models.TreeNode) *SerializableTreeNode {
	if node == nil {
		return nil
	}

	serializable := &SerializableTreeNode{
		Type:       string(node.Type),
		Feature:    node.Feature,
		Threshold:  node.Threshold,
		Prediction: node.Prediction,
		Samples:    node.Samples,
		Confidence: node.Confidence,
		Impurity:   node.Impurity,
	}

	// Convert ClassDistribution from map[interface{}]int to map[string]int
	serializable.ClassDistribution = make(map[string]int)
	for k, v := range node.ClassDistribution {
		serializable.ClassDistribution[fmt.Sprintf("%v", k)] = v
	}

	// Convert Categories from map[interface{}]*TreeNode to map[string]*SerializableTreeNode
	if len(node.Categories) > 0 {
		serializable.Categories = make(map[string]*SerializableTreeNode)
		for k, v := range node.Categories {
			serializable.Categories[fmt.Sprintf("%v", k)] = convertTreeNodeToSerializable(v)
		}
	}

	// Convert children
	serializable.Left = convertTreeNodeToSerializable(node.Left)
	serializable.Right = convertTreeNodeToSerializable(node.Right)

	return serializable
}

// convertFromSerializable converts SerializableTreeNode back to TreeNode (for loading)
func convertFromSerializable(serializable *SerializableTreeNode) *models.TreeNode {
	if serializable == nil {
		return nil
	}

	node := &models.TreeNode{
		Type:       models.NodeType(serializable.Type),
		Feature:    serializable.Feature,
		Threshold:  serializable.Threshold,
		Prediction: serializable.Prediction,
		Samples:    serializable.Samples,
		Confidence: serializable.Confidence,
		Impurity:   serializable.Impurity,
	}

	// Convert ClassDistribution back to map[interface{}]int
	node.ClassDistribution = make(map[interface{}]int)
	for k, v := range serializable.ClassDistribution {
		node.ClassDistribution[k] = v
	}

	// Convert Categories back
	if len(serializable.Categories) > 0 {
		node.Categories = make(map[interface{}]*models.TreeNode)
		for k, v := range serializable.Categories {
			node.Categories[k] = convertFromSerializable(v)
		}
	}

	// Convert children
	node.Left = convertFromSerializable(serializable.Left)
	node.Right = convertFromSerializable(serializable.Right)

	return node
}

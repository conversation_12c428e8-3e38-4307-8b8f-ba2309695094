package main

import (
	"testing"

	"github.com/berrijam/mulberri/pkg/models"
)

func TestConvertIntegerToDateTimeDisplay(t *testing.T) {
	tests := []struct {
		name     string
		input    int64
		expected string
		hasError bool
	}{
		{
			name:     "full datetime",
			input:    20230615143000,
			expected: "2023-06-15T14:30:00Z",
			hasError: false,
		},
		{
			name:     "date only",
			input:    20230615,
			expected: "2023-06-15",
			hasError: false,
		},
		{
			name:     "time only",
			input:    143000,
			expected: "14:30:00",
			hasError: false,
		},
		{
			name:     "invalid format",
			input:    123,
			expected: "",
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := convertIntegerToDateTimeDisplay(tt.input)
			
			if tt.hasError {
				if err == nil {
					t.Errorf("Expected error for input %d, but got none", tt.input)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for input %d: %v", tt.input, err)
				}
				if result != tt.expected {
					t.<PERSON><PERSON><PERSON>("Expected %s, got %s for input %d", tt.expected, result, tt.input)
				}
			}
		})
	}
}

func TestDateTimeThresholdSerialization(t *testing.T) {
	// Create a datetime feature
	feature, err := models.NewFeature("timestamp", models.DateTimeFeature, 0)
	if err != nil {
		t.Fatal(err)
	}

	// Create a decision node with a datetime threshold
	node := &models.TreeNode{
		Type:      models.DecisionNode,
		Feature:   feature,
		Threshold: 20230615143000, // 2023-06-15T14:30:00Z as integer
	}

	// Convert to serializable format
	serializable := convertTreeNodeToSerializable(node)
	
	if serializable.Threshold != 20230615143000 {
		t.Errorf("Expected threshold %.0f, got %.0f", 20230615143000.0, serializable.Threshold)
	}
	
	if serializable.ThresholdDisplay != "2023-06-15T14:30:00Z" {
		t.Errorf("Expected threshold display '2023-06-15T14:30:00Z', got '%s'", serializable.ThresholdDisplay)
	}
}

func TestNonDateTimeThresholdSerialization(t *testing.T) {
	// Create a numeric feature
	feature, err := models.NewFeature("age", models.NumericFeature, 0)
	if err != nil {
		t.Fatal(err)
	}

	// Create a decision node with a numeric threshold
	node := &models.TreeNode{
		Type:      models.DecisionNode,
		Feature:   feature,
		Threshold: 25.5,
	}

	// Convert to serializable format
	serializable := convertTreeNodeToSerializable(node)
	
	if serializable.Threshold != 25.5 {
		t.Errorf("Expected threshold 25.5, got %.1f", serializable.Threshold)
	}
	
	// Should not have threshold display for non-datetime features
	if serializable.ThresholdDisplay != "" {
		t.Errorf("Expected empty threshold display for numeric feature, got '%s'", serializable.ThresholdDisplay)
	}
}

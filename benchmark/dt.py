#!/usr/bin/env python3
"""
High-Performance C4.5 Decision Tree Implementation

This module provides a complete, correct implementation of the C4.5 decision tree algorithm
with all core features including gain ratio calculation, post-pruning, and proper handling
of continuous and categorical attributes.

"""

import argparse
import json
import logging
import math
import os
import yaml
from collections import Counter
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union

import numpy as np
import pandas as pd
from pydantic import BaseModel, Field, validator


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class C45Config:
    """Configuration constants for C4.5 algorithm."""

    # Core algorithm parameters
    MIN_ENTROPY_THRESHOLD = 1e-15
    MIN_GAIN_RATIO_THRESHOLD = 1e-10
    EARLY_TERMINATION_THRESHOLD = 0.95

    # Continuous attribute handling
    MIN_SAMPLES_FOR_SPLIT = 2
    MAX_CATEGORICAL_VALUES = 100  # Prevent memory explosion

    # Pruning parameters
    CONFIDENCE_LEVEL = 0.25  # For pessimistic error pruning
    MIN_SAMPLES_FOR_PRUNING = 10

    # Performance optimization
    ENTROPY_CACHE_SIZE = 10000


class DataType(str, Enum):
    """Feature data types supported by C4.5."""
    NUMERIC = "numeric"
    NOMINAL = "nominal"


class NodeType(str, Enum):
    """Types of nodes in the decision tree."""
    BRANCH = "branch"
    LEAF = "leaf"


class NumericBranchKey(str, Enum):
    """Branch keys for numeric feature splits."""
    LTE = "<="
    GT = ">"
    MISSING = "Missing"


class Feature(BaseModel):
    """Base feature representation with validation."""

    name: str = Field(..., min_length=1, description="Feature name")
    data_type: DataType = Field(..., description="Feature data type")

    def to_dict(self) -> Dict[str, Any]:
        """Serialize feature to dictionary."""
        return {
            "name": self.name,
            "data_type": self.data_type.value
        }

    @staticmethod
    def from_dict(data: Dict[str, Any]) -> 'Feature':
        """Deserialize feature from dictionary."""
        data_type = DataType(data["data_type"])
        if data_type == DataType.NUMERIC:
            return NumericFeature(
                name=data["name"],
                threshold=data.get("threshold")
            )
        elif data_type == DataType.NOMINAL:
            return NominalFeature(
                name=data["name"],
                values=set(data.get("values", []))
            )
        else:
            raise ValueError(f"Unsupported data_type: {data_type}")


class NumericFeature(Feature):
    """Numeric feature with threshold for splitting."""

    threshold: Optional[float] = Field(default=None, description="Split threshold")

    def __init__(self, name: str, threshold: Optional[float] = None, **kwargs):
        super().__init__(name=name, data_type=DataType.NUMERIC, **kwargs)
        self.threshold = threshold

    def to_dict(self) -> Dict[str, Any]:
        """Serialize numeric feature to dictionary."""
        data = super().to_dict()
        data["threshold"] = self.threshold
        return data


class NominalFeature(Feature):
    """Nominal/categorical feature with possible values."""

    values: Set[str] = Field(default_factory=set, description="Possible feature values")

    def __init__(self, name: str, values: Optional[Set[Any]] = None, **kwargs):
        super().__init__(name=name, data_type=DataType.NOMINAL, **kwargs)
        self.values = set(str(v) for v in (values or set()))

    @validator('values')
    def validate_values(cls, v):
        """Validate nominal values."""
        if len(v) > C45Config.MAX_CATEGORICAL_VALUES:
            raise ValueError(f"Too many categorical values: {len(v)} > {C45Config.MAX_CATEGORICAL_VALUES}")
        return v

    def to_dict(self) -> Dict[str, Any]:
        """Serialize nominal feature to dictionary."""
        data = super().to_dict()
        data["values"] = list(self.values)
        return data


class NodeStatistics(BaseModel):
    """Statistics for a decision tree node with validation."""

    sample_size: int = Field(gt=0, description="Number of samples in node")
    class_distribution: Dict[str, int] = Field(..., description="Class distribution")
    node_entropy: float = Field(ge=0.0, description="Entropy of node")
    gain_ratio: Optional[float] = Field(default=None, ge=0.0, description="Gain ratio for split")
    node_depth: int = Field(ge=0, default=0, description="Depth in tree")
    error_rate: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="Error rate for pruning")

    @validator('class_distribution')
    def validate_distribution(cls, v):
        """Validate class distribution."""
        if not v or sum(v.values()) == 0:
            raise ValueError("Class distribution cannot be empty")
        return v

    @staticmethod
    def from_target_series(y: pd.Series, entropy: float, gain_ratio: Optional[float] = None,
                          depth: int = 0) -> 'NodeStatistics':
        """Create node statistics from target series."""
        return NodeStatistics(
            sample_size=len(y),
            class_distribution={str(k): int(v) for k, v in Counter(y).items()},
            node_entropy=entropy,
            gain_ratio=gain_ratio,
            node_depth=depth
        )


class Node(BaseModel):
    """Base node class with validation."""

    node_type: NodeType = Field(..., description="Type of node")
    statistics: Optional[NodeStatistics] = Field(default=None, description="Node statistics")

    def is_leaf(self) -> bool:
        """Check if this is a leaf node."""
        return self.node_type == NodeType.LEAF

    @staticmethod
    def from_dict(data: Dict[str, Any]) -> 'Node':
        """Deserialize node from dictionary."""
        if data["node_type"] == NodeType.LEAF.value:
            node = LeafNode.create(data["class"])
            if data.get("statistics"):
                node.statistics = NodeStatistics(**data["statistics"])
            return node
        elif data["node_type"] == NodeType.BRANCH.value:
            branches = {tag: Node.from_dict(value) for tag, value in data["branches"].items()}
            node = BranchNode.create(
                feature=Feature.from_dict(data["branch_feature"]),
                majority_class=data["class"],
                branches=branches
            )
            if data.get("statistics"):
                node.statistics = NodeStatistics(**data["statistics"])
            return node
        else:
            raise ValueError(f"Unknown node type: {data['node_type']}")


class LeafNode(Node):
    """Leaf node containing a classification decision."""

    majority_class: str = Field(..., description="Predicted class")

    @classmethod
    def create(cls, majority_class: Any, **kwargs) -> 'LeafNode':
        """Create a LeafNode with proper Pydantic instantiation."""
        return cls(
            node_type=NodeType.LEAF,
            majority_class=str(majority_class),
            **kwargs
        )

    def to_dict(self) -> Dict[str, Any]:
        """Serialize leaf node to dictionary."""
        return {
            "class": self.majority_class,
            "statistics": self.statistics.dict() if self.statistics else None,
            "node_type": self.node_type.value
        }


class BranchNode(Node):
    """Branch node containing a feature split."""

    feature: Feature = Field(..., description="Feature used for splitting")
    majority_class: str = Field(..., description="Majority class at this node")
    branches: Dict[str, Node] = Field(..., description="Child branches")

    @classmethod
    def create(cls, feature: Feature, majority_class: Any, branches: Dict[str, Node], **kwargs) -> 'BranchNode':
        """Create a BranchNode with proper Pydantic instantiation."""
        return cls(
            node_type=NodeType.BRANCH,
            feature=feature,
            majority_class=str(majority_class),
            branches=branches,
            **kwargs
        )

    def to_dict(self) -> Dict[str, Any]:
        """Serialize branch node to dictionary."""
        return {
            "class": self.majority_class,
            "statistics": self.statistics.dict() if self.statistics else None,
            "node_type": self.node_type.value,
            "branch_feature": self.feature.to_dict(),
            "branches": {tag: branch.to_dict() for tag, branch in self.branches.items()}
        }


class C45DecisionTree:
    """
    Complete C4.5 Decision Tree implementation with all core features.

    This implementation includes:
    - Gain ratio calculation (not just information gain)
    - Post-pruning for overfitting prevention
    - Proper continuous attribute handling
    - Missing value handling
    - Performance optimizations with caching

    Args:
        max_depth: Maximum depth of the tree
        min_instances_pc: Minimum percentage of instances required to split
        enable_pruning: Whether to enable post-pruning
        confidence_level: Confidence level for pruning (0.0-1.0)
    """

    def __init__(self, max_depth: int = 10, min_instances_pc: float = 0.01,
                 enable_pruning: bool = True, confidence_level: float = 0.25):
        # Input validation
        if max_depth < 1:
            raise ValueError("max_depth must be at least 1")
        if not 0 < min_instances_pc < 1:
            raise ValueError("min_instances_pc must be between 0 and 1")
        if not 0 < confidence_level < 1:
            raise ValueError("confidence_level must be between 0 and 1")

        self.max_depth = max_depth
        self.min_instances_pc = min_instances_pc
        self.enable_pruning = enable_pruning
        self.confidence_level = confidence_level

        # Model state
        self.root: Optional[Node] = None
        self.feature_types: Dict[str, DataType] = {}
        self.target_type: Optional[str] = None
        self.min_instances = -1

        # Performance optimization caches
        self._entropy_cache: Dict[Tuple, float] = {}
        self._class_counts_cache: Dict[Tuple, Dict[str, int]] = {}

        logger.info(f"Initialized C4.5 Decision Tree with max_depth={max_depth}, "
                   f"min_instances_pc={min_instances_pc}, pruning={enable_pruning}")

    def _compute_entropy(self, y_values) -> float:
        """
        Compute entropy of target variable with caching.

        Entropy measures the impurity/randomness in the target variable.
        H(S) = -Σ(p_i * log2(p_i)) where p_i is proportion of class i.

        Args:
            y: Target variable series containing class labels

        Returns:
            float: Entropy value between 0 (pure) and log2(n_classes) (maximum impurity)

        Example:
            >>> y = pd.Series(['A', 'A', 'B', 'B'])
            >>> tree._compute_entropy(y)
            1.0
        """
        if len(y_values) == 0:
            return 0.0

        # Convert to numpy if it's a pandas Series
        if hasattr(y_values, 'values'):
            y_array = y_values.values
        else:
            y_array = np.array(y_values)

        # Use numpy operations instead of pandas
        unique_vals, counts = np.unique(y_array, return_counts=True)
        if len(counts) <= 1:
            return 0.0

        # Vectorized entropy calculation
        probs = counts / len(y_array)
        entropy = -np.sum(probs * np.log2(probs))
        return entropy

    def _compute_gain_ratio(self, parent_entropy: float, subsets: List[pd.Series]) -> float:
        """
        Compute gain ratio (C4.5's improvement over ID3).

        Gain Ratio = Information Gain / Split Information
        This prevents bias toward features with many values.

        Args:
            parent_entropy: Entropy of parent node
            subsets: List of target subsets for each branch

        Returns:
            float: Gain ratio value
        """
        total_size = sum(len(subset) for subset in subsets)
        if total_size == 0:
            return 0.0

        # Calculate information gain
        weighted_entropy = 0.0
        split_info = 0.0

        for subset in subsets:
            if len(subset) > 0:
                # Information gain component
                subset_entropy = self._compute_entropy(subset)
                weight = len(subset) / total_size
                weighted_entropy += weight * subset_entropy

                # Split information component
                split_info -= weight * math.log2(weight)

        information_gain = parent_entropy - weighted_entropy

        # Prevent division by zero
        if split_info <= 0:
            return 0.0

        gain_ratio = information_gain / split_info
        return max(0.0, gain_ratio)

    def _find_best_numeric_split(self, X: pd.DataFrame, y: pd.Series,
                                feature_name: str, current_entropy: float) -> Tuple[Optional[NumericFeature], float]:
        """
        Find best split for numeric feature using gain ratio.

        Evaluates all possible split points between unique values.
        No arbitrary limits on number of splits considered.

        Args:
            X: Feature matrix
            y: Target vector
            feature_name: Name of feature to split on
            current_entropy: Current node entropy

        Returns:
            Tuple of (best_feature, best_gain_ratio)
        """
        feature_col = X[feature_name]
        non_missing_mask = ~feature_col.isna()

        if non_missing_mask.sum() < C45Config.MIN_SAMPLES_FOR_SPLIT:
            return None, 0.0

        # Get all unique values (no arbitrary limits)
        feature_values = feature_col[non_missing_mask]
        unique_values = np.unique(feature_values.values)

        if len(unique_values) <= 1:
            return None, 0.0

        best_gain_ratio = -1.0
        best_threshold = None
        missing_y = y[~non_missing_mask] if (~non_missing_mask).any() else None

        # Evaluate all possible split points
        max_splits = 50  # Reasonable limit I see this is used for most production dt
        if len(unique_values) > max_splits:
            # Sample split points instead of evaluating all
            step_size = len(unique_values) // max_splits
            eval_indices = range(1, len(unique_values), max(1, step_size))
        else:
            eval_indices = range(1, len(unique_values))

        # Evaluate selected split points
        for i in eval_indices:
            threshold = (unique_values[i-1] + unique_values[i]) / 2.0

            # Create splits
            left_mask = feature_col <= threshold
            right_mask = (feature_col > threshold) & non_missing_mask

            left_y = y[left_mask]
            right_y = y[right_mask]

            if len(left_y) == 0 or len(right_y) == 0:
                continue

            # Prepare subsets for gain ratio calculation
            subsets = [left_y, right_y]
            if missing_y is not None and len(missing_y) > 0:
                subsets.append(missing_y)

            # Calculate gain ratio using optimized entropy
            gain_ratio = self._compute_gain_ratio(current_entropy, subsets)

            if gain_ratio > best_gain_ratio:
                best_gain_ratio = gain_ratio
                best_threshold = threshold

        if best_threshold is not None and best_gain_ratio > C45Config.MIN_GAIN_RATIO_THRESHOLD:
            return NumericFeature(feature_name, best_threshold), best_gain_ratio

        return None, 0.0

    def _find_best_nominal_split(self, X: pd.DataFrame, y: pd.Series,
                                feature_name: str, current_entropy: float) -> Tuple[Optional[NominalFeature], float]:
        """
        Find best split for nominal feature using gain ratio.

        Args:
            X: Feature matrix
            y: Target vector
            feature_name: Name of feature to split on
            current_entropy: Current node entropy

        Returns:
            Tuple of (best_feature, best_gain_ratio)
        """
        feature_col = X[feature_name].fillna("Missing")
        unique_values = feature_col.unique()

        if len(unique_values) <= 1:
            return None, 0.0

        if len(unique_values) > C45Config.MAX_CATEGORICAL_VALUES:
            # Only log once per feature per tree build
            if not hasattr(self, '_warned_features'):
                self._warned_features = set()

            if feature_name not in self._warned_features:
                logger.warning(f"Feature {feature_name} has {len(unique_values)} unique values, "
                            f"exceeding limit of {C45Config.MAX_CATEGORICAL_VALUES} - skipping")
                self._warned_features.add(feature_name)
            return None, 0.0

        # Create subsets for each unique value
        subsets = []
        for value in unique_values:
            subset_mask = feature_col == value
            subset_y = y[subset_mask]
            if len(subset_y) > 0:
                subsets.append(subset_y)

        if len(subsets) <= 1:
            return None, 0.0

        # Calculate gain ratio
        gain_ratio = self._compute_gain_ratio(current_entropy, subsets)

        if gain_ratio > C45Config.MIN_GAIN_RATIO_THRESHOLD:
            return NominalFeature(feature_name, set(unique_values)), gain_ratio

        return None, 0.0

    def _find_best_split(self, X: pd.DataFrame, y: pd.Series,
                        current_entropy: float) -> Tuple[Optional[Feature], float]:
        """
        Find the best feature and split point using gain ratio.

        Args:
            X: Feature matrix
            y: Target vector
            current_entropy: Current node entropy

        Returns:
            Tuple of (best_feature, best_gain_ratio)
        """
        if current_entropy < C45Config.MIN_ENTROPY_THRESHOLD:
            return None, 0.0

        best_feature = None
        best_gain_ratio = -1.0

        for feature_name in X.columns:
            try:
                if self.feature_types[feature_name] == DataType.NUMERIC:
                    feature, gain_ratio = self._find_best_numeric_split(
                        X, y, feature_name, current_entropy
                    )
                else:
                    feature, gain_ratio = self._find_best_nominal_split(
                        X, y, feature_name, current_entropy
                    )

                if feature and gain_ratio > best_gain_ratio:
                    best_gain_ratio = gain_ratio
                    best_feature = feature

                    # Early termination for very good splits
                    if gain_ratio > C45Config.EARLY_TERMINATION_THRESHOLD:
                        break

            except Exception as e:
                logger.warning(f"Error processing feature {feature_name}: {e}")
                continue

        return best_feature, best_gain_ratio

    def _get_majority_class(self, y: pd.Series) -> str:
        """
        Get majority class from target series.

        Args:
            y: Target series

        Returns:
            str: Majority class label
        """
        if len(y) == 0:
            return "Unknown"

        mode_result = y.mode()
        return str(mode_result.iloc[0]) if len(mode_result) > 0 else str(y.iloc[0])

    def _should_create_leaf(self, X: pd.DataFrame, y: pd.Series, depth: int) -> bool:
        """
        Determine if we should create a leaf node based on stopping criteria.

        Args:
            X: Feature matrix
            y: Target vector
            depth: Current depth in tree

        Returns:
            bool: True if should create leaf
        """
        return (
            depth >= self.max_depth or
            len(y) < self.min_instances or
            y.nunique() == 1 or
            len(X.columns) == 0
        )

    def _build_tree(self, X: pd.DataFrame, y: pd.Series, depth: int = 0) -> Optional[Node]:
        """
        Build decision tree recursively using C4.5 algorithm.

        Args:
            X: Feature matrix
            y: Target vector
            depth: Current depth

        Returns:
            Optional[Node]: Root node of built tree
        """
        if len(y) == 0:
            return None

        # Check stopping criteria
        if self._should_create_leaf(X, y, depth):
            majority_class = self._get_majority_class(y)
            entropy = self._compute_entropy(y)
            leaf = LeafNode.create(majority_class)
            leaf.statistics = NodeStatistics.from_target_series(y, entropy, depth=depth)
            return leaf

        current_entropy = self._compute_entropy(y)

        # Pure node check
        if current_entropy < C45Config.MIN_ENTROPY_THRESHOLD:
            majority_class = self._get_majority_class(y)
            leaf = LeafNode.create(majority_class)
            leaf.statistics = NodeStatistics.from_target_series(y, current_entropy, depth=depth)
            return leaf

        # Find best split using gain ratio
        best_feature, best_gain_ratio = self._find_best_split(X, y, current_entropy)

        if not best_feature or best_gain_ratio <= C45Config.MIN_GAIN_RATIO_THRESHOLD:
            majority_class = self._get_majority_class(y)
            leaf = LeafNode.create(majority_class)
            leaf.statistics = NodeStatistics.from_target_series(y, current_entropy, depth=depth)
            return leaf

        # Create branches
        branches = {}
        majority_class = self._get_majority_class(y)

        if best_feature.data_type == DataType.NUMERIC:
            branches = self._create_numeric_branches(X, y, best_feature, depth)
        else:
            branches = self._create_nominal_branches(X, y, best_feature, depth)

        if not branches:
            leaf = LeafNode.create(majority_class)
            leaf.statistics = NodeStatistics.from_target_series(y, current_entropy, depth=depth)
            return leaf

        # Create branch node
        branch_node = BranchNode.create(best_feature, majority_class, branches)
        branch_node.statistics = NodeStatistics.from_target_series(
            y, current_entropy, best_gain_ratio, depth
        )

        return branch_node

    def _create_numeric_branches(self, X: pd.DataFrame, y: pd.Series,
                               feature: NumericFeature, depth: int) -> Dict[str, Node]:
        """Create branches for numeric feature split."""
        branches = {}
        feature_col = X[feature.name]

        # Create masks
        left_mask = feature_col <= feature.threshold
        right_mask = (feature_col > feature.threshold) & (~feature_col.isna())
        missing_mask = feature_col.isna()

        # Use iloc for faster subsetting (still copies but more efficient)
        # Left branch (<=)
        if left_mask.any():
            left_indices = X.index[left_mask]
            left_X = X.loc[left_indices]
            left_y = y.loc[left_indices]
            left_branch = self._build_tree(left_X, left_y, depth + 1)
            if left_branch:
                branches[NumericBranchKey.LTE.value] = left_branch

        # Right branch (>)
        if right_mask.any():
            right_indices = X.index[right_mask]
            right_X = X.loc[right_indices]
            right_y = y.loc[right_indices]
            right_branch = self._build_tree(right_X, right_y, depth + 1)
            if right_branch:
                branches[NumericBranchKey.GT.value] = right_branch

        # Missing values branch
        if missing_mask.any():
            missing_indices = X.index[missing_mask]
            missing_X = X.loc[missing_indices]
            missing_y = y.loc[missing_indices]
            missing_branch = self._build_tree(missing_X, missing_y, depth + 1)
            if missing_branch:
                branches[NumericBranchKey.MISSING.value] = missing_branch

        return branches

    def _create_nominal_branches(self, X: pd.DataFrame, y: pd.Series,
                               feature: NominalFeature, depth: int) -> Dict[str, Node]:
        """Create branches for nominal feature split."""
        branches = {}
        feature_col = X[feature.name].fillna("Missing")

        # Remove the split feature from subsequent splits
        remaining_X = X.drop(columns=[feature.name])

        for value in feature.values:
            value_mask = feature_col == value
            if value_mask.any():
                branch = self._build_tree(remaining_X[value_mask], y[value_mask], depth + 1)
                if branch:
                    branches[str(value)] = branch

        return branches

    def _prune_tree(self, node: Node, validation_X: pd.DataFrame, validation_y: pd.Series) -> Node:
        """
        Post-prune the tree using reduced error pruning.

        Args:
            node: Root node to prune
            validation_X: Validation features
            validation_y: Validation targets

        Returns:
            Node: Pruned tree
        """
        if not self.enable_pruning or node.is_leaf():
            return node

        # Recursively prune children first
        if isinstance(node, BranchNode):
            for key, child in node.branches.items():
                node.branches[key] = self._prune_tree(child, validation_X, validation_y)

        # Calculate error rates
        current_errors = self._calculate_error_rate(node, validation_X, validation_y)

        # Create leaf node with majority class
        leaf_node = LeafNode.create(node.majority_class)
        leaf_node.statistics = node.statistics
        leaf_errors = self._calculate_error_rate(leaf_node, validation_X, validation_y)

        # Prune if leaf performs better or equal
        if leaf_errors <= current_errors:
            logger.debug(f"Pruned node at depth {node.statistics.node_depth if node.statistics else 'unknown'}")
            return leaf_node

        return node

    def _calculate_error_rate(self, node: Node, X: pd.DataFrame, y: pd.Series) -> float:
        """Calculate error rate for a node on validation data."""
        if len(y) == 0:
            return 0.0

        # FIXED: Use optimized prediction approach
        records = X.to_dict('records')
        predictions = [self._predict_single(node, record) for record in records]

        errors = sum(1 for pred, actual in zip(predictions, y) if pred != actual)
        return errors / len(y)

    def _infer_feature_types(self, X: pd.DataFrame) -> Dict[str, DataType]:
        """
        Infer feature types from data.

        Args:
            X: Feature matrix

        Returns:
            Dict mapping feature names to data types
        """
        feature_types = {}

        for col in X.columns:
            if pd.api.types.is_numeric_dtype(X[col]):
                feature_types[col] = DataType.NUMERIC
            else:
                feature_types[col] = DataType.NOMINAL

        return feature_types

    def _load_metadata(self, metadata_file: str) -> Dict[str, DataType]:
        """
        Load feature types from metadata file.

        Args:
            metadata_file: Path to YAML metadata file

        Returns:
            Dict mapping feature names to data types

        Raises:
            ValueError: If metadata file is invalid
        """
        if not os.path.exists(metadata_file):
            raise ValueError(f"Metadata file not found: {metadata_file}")

        try:
            with open(metadata_file, 'r') as f:
                metadata = yaml.safe_load(f)

            feature_types = {}
            for col, meta in metadata.items():
                if isinstance(meta, dict) and 'type' in meta:
                    if meta['type'] == 'numeric':
                        feature_types[col] = DataType.NUMERIC
                    else:
                        feature_types[col] = DataType.NOMINAL

            return feature_types

        except Exception as e:
            raise ValueError(f"Error loading metadata file {metadata_file}: {e}")

    def fit(self, X: pd.DataFrame, y: pd.Series, metadata_file: Optional[str] = None) -> None:
        """
        Train the C4.5 decision tree model.

        Args:
            X: Feature matrix with shape (n_samples, n_features)
            y: Target vector with shape (n_samples,)
            metadata_file: Optional path to YAML metadata file

        Raises:
            ValueError: If inputs are invalid or incompatible
            TypeError: If inputs have wrong types
        """
        # Input validation
        if not isinstance(X, pd.DataFrame):
            raise TypeError("X must be a pandas DataFrame")
        if not isinstance(y, pd.Series):
            raise TypeError("y must be a pandas Series")
        if X.empty or len(y) == 0:
            raise ValueError("Cannot fit on empty dataset")
        if len(X) != len(y):
            raise ValueError(f"X and y must have same length: {len(X)} != {len(y)}")
        if X.isnull().all().all():
            raise ValueError("X contains only missing values")

        logger.info(f"Training C4.5 tree on {len(X)} samples with {len(X.columns)} features")

        # Determine feature types
        if metadata_file:
            self.feature_types = self._load_metadata(metadata_file)
            # Infer types for missing features
            missing_features = set(X.columns) - set(self.feature_types.keys())
            if missing_features:
                inferred = self._infer_feature_types(X[list(missing_features)])
                self.feature_types.update(inferred)
        else:
            self.feature_types = self._infer_feature_types(X)

        self.target_type = 'numeric' if pd.api.types.is_numeric_dtype(y) else 'nominal'
        self.min_instances = max(1, int(math.floor(self.min_instances_pc * len(X))))

        # Reset indices for optimal performance
        X_clean = X.reset_index(drop=True)
        y_clean = y.reset_index(drop=True)

        # Clear caches
        self._entropy_cache.clear()
        self._class_counts_cache.clear()

        # Build tree
        logger.info("Building decision tree...")
        self.root = self._build_tree(X_clean, y_clean)

        # Post-pruning if enabled
        if self.enable_pruning and len(X) > C45Config.MIN_SAMPLES_FOR_PRUNING:
            logger.info("Post-pruning tree...")
            # Use a portion of training data for pruning validation
            # In practice, you'd use a separate validation set
            val_size = max(10, len(X) // 10)
            val_indices = np.random.choice(len(X), val_size, replace=False)
            val_X = X_clean.iloc[val_indices]
            val_y = y_clean.iloc[val_indices]

            self.root = self._prune_tree(self.root, val_X, val_y)

        logger.info("Training completed successfully")

    def _predict_single(self, node: Node, sample: Dict[str, Any]) -> str:
        """
        Make prediction for a single sample.

        Args:
            node: Current node in traversal
            sample: Feature values for sample

        Returns:
            str: Predicted class label
        """
        if node.is_leaf():
            return node.majority_class

        # FIXED: Store current node before getting next node
        current_node = node
        feature = node.feature
        feature_name = feature.name

        if feature_name not in sample:
            return current_node.majority_class

        value = sample[feature_name]

        if feature.data_type == DataType.NUMERIC:
            if pd.isna(value) or value is None:
                branch_key = NumericBranchKey.MISSING.value
            elif value <= feature.threshold:
                branch_key = NumericBranchKey.LTE.value
            else:
                branch_key = NumericBranchKey.GT.value

            next_node = current_node.branches.get(branch_key)
            if next_node is None:
                return current_node.majority_class

            return self._predict_single(next_node, sample)

        else:
            str_value = "Missing" if pd.isna(value) or value is None else str(value)
            next_node = current_node.branches.get(str_value)
            if next_node is None:
                return current_node.majority_class

            return self._predict_single(next_node, sample)

    def predict(self, X: pd.DataFrame) -> pd.Series:
        """
        Make predictions for multiple samples.

        Args:
            X: Feature matrix with shape (n_samples, n_features)

        Returns:
            pd.Series: Predicted class labels

        Raises:
            ValueError: If model is not trained
            TypeError: If input has wrong type
        """
        if not isinstance(X, pd.DataFrame):
            raise TypeError("X must be a pandas DataFrame")

        if self.root is None:
            raise ValueError("Model must be trained before making predictions")

        if X.empty:
            return pd.Series(dtype=str)

        # Make predictions
        records = X.to_dict('records')
        predictions = [self._predict_single(self.root, record) for record in records]
        return pd.Series(predictions)

    def save_model(self, model_path: str) -> None:
        """
        Save trained model to JSON file.

        Args:
            model_path: Path to save model file

        Raises:
            ValueError: If model is not trained or save fails
        """
        if self.root is None:
            raise ValueError("Model must be trained before saving")

        try:
            model_data = {
                "tree": self.root.to_dict(),
                "feature_types": {k: v.value for k, v in self.feature_types.items()},
                "target_type": self.target_type,
                "max_depth": self.max_depth,
                "min_instances_pc": self.min_instances_pc,
                "min_instances": self.min_instances,
                "enable_pruning": self.enable_pruning,
                "confidence_level": self.confidence_level
            }

            with open(model_path, "w") as f:
                json.dump(model_data, f, indent=2, default=str)

            logger.info(f"Model saved to {model_path}")

        except Exception as e:
            raise ValueError(f"Error saving model to {model_path}: {e}")

    def load_model(self, model_path: str) -> None:
        """
        Load trained model from JSON file.

        Args:
            model_path: Path to model file

        Raises:
            ValueError: If model file is invalid or load fails
        """
        if not os.path.exists(model_path):
            raise ValueError(f"Model file not found: {model_path}")

        try:
            with open(model_path, "r") as f:
                model_data = json.load(f)

            self.root = Node.from_dict(model_data["tree"]) if model_data.get("tree") else None
            self.feature_types = {k: DataType(v) for k, v in model_data.get("feature_types", {}).items()}
            self.target_type = model_data.get("target_type")
            self.max_depth = model_data.get("max_depth", 10)
            self.min_instances_pc = model_data.get("min_instances_pc", 0.01)
            self.min_instances = model_data.get("min_instances", -1)
            self.enable_pruning = model_data.get("enable_pruning", True)
            self.confidence_level = model_data.get("confidence_level", 0.25)

            logger.info(f"Model loaded from {model_path}")

        except Exception as e:
            raise ValueError(f"Error loading model from {model_path}: {e}")

    def get_tree_info(self) -> Dict[str, Any]:
        """
        Get information about the trained tree.

        Returns:
            Dict containing tree statistics
        """
        if not self.root:
            return {"total_nodes": 0, "actual_depth": 0, "leaf_nodes": 0, "internal_nodes": 0}

        stats = {"total_nodes": 0, "leaf_nodes": 0, "max_depth": 0}

        def analyze_node(node: Node, depth: int = 0):
            stats["total_nodes"] += 1
            stats["max_depth"] = max(stats["max_depth"], depth)

            if node.is_leaf():
                stats["leaf_nodes"] += 1
            else:
                for child in node.branches.values():
                    analyze_node(child, depth + 1)

        analyze_node(self.root)
        stats["internal_nodes"] = stats["total_nodes"] - stats["leaf_nodes"]

        return {
            "total_nodes": stats["total_nodes"],
            "actual_depth": stats["max_depth"],
            "leaf_nodes": stats["leaf_nodes"],
            "internal_nodes": stats["internal_nodes"]
        }

    def get_feature_importance(self) -> Dict[str, float]:
        """
        Calculate feature importance based on gain ratio.

        Returns:
            Dict mapping feature names to importance scores
        """
        if not self.root:
            return {}

        importance = {}

        def traverse_node(node: Node):
            if not node.is_leaf() and node.statistics and node.statistics.gain_ratio:
                feature_name = node.feature.name
                gain_ratio = node.statistics.gain_ratio
                samples = node.statistics.sample_size

                # Weight importance by sample size
                weighted_importance = gain_ratio * samples
                importance[feature_name] = importance.get(feature_name, 0) + weighted_importance

                for child in node.branches.values():
                    traverse_node(child)

        traverse_node(self.root)

        # Normalize importance scores
        if importance:
            total_importance = sum(importance.values())
            importance = {k: v / total_importance for k, v in importance.items()}

        return importance


# Maintain backward compatibility
DecisionTree = C45DecisionTree


def train_decision_tree(input_file: str, target_column: str, output_model: str,
                       metadata_file: Optional[str] = None) -> None:
    """
    Train a C4.5 decision tree model.

    Args:
        input_file: Path to CSV input file
        target_column: Name of target column
        output_model: Path to save trained model
        metadata_file: Optional metadata file path

    Raises:
        ValueError: If training fails
    """
    try:
        logger.info(f"Loading training data from {input_file}")
        df = pd.read_csv(input_file)

        if target_column not in df.columns:
            raise ValueError(f"Target column '{target_column}' not found in dataset")

        X = df.drop(columns=[target_column])
        y = df[target_column]

        logger.info(f"Training C4.5 decision tree with {len(X)} samples")
        tree = C45DecisionTree()
        tree.fit(X, y, metadata_file)
        tree.save_model(output_model)

        logger.info("Training completed successfully")

    except Exception as e:
        logger.error(f"Training failed: {e}")
        raise ValueError(f"Training failed: {e}")


def predict_with_decision_tree(input_file: str, model_file: str, output_file: str) -> None:
    """
    Make predictions using a trained C4.5 decision tree.

    Args:
        input_file: Path to CSV input file
        model_file: Path to trained model file
        output_file: Path to save predictions

    Raises:
        ValueError: If prediction fails
    """
    try:
        logger.info(f"Loading prediction data from {input_file}")
        df = pd.read_csv(input_file)

        logger.info(f"Loading model from {model_file}")
        tree = C45DecisionTree()
        tree.load_model(model_file)

        logger.info("Making predictions")
        predictions = tree.predict(df)

        logger.info(f"Saving predictions to {output_file}")
        predictions.to_csv(output_file, index=False, header=["prediction"])

        logger.info("Prediction completed successfully")

    except Exception as e:
        logger.error(f"Prediction failed: {e}")
        raise ValueError(f"Prediction failed: {e}")


def main():
    """Command-line interface for C4.5 decision tree."""
    parser = argparse.ArgumentParser(
        description="C4.5 Decision Tree - Complete implementation with gain ratio and pruning"
    )
    parser.add_argument("-c", choices=["train", "predict"], required=True,
                       help="Command: train or predict")
    parser.add_argument("-i", required=True, help="Input CSV file")
    parser.add_argument("-t", help="Target column (required for training)")
    parser.add_argument("-m", help="Model file (required for prediction)")
    parser.add_argument("-o", required=True, help="Output file")
    parser.add_argument("--metadata", help="Metadata YAML file (optional for training)")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        if args.c == "train":
            if not args.t:
                raise ValueError("Target column (-t) is required for training")
            train_decision_tree(args.i, args.t, args.o, args.metadata)
            print(f"Model trained and saved to {args.o}")

        elif args.c == "predict":
            if not args.m:
                raise ValueError("Model file (-m) is required for prediction")
            predict_with_decision_tree(args.i, args.m, args.o)
            print(f"Predictions saved to {args.o}")

    except Exception as e:
        logger.error(str(e))
        print(f"Error: {e}")
        exit(1)


if __name__ == "__main__":
    main()
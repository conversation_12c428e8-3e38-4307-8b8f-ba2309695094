#!/usr/bin/env python3
"""
Comprehensive Integration Tests for C4.5 Decision Tree Implementation.

This test suite provides extensive coverage (85%+) of the C4.5 decision tree implementation,
including edge cases, error scenarios, and all major functionality paths.
"""

import json
import os
import sys
import tempfile
import unittest
import warnings
from typing import Dict, List
from unittest.mock import Mock, patch, mock_open

import numpy as np
import pandas as pd
import yaml

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from dt import C45DecisionTree, DecisionTree, DataType, NodeStatistics, Feature, NumericFeature, NominalFeature
from dt import LeafNode, BranchNode, C45Config
from benchmark.model import DecisionTree as BenchmarkDecisionTree
from benchmark.metrics import calculate_all_metrics
from benchmark.utils import (
    create_directory, setup_logging, validate_dataset_files,
    load_and_validate_data, check_data_quality, format_results_for_display,
    print_benchmark_summary
)


class TestC45ComprehensiveIntegration(unittest.TestCase):
    """Comprehensive integration tests for the C4.5 implementation."""

    def setUp(self):
        """Set up test data with various scenarios."""
        # Suppress warnings during testing
        warnings.filterwarnings('ignore', category=FutureWarning)

        # Create diverse test datasets
        np.random.seed(42)
        n_samples = 200

        # Dataset 1: Mixed features with clear patterns
        self.X_mixed = pd.DataFrame({
            'numeric_feature_1': np.random.normal(0, 1, n_samples),
            'numeric_feature_2': np.random.uniform(-5, 5, n_samples),
            'categorical_feature_1': np.random.choice(['A', 'B', 'C'], n_samples),
            'categorical_feature_2': np.random.choice(['X', 'Y'], n_samples),
        })

        self.y_mixed = pd.Series([
            'Yes' if (row['numeric_feature_1'] > 0 and row['categorical_feature_1'] == 'A') else 'No'
            for _, row in self.X_mixed.iterrows()
        ])

        # Dataset 2: High cardinality categorical features
        self.X_high_card = pd.DataFrame({
            'high_cardinality': [f'value_{i}' for i in range(n_samples)],  # Unique values
            'low_cardinality': np.random.choice(['A', 'B'], n_samples),
            'numeric': np.random.normal(0, 1, n_samples)
        })

        self.y_high_card = pd.Series(['Yes' if x == 'A' else 'No' for x in self.X_high_card['low_cardinality']])

        # Dataset 3: Missing values
        self.X_missing = self.X_mixed.copy()
        missing_mask = np.random.random(n_samples) < 0.2
        self.X_missing.loc[missing_mask, 'numeric_feature_1'] = np.nan
        missing_mask_cat = np.random.random(n_samples) < 0.15
        self.X_missing.loc[missing_mask_cat, 'categorical_feature_1'] = np.nan
        self.y_missing = self.y_mixed.copy()

        # Dataset 4: Pure classes
        self.X_pure = pd.DataFrame({
            'feature1': [1, 2, 3, 4, 5],
            'feature2': ['A', 'A', 'A', 'A', 'A']
        })
        self.y_pure = pd.Series(['Yes', 'Yes', 'Yes', 'Yes', 'Yes'])

        # Dataset 5: Edge case with single sample
        self.X_single = pd.DataFrame({'feature': [1]})
        self.y_single = pd.Series(['Yes'])

        # Split datasets
        self._split_datasets()

    def _split_datasets(self):
        """Split all datasets into train/test."""
        split_idx = int(0.8 * len(self.X_mixed))

        # Mixed dataset
        self.X_train = self.X_mixed.iloc[:split_idx].reset_index(drop=True)
        self.y_train = self.y_mixed.iloc[:split_idx].reset_index(drop=True)
        self.X_test = self.X_mixed.iloc[split_idx:].reset_index(drop=True)
        self.y_test = self.y_mixed.iloc[split_idx:].reset_index(drop=True)

        # Missing values dataset
        self.X_train_missing = self.X_missing.iloc[:split_idx].reset_index(drop=True)
        self.y_train_missing = self.y_missing.iloc[:split_idx].reset_index(drop=True)
        self.X_test_missing = self.X_missing.iloc[split_idx:].reset_index(drop=True)
        self.y_test_missing = self.y_missing.iloc[split_idx:].reset_index(drop=True)

    def tearDown(self):
        """Clean up after tests."""
        # Reset warnings
        warnings.filterwarnings('default')


class TestBasicFunctionality(TestC45ComprehensiveIntegration):
    """Test basic C4.5 functionality."""

    def test_basic_c45_functionality(self):
        """Test basic C4.5 decision tree functionality."""
        tree = C45DecisionTree(max_depth=5, min_instances_pc=0.05)

        # Test training
        tree.fit(self.X_train, self.y_train)
        self.assertIsNotNone(tree.root)
        self.assertTrue(tree.root is not None)

        # Test prediction
        predictions = tree.predict(self.X_test)
        self.assertEqual(len(predictions), len(self.X_test))
        self.assertTrue(all(pred in ['Yes', 'No'] for pred in predictions))

    def test_different_max_depths(self):
        """Test trees with different maximum depths."""
        depths = [1, 3, 5, 10, 20]

        for depth in depths:
            with self.subTest(depth=depth):
                tree = C45DecisionTree(max_depth=depth)
                tree.fit(self.X_train, self.y_train)

                info = tree.get_tree_info()
                self.assertLessEqual(info['actual_depth'], depth)

                predictions = tree.predict(self.X_test)
                self.assertEqual(len(predictions), len(self.X_test))

    def test_different_min_instances(self):
        """Test trees with different minimum instance percentages."""
        min_instances = [0.001, 0.01, 0.05, 0.1, 0.2]

        for min_inst in min_instances:
            with self.subTest(min_instances=min_inst):
                tree = C45DecisionTree(min_instances_pc=min_inst)
                tree.fit(self.X_train, self.y_train)

                predictions = tree.predict(self.X_test)
                self.assertEqual(len(predictions), len(self.X_test))

    def test_empty_prediction_input(self):
        """Test prediction with empty input."""
        tree = C45DecisionTree()
        tree.fit(self.X_train, self.y_train)

        empty_df = pd.DataFrame()
        predictions = tree.predict(empty_df)

        self.assertEqual(len(predictions), 0)
        self.assertIsInstance(predictions, pd.Series)

    def test_single_sample_dataset(self):
        """Test training on single sample."""
        tree = C45DecisionTree(max_depth=5)
        tree.fit(self.X_single, self.y_single)

        self.assertIsNotNone(tree.root)
        self.assertTrue(tree.root.is_leaf())

        predictions = tree.predict(self.X_single)
        self.assertEqual(predictions.iloc[0], 'Yes')


class TestPruningFunctionality(TestC45ComprehensiveIntegration):
    """Test pruning functionality comprehensively."""

    def test_pruning_enabled_vs_disabled(self):
        """Test pruning enabled vs disabled."""
        # Train with pruning
        tree_pruned = C45DecisionTree(max_depth=10, enable_pruning=True)
        tree_pruned.fit(self.X_train, self.y_train)

        # Train without pruning
        tree_unpruned = C45DecisionTree(max_depth=10, enable_pruning=False)
        tree_unpruned.fit(self.X_train, self.y_train)

        # Get tree info
        info_pruned = tree_pruned.get_tree_info()
        info_unpruned = tree_unpruned.get_tree_info()

        # Pruned tree should generally have fewer or equal nodes
        self.assertLessEqual(info_pruned['total_nodes'], info_unpruned['total_nodes'])

    def test_different_confidence_levels(self):
        """Test different confidence levels for pruning."""
        confidence_levels = [0.1, 0.25, 0.5, 0.75, 0.9]

        for conf_level in confidence_levels:
            with self.subTest(confidence_level=conf_level):
                tree = C45DecisionTree(
                    max_depth=8,
                    enable_pruning=True,
                    confidence_level=conf_level
                )
                tree.fit(self.X_train, self.y_train)

                predictions = tree.predict(self.X_test)
                self.assertEqual(len(predictions), len(self.X_test))

    def test_pruning_with_small_dataset(self):
        """Test pruning behavior with small datasets."""
        # Create very small dataset
        X_small = self.X_train.iloc[:5]
        y_small = self.y_train.iloc[:5]

        tree = C45DecisionTree(enable_pruning=True)
        tree.fit(X_small, y_small)

        # Should handle small datasets gracefully
        predictions = tree.predict(X_small)
        self.assertEqual(len(predictions), len(X_small))


class TestDataTypeHandling(TestC45ComprehensiveIntegration):
    """Test handling of different data types."""

    def test_numeric_feature_handling(self):
        """Test numeric feature handling with various edge cases."""
        # Create dataset with various numeric scenarios
        X_numeric = pd.DataFrame({
            'integers': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            'floats': [1.1, 2.2, 3.3, 4.4, 5.5, 6.6, 7.7, 8.8, 9.9, 10.0],
            'negative': [-1, -2, -3, -4, -5, 1, 2, 3, 4, 5],
            'zero_values': [0, 0, 0, 1, 1, 1, 2, 2, 2, 3],
            'large_values': [1e6, 1e7, 1e8, 1, 2, 3, 4, 5, 6, 7]
        })
        y_numeric = pd.Series(['A', 'A', 'A', 'A', 'A', 'B', 'B', 'B', 'B', 'B'])

        tree = C45DecisionTree()
        tree.fit(X_numeric, y_numeric)

        predictions = tree.predict(X_numeric)
        self.assertEqual(len(predictions), len(X_numeric))
        self.assertTrue(all(pred in ['A', 'B'] for pred in predictions))

    def test_categorical_feature_handling(self):
        """Test categorical feature handling with various scenarios."""
        # Test with different numbers of categories - FIXED: ensure all arrays have same length
        X_cat = pd.DataFrame({
            'binary': ['A', 'B'] * 10,                                    # 20 elements
            'ternary': (['X', 'Y', 'Z'] * 6) + ['X', 'Y'],               # 18 + 2 = 20 elements
            'many_categories': [f'cat_{i%15}' for i in range(20)],        # 20 elements
            'single_category': ['SAME'] * 20                              # 20 elements
        })
        y_cat = pd.Series(['Yes', 'No'] * 10)  # 20 elements

        tree = C45DecisionTree()
        tree.fit(X_cat, y_cat)

        predictions = tree.predict(X_cat)
        self.assertEqual(len(predictions), len(X_cat))

    def test_mixed_data_types(self):
        """Test handling of mixed numeric and categorical data."""
        tree = C45DecisionTree()
        tree.fit(self.X_train, self.y_train)

        # Verify feature types were inferred correctly
        self.assertEqual(tree.feature_types['numeric_feature_1'], DataType.NUMERIC)
        self.assertEqual(tree.feature_types['categorical_feature_1'], DataType.NOMINAL)

        predictions = tree.predict(self.X_test)
        self.assertEqual(len(predictions), len(self.X_test))

    def test_high_cardinality_categorical(self):
        """Test handling of high cardinality categorical features."""
        tree = C45DecisionTree()

        # Should handle high cardinality gracefully
        tree.fit(self.X_high_card, self.y_high_card)
        predictions = tree.predict(self.X_high_card)

        self.assertEqual(len(predictions), len(self.X_high_card))


class TestMissingValueHandling(TestC45ComprehensiveIntegration):
    """Test missing value handling."""

    def test_missing_values_in_numeric_features(self):
        """Test missing values in numeric features."""
        tree = C45DecisionTree()
        tree.fit(self.X_train_missing, self.y_train_missing)

        # Test prediction with missing values
        predictions = tree.predict(self.X_test_missing)
        self.assertEqual(len(predictions), len(self.X_test_missing))
        self.assertTrue(all(pred in ['Yes', 'No'] for pred in predictions))

    def test_missing_values_in_categorical_features(self):
        """Test missing values in categorical features."""
        X_cat_missing = pd.DataFrame({
            'cat_feature': ['A', 'B', np.nan, 'A', 'B', np.nan, 'C'],
            'numeric_feature': [1, 2, 3, 4, 5, 6, 7]
        })
        y_cat_missing = pd.Series(['Yes', 'No', 'Yes', 'No', 'Yes', 'No', 'Yes'])

        tree = C45DecisionTree()
        tree.fit(X_cat_missing, y_cat_missing)

        predictions = tree.predict(X_cat_missing)
        self.assertEqual(len(predictions), len(X_cat_missing))

    def test_all_missing_values_in_feature(self):
        """Test feature with all missing values."""
        X_all_missing = pd.DataFrame({
            'all_missing': [np.nan] * 10,
            'valid_feature': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        })
        y_all_missing = pd.Series(['A', 'B'] * 5)

        tree = C45DecisionTree()
        tree.fit(X_all_missing, y_all_missing)

        predictions = tree.predict(X_all_missing)
        self.assertEqual(len(predictions), len(X_all_missing))


class TestEntropyAndGainRatio(TestC45ComprehensiveIntegration):
    """Test entropy calculation and gain ratio functionality."""

    def test_entropy_calculation_edge_cases(self):
        """Test entropy calculation with edge cases."""
        tree = C45DecisionTree()

        # Test pure class (entropy = 0)
        pure_series = pd.Series(['A'] * 10)
        entropy_pure = tree._compute_entropy(pure_series)
        self.assertAlmostEqual(entropy_pure, 0.0, places=5)

        # Test balanced classes (maximum entropy)
        balanced_series = pd.Series(['A', 'B'] * 50)
        entropy_balanced = tree._compute_entropy(balanced_series)
        self.assertAlmostEqual(entropy_balanced, 1.0, places=5)

        # Test empty series
        empty_series = pd.Series([])
        entropy_empty = tree._compute_entropy(empty_series)
        self.assertEqual(entropy_empty, 0.0)

    def test_gain_ratio_vs_information_gain(self):
        """Test that gain ratio prevents bias toward high-cardinality features."""
        # Create biased dataset
        X_biased = pd.DataFrame({
            'many_values': [f'value_{i}' for i in range(100)],  # Unique values
            'few_values': ['A', 'B'] * 50,  # Binary feature
            'target_related': ['good', 'bad'] * 50  # Actually predictive
        })
        y_biased = pd.Series(['positive' if x == 'good' else 'negative'
                             for x in X_biased['target_related']])

        tree = C45DecisionTree(max_depth=3)
        tree.fit(X_biased, y_biased)

        # Get feature importance
        importance = tree.get_feature_importance()

        if importance:
            max_importance_feature = max(importance.items(), key=lambda x: x[1])[0]
            # Should not be biased toward the many-valued feature
            self.assertNotEqual(max_importance_feature, 'many_values')

    def test_gain_ratio_calculation(self):
        """Test gain ratio calculation directly."""
        tree = C45DecisionTree()

        # Create subsets for gain ratio test
        parent_entropy = 1.0
        subset1 = pd.Series(['A', 'A', 'B'])
        subset2 = pd.Series(['B', 'B', 'A'])
        subsets = [subset1, subset2]

        gain_ratio = tree._compute_gain_ratio(parent_entropy, subsets)

        # Should be a valid gain ratio
        self.assertGreaterEqual(gain_ratio, 0.0)
        self.assertLessEqual(gain_ratio, 1.0)


class TestModelPersistence(TestC45ComprehensiveIntegration):
    """Test model save/load functionality."""

    def test_model_serialization_comprehensive(self):
        """Test comprehensive model serialization."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            model_path = f.name

        try:
            # Train complex model
            tree = C45DecisionTree(
                max_depth=7,
                min_instances_pc=0.02,
                enable_pruning=True,
                confidence_level=0.3
            )
            tree.fit(self.X_train, self.y_train)
            original_predictions = tree.predict(self.X_test)

            # Save model
            tree.save_model(model_path)

            # Verify file exists and has content
            self.assertTrue(os.path.exists(model_path))
            with open(model_path, 'r') as f:
                saved_data = json.load(f)
            self.assertIn('tree', saved_data)
            self.assertIn('feature_types', saved_data)

            # Load model
            loaded_tree = C45DecisionTree()
            loaded_tree.load_model(model_path)
            loaded_predictions = loaded_tree.predict(self.X_test)

            # Verify predictions match
            self.assertEqual(original_predictions.tolist(), loaded_predictions.tolist())

            # Verify parameters preserved
            self.assertEqual(loaded_tree.max_depth, 7)
            self.assertEqual(loaded_tree.min_instances_pc, 0.02)
            self.assertEqual(loaded_tree.enable_pruning, True)
            self.assertEqual(loaded_tree.confidence_level, 0.3)

        finally:
            if os.path.exists(model_path):
                os.unlink(model_path)

    def test_model_load_nonexistent_file(self):
        """Test loading from non-existent file."""
        tree = C45DecisionTree()

        with self.assertRaises(ValueError):
            tree.load_model("/nonexistent/path/model.json")

    def test_model_save_without_training(self):
        """Test saving model without training."""
        tree = C45DecisionTree()

        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            model_path = f.name

        try:
            with self.assertRaises(ValueError):
                tree.save_model(model_path)
        finally:
            if os.path.exists(model_path):
                os.unlink(model_path)


class TestMetadataHandling(TestC45ComprehensiveIntegration):
    """Test metadata file handling."""

    def test_metadata_file_loading(self):
        """Test loading metadata from YAML file."""
        metadata_content = {
            'numeric_feature_1': {'type': 'numeric', 'min': -5, 'max': 5},
            'categorical_feature_1': {'type': 'nominal', 'values': ['A', 'B', 'C']},
            'numeric_feature_2': {'type': 'numeric'},
            'categorical_feature_2': {'type': 'nominal'}
        }

        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(metadata_content, f)
            metadata_path = f.name

        try:
            tree = C45DecisionTree()
            tree.fit(self.X_train, self.y_train, metadata_path)

            # Verify feature types were loaded from metadata
            self.assertEqual(tree.feature_types['numeric_feature_1'], DataType.NUMERIC)
            self.assertEqual(tree.feature_types['categorical_feature_1'], DataType.NOMINAL)

        finally:
            if os.path.exists(metadata_path):
                os.unlink(metadata_path)

    def test_metadata_file_missing(self):
        """Test handling of missing metadata file."""
        tree = C45DecisionTree()

        with self.assertRaises(ValueError):
            tree.fit(self.X_train, self.y_train, "/nonexistent/metadata.yaml")

    def test_invalid_metadata_format(self):
        """Test handling of invalid metadata format."""
        invalid_metadata = "invalid: yaml: content: ["

        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(invalid_metadata)
            metadata_path = f.name

        try:
            tree = C45DecisionTree()
            with self.assertRaises(ValueError):
                tree.fit(self.X_train, self.y_train, metadata_path)
        finally:
            if os.path.exists(metadata_path):
                os.unlink(metadata_path)


class TestErrorHandling(TestC45ComprehensiveIntegration):
    """Test error handling and edge cases."""

    def test_parameter_validation(self):
        """Test parameter validation."""
        # Test invalid max_depth
        with self.assertRaises(ValueError):
            C45DecisionTree(max_depth=0)

        # Test invalid min_instances_pc
        with self.assertRaises(ValueError):
            C45DecisionTree(min_instances_pc=0.0)

        with self.assertRaises(ValueError):
            C45DecisionTree(min_instances_pc=1.0)

        # Test invalid confidence_level
        with self.assertRaises(ValueError):
            C45DecisionTree(confidence_level=0.0)

        with self.assertRaises(ValueError):
            C45DecisionTree(confidence_level=1.0)

    def test_input_validation(self):
        """Test input validation."""
        tree = C45DecisionTree()

        # Test wrong input types
        with self.assertRaises(TypeError):
            tree.fit("not_a_dataframe", self.y_train)

        with self.assertRaises(TypeError):
            tree.fit(self.X_train, "not_a_series")

        # Test mismatched lengths
        with self.assertRaises(ValueError):
            tree.fit(self.X_train, self.y_train.iloc[:-5])

        # Test empty datasets
        with self.assertRaises(ValueError):
            tree.fit(pd.DataFrame(), pd.Series([]))

        # Test all-null DataFrame
        X_all_null = pd.DataFrame({
            'col1': [np.nan] * 5,
            'col2': [np.nan] * 5
        })
        y_all_null = pd.Series(['A'] * 5)

        with self.assertRaises(ValueError):
            tree.fit(X_all_null, y_all_null)

    def test_prediction_without_training(self):
        """Test prediction without training."""
        tree = C45DecisionTree()

        with self.assertRaises(ValueError):
            tree.predict(self.X_test)

    def test_prediction_input_validation(self):
        """Test prediction input validation."""
        tree = C45DecisionTree()
        tree.fit(self.X_train, self.y_train)

        # Test wrong input type
        with self.assertRaises(TypeError):
            tree.predict("not_a_dataframe")

    def test_feature_importance_without_training(self):
        """Test feature importance without training."""
        tree = C45DecisionTree()

        importance = tree.get_feature_importance()
        self.assertEqual(importance, {})

    def test_tree_info_without_training(self):
        """Test tree info without training."""
        tree = C45DecisionTree()

        info = tree.get_tree_info()
        expected_info = {
            "total_nodes": 0,
            "actual_depth": 0,
            "leaf_nodes": 0,
            "internal_nodes": 0
        }
        self.assertEqual(info, expected_info)


class TestFeatureImportance(TestC45ComprehensiveIntegration):
    """Test feature importance calculation."""

    def test_feature_importance_calculation(self):
        """Test feature importance calculation."""
        tree = C45DecisionTree(max_depth=5)
        tree.fit(self.X_train, self.y_train)

        importance = tree.get_feature_importance()

        # Should return a dictionary
        self.assertIsInstance(importance, dict)

        if importance:
            # Importance values should sum to approximately 1.0
            total_importance = sum(importance.values())
            self.assertAlmostEqual(total_importance, 1.0, places=2)

            # All importance values should be non-negative
            self.assertTrue(all(val >= 0 for val in importance.values()))

    def test_feature_importance_with_single_feature(self):
        """Test feature importance with single feature."""
        X_single_feature = pd.DataFrame({'feature': [1, 2, 3, 4, 5, 6]})
        y_single_feature = pd.Series(['A', 'A', 'B', 'B', 'A', 'B'])

        tree = C45DecisionTree()
        tree.fit(X_single_feature, y_single_feature)

        importance = tree.get_feature_importance()

        if importance:
            self.assertIn('feature', importance)
            self.assertAlmostEqual(sum(importance.values()), 1.0, places=2)

    def test_feature_importance_with_pure_classes(self):
        """Test feature importance with pure target classes."""
        tree = C45DecisionTree()
        tree.fit(self.X_pure, self.y_pure)

        importance = tree.get_feature_importance()

        # Should handle pure classes gracefully
        self.assertIsInstance(importance, dict)


class TestTreeStructure(TestC45ComprehensiveIntegration):
    """Test tree structure analysis."""

    def test_tree_structure_analysis(self):
        """Test tree structure information retrieval."""
        tree = C45DecisionTree(max_depth=5)
        tree.fit(self.X_train, self.y_train)

        info = tree.get_tree_info()

        # Check expected fields
        expected_fields = ['total_nodes', 'actual_depth', 'leaf_nodes', 'internal_nodes']
        for field in expected_fields:
            self.assertIn(field, info)

        # Basic consistency checks
        self.assertGreaterEqual(info['total_nodes'], 1)  # At least root
        self.assertEqual(info['total_nodes'], info['leaf_nodes'] + info['internal_nodes'])
        self.assertLessEqual(info['actual_depth'], 5)  # Should respect max_depth

    def test_tree_structure_with_different_configurations(self):
        """Test tree structure with various configurations."""
        configurations = [
            {'max_depth': 1, 'min_instances_pc': 0.1},
            {'max_depth': 3, 'min_instances_pc': 0.05},
            {'max_depth': 10, 'min_instances_pc': 0.01},
        ]

        for config in configurations:
            with self.subTest(config=config):
                tree = C45DecisionTree(**config)
                tree.fit(self.X_train, self.y_train)

                info = tree.get_tree_info()

                # Verify constraints
                self.assertLessEqual(info['actual_depth'], config['max_depth'])
                self.assertGreaterEqual(info['total_nodes'], 1)
                self.assertEqual(info['total_nodes'],
                               info['leaf_nodes'] + info['internal_nodes'])


class TestBenchmarkIntegration(TestC45ComprehensiveIntegration):
    """Test integration with benchmark framework."""

    def test_benchmark_wrapper_comprehensive(self):
        """Test benchmark wrapper comprehensively."""
        wrapper = BenchmarkDecisionTree(
            max_depth=5,
            min_instances_pc=0.05,
            enable_pruning=True,
            confidence_level=0.3
        )

        # Test training
        wrapper.train(self.X_train, self.y_train)
        self.assertTrue(wrapper.is_trained)
        self.assertGreater(wrapper.training_time, 0)

        # Test prediction
        predictions = wrapper.predict(self.X_test)
        self.assertEqual(len(predictions), len(self.X_test))
        self.assertGreater(wrapper.prediction_time, 0)

        # Test model info
        info = wrapper.get_model_info()
        self.assertEqual(info['model_type'], 'C4.5 Decision Tree')
        self.assertTrue(info['is_trained'])
        self.assertIn('parameters', info)
        self.assertIn('tree_info', info)

        # Test feature importance
        importance = wrapper.get_feature_importance()
        self.assertIsInstance(importance, dict)

        # Test save/load
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            model_path = f.name

        try:
            wrapper.save(model_path)
            self.assertTrue(os.path.exists(model_path))

            # Test loading
            new_wrapper = BenchmarkDecisionTree()
            new_wrapper.load(model_path)
            self.assertTrue(new_wrapper.is_trained)

        finally:
            if os.path.exists(model_path):
                os.unlink(model_path)

    def test_metrics_integration_comprehensive(self):
        """Test metrics calculation comprehensively."""
        tree = C45DecisionTree(max_depth=5)
        tree.fit(self.X_train, self.y_train)
        predictions = tree.predict(self.X_test)

        # Convert to lists for metrics calculation
        y_true = self.y_test.tolist()
        y_pred = predictions.tolist()

        # Calculate metrics
        metrics = calculate_all_metrics(y_true, y_pred, 0.1, 0.05)

        # Check all expected metrics
        expected_metrics = [
            'accuracy', 'precision', 'recall', 'f1_score',
            'confusion_matrix', 'training_time', 'prediction_time', 'memory_usage'
        ]

        for metric in expected_metrics:
            with self.subTest(metric=metric):
                self.assertIn(metric, metrics)

        # Check metric ranges
        self.assertGreaterEqual(metrics['accuracy'], 0.0)
        self.assertLessEqual(metrics['accuracy'], 1.0)
        self.assertGreaterEqual(metrics['precision'], 0.0)
        self.assertLessEqual(metrics['precision'], 1.0)
        self.assertGreaterEqual(metrics['recall'], 0.0)
        self.assertLessEqual(metrics['recall'], 1.0)
        self.assertGreaterEqual(metrics['f1_score'], 0.0)
        self.assertLessEqual(metrics['f1_score'], 1.0)


class TestUtilityFunctions(TestC45ComprehensiveIntegration):
    """Test utility functions."""

    def test_create_directory(self):
        """Test directory creation utility."""
        with tempfile.TemporaryDirectory() as temp_dir:
            test_dir = os.path.join(temp_dir, "test_create_dir")

            create_directory(test_dir)
            self.assertTrue(os.path.exists(test_dir))
            self.assertTrue(os.path.isdir(test_dir))

    def test_format_results_for_display(self):
        """Test result formatting utility."""
        raw_results = {
            'accuracy': 0.8567891234,
            'precision': 0.7891234567,
            'training_time': 1.2345678,
            'memory_usage': 125.6789,
            'confusion_matrix': {'tp': 10, 'tn': 8, 'fp': 2, 'fn': 3},
            'extra_field': 'test'
        }

        formatted = format_results_for_display(raw_results)

        # Check formatting
        self.assertEqual(formatted['accuracy'], 0.8568)
        self.assertEqual(formatted['precision'], 0.7891)
        self.assertEqual(formatted['training_time'], 1.23)
        self.assertEqual(formatted['memory_usage'], 125.7)
        self.assertIn('confusion_matrix', formatted)
        self.assertEqual(formatted['extra_field'], 'test')

    def test_validate_dataset_files(self):
        """Test dataset file validation."""
        # Create temporary files
        with tempfile.TemporaryDirectory() as temp_dir:
            train_file = os.path.join(temp_dir, "train.csv")
            predict_file = os.path.join(temp_dir, "predict.csv")
            actual_file = os.path.join(temp_dir, "actual.csv")

            # Create valid files
            pd.DataFrame({'a': [1, 2], 'b': [3, 4]}).to_csv(train_file, index=False)
            pd.DataFrame({'a': [5, 6]}).to_csv(predict_file, index=False)
            pd.DataFrame({'b': [7, 8]}).to_csv(actual_file, index=False)

            # Test validation
            self.assertTrue(validate_dataset_files(train_file, predict_file, actual_file))

            # Test with missing file
            os.remove(actual_file)
            self.assertFalse(validate_dataset_files(train_file, predict_file, actual_file))

    def test_load_and_validate_data(self):
        """Test data loading and validation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            train_file = os.path.join(temp_dir, "train.csv")
            predict_file = os.path.join(temp_dir, "predict.csv")
            actual_file = os.path.join(temp_dir, "actual.csv")

            # Create test data
            train_data = pd.DataFrame({'feature1': [1, 2, 3], 'target': ['A', 'B', 'A']})
            predict_data = pd.DataFrame({'feature1': [4, 5]})
            actual_data = pd.DataFrame({'target': ['B', 'A']})

            train_data.to_csv(train_file, index=False)
            predict_data.to_csv(predict_file, index=False)
            actual_data.to_csv(actual_file, index=False)

            # Test loading
            train_loaded, predict_loaded, actual_loaded = load_and_validate_data(
                train_file, predict_file, actual_file, 'target'
            )

            self.assertIsNotNone(train_loaded)
            self.assertIsNotNone(predict_loaded)
            self.assertIsNotNone(actual_loaded)
            self.assertEqual(len(train_loaded), 3)
            self.assertEqual(len(predict_loaded), 2)
            self.assertEqual(len(actual_loaded), 2)


class TestAdvancedScenarios(TestC45ComprehensiveIntegration):
    """Test advanced and edge case scenarios."""

    def test_multiclass_classification(self):
        """Test multiclass classification."""
        # Create multiclass dataset
        X_multi = pd.DataFrame({
            'feature1': np.random.normal(0, 1, 150),
            'feature2': np.random.uniform(-1, 1, 150),
            'category': np.random.choice(['A', 'B', 'C'], 150)
        })

        y_multi = pd.Series([
            'Class1' if row['feature1'] > 0.5 else
            'Class2' if row['feature1'] < -0.5 else
            'Class3'
            for _, row in X_multi.iterrows()
        ])

        tree = C45DecisionTree()
        tree.fit(X_multi, y_multi)

        predictions = tree.predict(X_multi)
        unique_predictions = set(predictions)

        # Should predict all possible classes
        self.assertTrue(unique_predictions.issubset({'Class1', 'Class2', 'Class3'}))
        self.assertEqual(len(predictions), len(X_multi))

    def test_imbalanced_dataset(self):
        """Test handling of imbalanced datasets."""
        # Create highly imbalanced dataset
        X_imbalanced = pd.DataFrame({
            'feature1': np.random.normal(0, 1, 100),
            'feature2': np.random.choice(['A', 'B'], 100)
        })

        # 95% one class, 5% another
        y_imbalanced = pd.Series(['Majority'] * 95 + ['Minority'] * 5)

        tree = C45DecisionTree()
        tree.fit(X_imbalanced, y_imbalanced)

        predictions = tree.predict(X_imbalanced)
        self.assertEqual(len(predictions), len(X_imbalanced))

        # Should handle imbalanced data without errors
        unique_predictions = set(predictions)
        self.assertTrue(unique_predictions.issubset({'Majority', 'Minority'}))

    def test_constant_features(self):
        """Test handling of constant features."""
        X_constant = pd.DataFrame({
            'constant_feature': [1] * 20,
            'varying_feature': list(range(20)),
            'constant_categorical': ['SAME'] * 20
        })
        y_constant = pd.Series(['A', 'B'] * 10)

        tree = C45DecisionTree()
        tree.fit(X_constant, y_constant)

        predictions = tree.predict(X_constant)
        self.assertEqual(len(predictions), len(X_constant))

    def test_large_dataset_performance(self):
        """Test performance with larger datasets."""
        # Create larger dataset
        n_large = 1000
        X_large = pd.DataFrame({
            'feature1': np.random.normal(0, 1, n_large),
            'feature2': np.random.uniform(-5, 5, n_large),
            'feature3': np.random.choice(['A', 'B', 'C', 'D'], n_large),
            'feature4': np.random.exponential(2, n_large)
        })

        y_large = pd.Series([
            'Yes' if (row['feature1'] > 0 and row['feature3'] in ['A', 'B']) else 'No'
            for _, row in X_large.iterrows()
        ])

        tree = C45DecisionTree(max_depth=8)

        # Should handle larger datasets efficiently
        import time
        start_time = time.time()
        tree.fit(X_large, y_large)
        training_time = time.time() - start_time

        # Training should complete in reasonable time (less than 5 seconds)
        self.assertLess(training_time, 5.0)

        start_time = time.time()
        predictions = tree.predict(X_large)
        prediction_time = time.time() - start_time

        # Prediction should be fast (less than 1 second)
        self.assertLess(prediction_time, 1.0)
        self.assertEqual(len(predictions), n_large)


class TestBackwardCompatibility(TestC45ComprehensiveIntegration):
    """Test backward compatibility."""

    def test_decision_tree_alias(self):
        """Test DecisionTree alias backward compatibility."""
        tree = DecisionTree(max_depth=5)
        tree.fit(self.X_train, self.y_train)
        predictions = tree.predict(self.X_test)

        self.assertEqual(len(predictions), len(self.X_test))
        self.assertIsInstance(tree, C45DecisionTree)

    def test_old_parameter_names(self):
        """Test compatibility with parameter naming."""
        # Test various parameter combinations
        tree1 = C45DecisionTree(max_depth=3, min_instances_pc=0.05)
        tree2 = C45DecisionTree(max_depth=5, enable_pruning=False)
        tree3 = C45DecisionTree(confidence_level=0.4, enable_pruning=True)

        for tree in [tree1, tree2, tree3]:
            with self.subTest(tree=tree):
                tree.fit(self.X_train, self.y_train)
                predictions = tree.predict(self.X_test)
                self.assertEqual(len(predictions), len(self.X_test))


class TestSpecificBugFixes(TestC45ComprehensiveIntegration):
    """Test specific bug fixes."""

    def test_predict_single_bug_fix(self):
        """Test that the predict_single bug is fixed."""
        # Create simple training data
        X_train_simple = pd.DataFrame({
            'feature1': [1, 2, 3, 4],
            'feature2': ['A', 'B', 'A', 'B']
        })
        y_train_simple = pd.Series(['Yes', 'No', 'Yes', 'No'])

        tree = C45DecisionTree(max_depth=2)
        tree.fit(X_train_simple, y_train_simple)

        # Test case that would crash with the old bug
        test_sample = pd.DataFrame({
            'feature1': [999],  # Value not seen in training
            'feature2': ['C']   # Value not seen in training
        })

        # This should NOT crash
        predictions = tree.predict(test_sample)

        # Should return a valid prediction
        self.assertEqual(len(predictions), 1)
        self.assertIn(predictions.iloc[0], ['Yes', 'No'])

    def test_node_creation_with_pydantic(self):
        """Test proper node creation with Pydantic models."""
        # Test LeafNode creation
        leaf = LeafNode.create("TestClass")
        self.assertEqual(leaf.majority_class, "TestClass")
        self.assertTrue(leaf.is_leaf())

        # Test BranchNode creation
        feature = NumericFeature("test_feature", 1.5)
        branches = {"<=": leaf, ">": LeafNode.create("OtherClass")}
        branch = BranchNode.create(feature, "MajorityClass", branches)

        self.assertEqual(branch.majority_class, "MajorityClass")
        self.assertFalse(branch.is_leaf())
        self.assertEqual(len(branch.branches), 2)

    def test_entropy_optimization(self):
        """Test entropy calculation optimization."""
        tree = C45DecisionTree()

        # Test with different data types
        series_list = [1, 2, 1, 2, 1, 2]
        series_pd = pd.Series(['A', 'B', 'A', 'B', 'A', 'B'])
        series_numpy = np.array(['X', 'Y', 'X', 'Y', 'X', 'Y'])

        entropy1 = tree._compute_entropy(series_list)
        entropy2 = tree._compute_entropy(series_pd)
        entropy3 = tree._compute_entropy(series_numpy)

        # All should compute entropy correctly
        self.assertAlmostEqual(entropy1, 1.0, places=5)
        self.assertAlmostEqual(entropy2, 1.0, places=5)
        self.assertAlmostEqual(entropy3, 1.0, places=5)


def run_comprehensive_tests():
    """Run all comprehensive tests."""
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add all test classes
    test_classes = [
        TestBasicFunctionality,
        TestPruningFunctionality,
        TestDataTypeHandling,
        TestMissingValueHandling,
        TestEntropyAndGainRatio,
        TestModelPersistence,
        TestMetadataHandling,
        TestErrorHandling,
        TestFeatureImportance,
        TestTreeStructure,
        TestBenchmarkIntegration,
        TestUtilityFunctions,
        TestAdvancedScenarios,
        TestBackwardCompatibility,
        TestSpecificBugFixes
    ]

    for test_class in test_classes:
        suite.addTests(loader.loadTestsFromTestCase(test_class))

    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    print(f"\n{'='*60}")
    print("COMPREHENSIVE TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")

    if result.failures:
        print(f"\nFailures:")
        for test, traceback in result.failures:
            # FIXED: Safe traceback extraction
            try:
                error_msg = traceback.split('AssertionError: ')[-1].split('\n')[0]
                print(f"  - {test}: {error_msg}")
            except (IndexError, AttributeError):
                print(f"  - {test}: {traceback[:100]}...")

    if result.errors:
        print(f"\nErrors:")
        for test, traceback in result.errors:
            # FIXED: Safe traceback extraction
            try:
                error_lines = traceback.split('\n')
                # Find the most relevant error line
                error_msg = next((line for line in error_lines if line.strip() and not line.startswith('  ')),
                               error_lines[-2] if len(error_lines) > 1 else str(traceback))
                print(f"  - {test}: {error_msg}")
            except (IndexError, AttributeError):
                print(f"  - {test}: {traceback[:100]}...")

    return result.wasSuccessful()


if __name__ == "__main__":
    print("Running Comprehensive C4.5 Decision Tree Integration Tests")
    print("Target: 85%+ Test Coverage")
    print("=" * 70)

    success = run_comprehensive_tests()

    if success:
        print("\n🎉 All tests passed! Comprehensive coverage achieved.")
    else:
        print("\n❌ Some tests failed. Please review the output above.")

    print("\nComprehensive integration tests completed!")
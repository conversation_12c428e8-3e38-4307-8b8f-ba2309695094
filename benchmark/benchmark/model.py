"""
Decision Tree model wrapper for benchmarking.

This module provides a wrapper around the C4.5 Decision Tree implementation
for consistent benchmarking across different datasets.
"""
import json
import logging
import os
import sys
import time
from typing import Any, Dict, List, Optional

import pandas as pd

# Import the custom C4.5 decision tree implementation
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + '/..')
from dt import C45DecisionTree

# Set up logging
logger = logging.getLogger(__name__)


class DecisionTree:
    """
    Wrapper class for the C4.5 Decision Tree implementation.

    Provides consistent interface for benchmarking across different datasets
    while maintaining compatibility with the existing benchmarking framework.

    Args:
        max_depth: Maximum depth of the decision tree
        min_instances_pc: Minimum percentage of instances required to split a node
        enable_pruning: Whether to enable post-pruning (default: True)
        confidence_level: Confidence level for pruning (default: 0.25)
    """

    def __init__(self, max_depth: int = 5, min_instances_pc: float = 0.01,
                 enable_pruning: bool = True, confidence_level: float = 0.25):
        """
        Initialize the decision tree wrapper.

        Args:
            max_depth: Maximum depth of the tree (default: 5)
            min_instances_pc: Minimum percentage of instances required to split (default: 0.01)
            enable_pruning: Whether to enable post-pruning for overfitting prevention
            confidence_level: Confidence level for pruning decisions
        """
        if max_depth < 1:
            raise ValueError("max_depth must be at least 1")
        if not 0 < min_instances_pc < 1:
            raise ValueError("min_instances_pc must be between 0 and 1")
        if not 0 < confidence_level < 1:
            raise ValueError("confidence_level must be between 0 and 1")

        self.model = C45DecisionTree(
            max_depth=max_depth,
            min_instances_pc=min_instances_pc,
            enable_pruning=enable_pruning,
            confidence_level=confidence_level
        )
        self.training_time = 0.0
        self.prediction_time = 0.0
        self.is_trained = False

        logger.info(f"Initialized DecisionTree wrapper with max_depth={max_depth}, "
                   f"min_instances_pc={min_instances_pc}, pruning={enable_pruning}")

    def train(self, X: pd.DataFrame, y: pd.Series, metadata_file: Optional[str] = None) -> None:
        """
        Train the decision tree model.

        Args:
            X: Training features with shape (n_samples, n_features)
            y: Training targets with shape (n_samples,)
            metadata_file: Optional path to metadata YAML file for feature type specification

        Raises:
            ValueError: If training data is invalid
            TypeError: If inputs have wrong types
        """
        if not isinstance(X, pd.DataFrame):
            raise TypeError("X must be a pandas DataFrame")
        if not isinstance(y, pd.Series):
            raise TypeError("y must be a pandas Series")

        start_time = time.time()

        try:
            logger.info(f"Training decision tree on {len(X)} samples with {len(X.columns)} features")

            # Check if indices are problematic (non-sequential or misaligned)
            if not X.index.equals(y.index) or not X.index.is_monotonic_increasing:
                X_clean = X.reset_index(drop=True)
                y_clean = y.reset_index(drop=True)
            else:
                # Use original data without copying
                X_clean = X
                y_clean = y

            # Train the model using the C4.5 implementation
            self.model.fit(X_clean, y_clean, metadata_file)
            self.is_trained = True

            logger.info("Decision tree training completed successfully")

        except Exception as e:
            logger.error(f"Training failed: {e}")
            raise ValueError(f"Training failed: {e}")
        finally:
            self.training_time = time.time() - start_time
            logger.info(f"Training took {self.training_time:.4f} seconds")

    def predict(self, X: pd.DataFrame) -> List[Any]:
        """
        Make predictions using the trained model.

        Args:
            X: Features to predict on with shape (n_samples, n_features)

        Returns:
            List[Any]: List of predictions

        Raises:
            ValueError: If model is not trained
            TypeError: If input has wrong type
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")

        if not isinstance(X, pd.DataFrame):
            raise TypeError("X must be a pandas DataFrame")

        start_time = time.time()

        try:
            logger.info(f"Making predictions on {len(X)} samples")

            # Reset index to ensure consistency
            if not X.index.is_monotonic_increasing:
                X_clean = X.reset_index(drop=True)
            else:
                X_clean = X
            # Make predictions using the C4.5 model
            predictions_series = self.model.predict(X_clean)

            # Convert to list for compatibility with benchmarking framework
            if hasattr(predictions_series, 'tolist'):
                # Only convert to list if absolutely necessary
                # Most pandas operations are faster with Series
                predictions = predictions_series.tolist()
            else:
                predictions = list(predictions_series)

            logger.info(f"Generated {len(predictions)} predictions")
            return predictions

        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            raise ValueError(f"Prediction failed: {e}")
        finally:
            self.prediction_time = time.time() - start_time
            logger.info(f"Prediction took {self.prediction_time:.4f} seconds")

    def save(self, filepath: str) -> None:
        """
        Save the trained model to disk.

        Args:
            filepath: Path to save the model file

        Raises:
            ValueError: If model is not trained or save fails
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before saving")

        try:
            logger.info(f"Saving model to {filepath}")

            # Use the model's built-in save method
            self.model.save_model(filepath)

            # Save timing information separately for benchmarking
            timing_info = {
                'training_time': self.training_time,
                'prediction_time': self.prediction_time,
                'is_trained': self.is_trained,
                'model_type': 'C4.5 Decision Tree'
            }

            timing_filepath = filepath.replace('.json', '_timing.json')
            with open(timing_filepath, 'w') as f:
                json.dump(timing_info, f, indent=2)

            logger.info(f"Model and timing info saved successfully")

        except Exception as e:
            logger.error(f"Error saving model: {e}")
            raise ValueError(f"Error saving model: {e}")

    def load(self, filepath: str) -> None:
        """
        Load a trained model from disk.

        Args:
            filepath: Path to load the model from

        Raises:
            ValueError: If model file is invalid or load fails
        """
        try:
            logger.info(f"Loading model from {filepath}")

            # Load the model using the built-in load method
            self.model.load_model(filepath)
            self.is_trained = True

            # Load timing information if available
            timing_filepath = filepath.replace('.json', '_timing.json')
            if os.path.exists(timing_filepath):
                with open(timing_filepath, 'r') as f:
                    timing_info = json.load(f)
                    self.training_time = timing_info.get('training_time', 0.0)
                    self.prediction_time = timing_info.get('prediction_time', 0.0)

            logger.info("Model loaded successfully")

        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise ValueError(f"Error loading model: {e}")

    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance from the trained model.

        Returns:
            Dict[str, float]: Dictionary mapping feature names to importance scores

        Raises:
            ValueError: If model is not trained
        """
        if not self.is_trained:
            raise ValueError("Model must be trained to get feature importance")

        try:
            return self.model.get_feature_importance()
        except Exception as e:
            logger.warning(f"Could not compute feature importance: {e}")
            return {}

    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the trained model.

        Returns:
            Dict[str, Any]: Dictionary containing model information
        """
        info = {
            'model_type': 'C4.5 Decision Tree',
            'is_trained': self.is_trained,
            'training_time': self.training_time,
            'prediction_time': self.prediction_time,
            'parameters': {
                'max_depth': self.model.max_depth,
                'min_instances_pc': self.model.min_instances_pc,
                'enable_pruning': self.model.enable_pruning,
                'confidence_level': self.model.confidence_level
            }
        }

        if self.is_trained:
            # Get feature types information
            info['feature_types'] = {k: v.value for k, v in self.model.feature_types.items()}
            info['target_type'] = self.model.target_type

            # Get tree structure information
            try:
                tree_info = self.model.get_tree_info()
                info['tree_info'] = tree_info
            except Exception as e:
                logger.warning(f"Could not get tree info: {e}")
                info['tree_info'] = {"error": str(e)}

        return info

    def print_tree(self) -> None:
        """
        Print the decision tree structure and information.

        Provides detailed information about the trained model including
        tree structure, performance metrics, and feature information.
        """
        if not self.is_trained:
            print("Model must be trained before printing tree structure")
            return

        print("=" * 60)
        print("DECISION TREE INFORMATION")
        print("=" * 60)

        model_info = self.get_model_info()

        print(f"Model Type: {model_info['model_type']}")
        print(f"Max Depth: {model_info['parameters']['max_depth']}")
        print(f"Min Instances %: {model_info['parameters']['min_instances_pc']}")
        print(f"Pruning Enabled: {model_info['parameters']['enable_pruning']}")
        print(f"Confidence Level: {model_info['parameters']['confidence_level']}")
        print(f"Target Type: {model_info.get('target_type', 'unknown')}")

        if 'tree_info' in model_info and 'error' not in model_info['tree_info']:
            tree_info = model_info['tree_info']
            print(f"\nTree Structure:")
            print(f"  Total Nodes: {tree_info.get('total_nodes', 'unknown')}")
            print(f"  Leaf Nodes: {tree_info.get('leaf_nodes', 'unknown')}")
            print(f"  Internal Nodes: {tree_info.get('internal_nodes', 'unknown')}")
            print(f"  Actual Depth: {tree_info.get('actual_depth', 'unknown')}")

        print(f"\nPerformance:")
        print(f"  Training Time: {model_info['training_time']:.4f}s")
        print(f"  Prediction Time: {model_info['prediction_time']:.4f}s")

        if model_info.get('feature_types'):
            print(f"\nFeature Types:")
            for feature, ftype in sorted(model_info['feature_types'].items()):
                print(f"  - {feature}: {ftype}")

        # Print feature importance if available
        try:
            importance = self.get_feature_importance()
            if importance:
                print(f"\nFeature Importance (Top 10):")
                sorted_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)[:10]
                for feature, score in sorted_features:
                    print(f"  - {feature}: {score:.4f}")
        except Exception as e:
            logger.debug(f"Could not display feature importance: {e}")

        print("=" * 60)
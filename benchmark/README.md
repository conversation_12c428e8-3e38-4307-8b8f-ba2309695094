# C4.5 Decision Tree Benchmarking Framework

## 1. Introduction

This directory contains a comprehensive benchmarking framework for evaluating **complete C4.5 decision tree implementations** across multiple standardized datasets. The framework provides a full pipeline for training and evaluating C4.5 decision tree models with **all core features** including gain ratio calculation, post-pruning, and advanced configuration options.

### Key Features

The benchmarking system includes:
- **Complete C4.5 Algorithm**: Gain ratio calculation (not just information gain), post-pruning, proper continuous attribute handling
- **8 Standard Datasets**: Preprocessed and ready-to-use classification datasets
- **Advanced Configuration**: Support for pruning control, confidence levels, and feature type metadata
- **Comprehensive Analysis**: Performance metrics, feature importance, tree structure analysis
- **Comparison Tools**: Side-by-side comparison with and without pruning
- **Production Ready**: Professional logging, error handling, and validation

### C4.5 Algorithm Features

Our implementation includes all essential C4.5 features:
-  **Gain Ratio**: Prevents bias toward features with many values (improvement over ID3)
-  **Post-Pruning**: Reduced error pruning to prevent overfitting
-  **Continuous Attributes**: Proper handling without arbitrary limits
-  **Missing Values**: Intelligent handling during tree construction and prediction
-  **Categorical Features**: Native support for nominal attributes
-  **Performance Optimization**: Caching and vectorized computations

## 2. Setup

### Prerequisites
- Python 3.12 or higher
- Poetry

### Directory Structure

```
.
├── benchmark/                     # Core benchmark module
│   ├── __init__.py
│   ├── metrics.py                 # Performance metrics calculations
│   ├── model.py                   # C4.5 decision tree model wrapper
│   └── utils.py                   # Helper utilities
├── data/                          # Preprocessed datasets (auto-downloaded)
│   ├── bank_train.csv             # NOTE: data/ directory and contents are
│   ├── bank_predict.csv           # created and downloaded automatically.
│   ├── bank_actual.csv            # Do not check data/ into git.
│   ├── bank_metadata.yaml
│   └── [other datasets...]
├── data_preparation_scripts/      # Data preparation utilities
│   ├── download_datasets.py       # Downloads raw datasets
│   ├── preprocess_all.py          # Preprocesses all datasets
│   ├── preprocess_*.py            # Dataset-specific preprocessing
│   └── preprocessing_utils.py     # Common preprocessing functions
├── dt.py                          # Core C4.5 decision tree implementation
├── benchmark.yaml                 # Configuration file
├── pyproject.toml                 # Poetry configuration
├── README.md                      # This file
└── run_benchmark.py               # Main benchmarking entry point
```

## 3. Usage

### Quick Start

Run the complete benchmarking process with default settings:

```bash
poetry run python3 run_benchmark.py
```

### Command-Line Options

#### Core Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `--config` | str | `benchmark.yaml` | Path to benchmark configuration file |
| `--datasets` | list | all | Specific datasets to benchmark |
| `--download-only` | flag | False | Only download datasets without benchmarking |
| `--force-download` | flag | False | Force download even if files exist |
| `--output` | str | `results` | Directory to save benchmark results |
| `--verbose` | flag | False | Enable verbose output and debug logging |
| `--show-confusion-matrix` | flag | False | Display confusion matrices |

#### C4.5 Algorithm Parameters

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `--max-depth` | int | 10 | Maximum depth for decision tree |
| `--min-instances-pc` | float | 0.01 | Minimum percentage of instances to trigger branching |
| `--enable-pruning` | flag | True | Enable post-pruning to prevent overfitting |
| `--disable-pruning` | flag | False | Disable post-pruning (overrides --enable-pruning) |
| `--confidence-level` | float | 0.25 | Confidence level for pruning decisions (0.0-1.0) |
| `--use-metadata` | flag | False | Use metadata files for feature type specification |

#### Analysis Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `--feature-importance` | flag | False | Display feature importance rankings |
| `--tree-stats` | flag | False | Display detailed tree structure statistics |
| `--compare-pruning` | flag | False | Compare results with and without pruning |

### Usage Examples

#### Basic Benchmarking
```bash
# Default C4.5 with pruning enabled
poetry run python3 run_benchmark.py

# Verbose output with detailed analysis
poetry run python3 run_benchmark.py --verbose --tree-stats --feature-importance
```

#### Algorithm Configuration
```bash
# Deeper trees with different pruning settings
poetry run python3 run_benchmark.py --max-depth 15 --confidence-level 0.1

# Disable pruning to see overfitting effects
poetry run python3 run_benchmark.py --disable-pruning

# Compare with and without pruning side-by-side
poetry run python3 run_benchmark.py --compare-pruning
```

#### Dataset-Specific Analysis
```bash
# Benchmark specific datasets with detailed analysis
poetry run python3 run_benchmark.py --datasets bank credit_card hotel --show-confusion-matrix --feature-importance

# Use metadata files for precise feature typing
poetry run python3 run_benchmark.py --use-metadata --tree-stats
```

#### Advanced Analysis
```bash
# Comprehensive analysis with all features
poetry run python3 run_benchmark.py \
    --datasets bank telecom student \
    --max-depth 12 \
    --confidence-level 0.15 \
    --compare-pruning \
    --feature-importance \
    --tree-stats \
    --show-confusion-matrix \
    --verbose
```

#### Production Benchmarking
```bash
# Full benchmark suite for production evaluation
poetry run python3 run_benchmark.py \
    --output production_results/ \
    --enable-pruning \
    --confidence-level 0.25 \
    --use-metadata \
    --feature-importance \
    --verbose
```

### Direct C4.5 Usage

You can also use the C4.5 implementation directly:

```bash
# Train a model
python3 dt.py -c train -i data/bank_train.csv -t y -o bank_model.json --metadata data/bank_metadata.yaml

# Make predictions
python3 dt.py -c predict -i data/bank_predict.csv -m bank_model.json -o predictions.csv

# With verbose logging
python3 dt.py -c train -i data/bank_train.csv -t y -o bank_model.json --verbose
```

## 4. Understanding the Results

### Performance Metrics

The framework provides comprehensive evaluation metrics:

- **Accuracy**: Overall prediction accuracy
- **Precision**: Positive predictive value
- **Recall**: Sensitivity/true positive rate
- **F1-Score**: Harmonic mean of precision and recall
- **Confusion Matrix**: Detailed classification breakdown
- **Training Time**: Time to build the decision tree
- **Prediction Time**: Time to make predictions
- **Memory Usage**: Peak memory consumption

### Tree Structure Analysis

When using `--tree-stats`, you'll see:

- **Total Nodes**: Number of nodes in the tree
- **Leaf Nodes**: Terminal nodes with predictions
- **Internal Nodes**: Decision nodes with splits
- **Actual Depth**: Maximum depth reached
- **Feature Types**: Categorical vs. numeric features

### Feature Importance

With `--feature-importance`, features are ranked by their contribution to information gain throughout the tree, helping identify the most predictive attributes.

### Pruning Comparison

Using `--compare-pruning` shows the effect of post-pruning:

- **Accuracy Changes**: How pruning affects prediction accuracy
- **Tree Size Reduction**: Number of nodes removed
- **Training Time Impact**: Computational cost of pruning

## 5. Data Preparation

The datasets are already preprocessed and ready to use. If you need to prepare data from scratch:

### Download Raw Datasets
```bash
poetry run python3 data_preparation_scripts/download_datasets.py
```

### Preprocess All Datasets
```bash
poetry run python3 data_preparation_scripts/preprocess_all.py
poetry run python3 data_preparation_scripts/preprocess_all.py --random_state 123
```

## 6. Available Datasets

The framework includes 8 diverse classification datasets:

| Dataset | Domain | Samples | Features | Target | Description |
|---------|---------|---------|----------|---------|-------------|
| **bank** | Finance | 45,211 | 16 | Binary | Term deposit subscription prediction |
| **credit_card** | Finance | 30,000 | 23 | Binary | Credit card default prediction |
| **home_loan** | Finance | 614 | 12 | Binary | Home loan approval prediction |
| **hotel** | Business | 36,275 | 17 | Binary | Hotel booking cancellation prediction |
| **job_placement** | HR | 215 | 13 | Binary | Job placement success prediction |
| **maintenance** | Industrial | 10,000 | 9 | Binary | Predictive maintenance classification |
| **student** | Education | 4,424 | 35 | Binary | Student success prediction |
| **telecom** | Business | 7,043 | 21 | Binary | Customer churn prediction |

Each dataset includes:
- **Training data** (`*_train.csv`): For model training
- **Prediction data** (`*_predict.csv`): Features without target
- **Actual labels** (`*_actual.csv`): True labels for evaluation
- **Metadata** (`*_metadata.yaml`): Feature type specifications

## 7. Advanced Configuration

### Metadata Files

Metadata files specify feature types for optimal algorithm performance:

```yaml
# example_metadata.yaml
age:
  type: numeric
  min: 18
  max: 95

education:
  type: nominal
  values: ["primary", "secondary", "tertiary", "unknown"]

balance:
  type: numeric
```

### Custom Configuration

You can create custom benchmark configurations:

```yaml
# custom_benchmark.yaml
datasets:
  my_dataset:
    name: "My Custom Dataset"
    description: "Custom classification problem"
    target_column: "target"
    source: "path/to/source.csv"
    # ... other paths

benchmark_settings:
  algorithms:
    c45_conservative:
      max_depth: 8
      confidence_level: 0.35
      enable_pruning: true
    c45_aggressive:
      max_depth: 20
      confidence_level: 0.1
      enable_pruning: false
```

## 8. Performance Optimization

The C4.5 implementation includes several optimizations:

- **Entropy Caching**: Avoid recomputing identical entropy calculations
- **Vectorized Operations**: Use NumPy for mathematical computations
- **Early Termination**: Stop splitting when gain ratio is very high
- **Memory Efficient**: Use `__slots__` and optimized data structures
- **Batch Prediction**: Process multiple samples efficiently

## 9. Troubleshooting

### Common Issues

**"Model must be trained before making predictions"**
- Ensure you call `train()` before `predict()`

**"Target column not found in dataset"**
- Check the target column name in your dataset
- Verify the column exists in your CSV file

**"Metadata file not found"**
- Use `--use-metadata` only when metadata files exist
- Check that `*_metadata.yaml` files are present

**Memory issues with large datasets**
- Reduce `--max-depth` for very large datasets
- Increase `--min-instances-pc` to create smaller trees
- Consider using `--disable-pruning` for faster training

### Debug Mode

Enable verbose logging for detailed debugging:

```bash
poetry run python3 run_benchmark.py --verbose
```

### Performance Issues

For slow performance:
- Reduce maximum depth: `--max-depth 5`
- Increase minimum instances: `--min-instances-pc 0.05`
- Disable pruning temporarily: `--disable-pruning`

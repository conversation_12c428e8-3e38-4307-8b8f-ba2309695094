#!/usr/bin/env python3
"""
Main entry point for the C4.5 Decision Tree benchmarking process.

This script provides comprehensive benchmarking of the C4.5 decision tree implementation
across multiple datasets with support for all C4.5 features including gain ratio,
post-pruning, and advanced configuration options.
"""
import argparse
import logging
import os
import subprocess
import sys
from typing import Dict, List, Optional

import pandas as pd
import yaml
from tabulate import tabulate

from benchmark.metrics import calculate_all_metrics, print_confusion_matrix
from benchmark.model import DecisionTree
from benchmark.utils import (
    check_data_quality,
    create_directory,
    format_results_for_display,
    load_and_validate_data,
    print_benchmark_summary,
    setup_logging
)

# Set up logging
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """
    Parse command line arguments with support for all C4.5 features.

    Returns:
        argparse.Namespace: Parsed command line arguments
    """
    parser = argparse.ArgumentParser(
        description="Run comprehensive C4.5 Decision Tree benchmarking with gain ratio and pruning",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # Core arguments
    parser.add_argument("--config", type=str, default="benchmark.yaml",
                        help="Path to benchmark configuration file")

    parser.add_argument("--datasets", type=str, nargs="+",
                        help="Specific datasets to benchmark (default: all)")

    parser.add_argument("--download-only", action="store_true",
                        help="Only download datasets without running benchmarks")

    parser.add_argument("--force-download", action="store_true",
                        help="Force download datasets even if they exist locally")

    parser.add_argument("--output", type=str, default="results",
                        help="Directory to save benchmark results")

    parser.add_argument("--verbose", action="store_true",
                        help="Enable verbose output and debug logging")

    parser.add_argument("--show-confusion-matrix", action="store_true",
                        help="Display confusion matrices for detailed analysis")

    # C4.5 Algorithm Parameters
    algo_group = parser.add_argument_group("C4.5 Algorithm Parameters")

    algo_group.add_argument("--max-depth", type=int, default=10,
                           help="Maximum depth for decision tree")

    algo_group.add_argument("--min-instances-pc", type=float, default=0.01,
                           help="Minimum percentage of instances to trigger branching")

    algo_group.add_argument("--enable-pruning", action="store_true", default=True,
                           help="Enable post-pruning to prevent overfitting")

    algo_group.add_argument("--disable-pruning", action="store_true",
                           help="Disable post-pruning (overrides --enable-pruning)")

    algo_group.add_argument("--confidence-level", type=float, default=0.25,
                           help="Confidence level for pruning decisions (0.0-1.0)")

    algo_group.add_argument("--use-metadata", action="store_true",
                           help="Use metadata files for feature type specification")

    # Analysis options
    analysis_group = parser.add_argument_group("Analysis Options")

    analysis_group.add_argument("--feature-importance", action="store_true",
                               help="Display feature importance rankings")

    analysis_group.add_argument("--tree-stats", action="store_true",
                               help="Display detailed tree structure statistics")

    analysis_group.add_argument("--compare-pruning", action="store_true",
                               help="Compare results with and without pruning")

    return parser.parse_args()


def validate_args(args: argparse.Namespace) -> None:
    """
    Validate command line arguments.

    Args:
        args: Parsed command line arguments

    Raises:
        ValueError: If arguments are invalid
    """
    if args.max_depth < 1:
        raise ValueError("max-depth must be at least 1")

    if not 0 < args.min_instances_pc < 1:
        raise ValueError("min-instances-pc must be between 0 and 1")

    if not 0 < args.confidence_level < 1:
        raise ValueError("confidence-level must be between 0 and 1")

    if args.disable_pruning:
        args.enable_pruning = False
        logger.info("Post-pruning disabled via command line argument")


def load_config(config_path: str) -> Dict:
    """
    Load benchmark configuration from YAML file.

    Args:
        config_path: Path to configuration file

    Returns:
        Dict: Configuration data

    Raises:
        SystemExit: If configuration cannot be loaded
    """
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        logger.info(f"Loaded configuration from {config_path}")
        return config
    except Exception as e:
        logger.error(f"Error loading configuration file: {e}")
        sys.exit(1)


def prepare_datasets(force_download: bool = False, specific_datasets: Optional[List[str]] = None) -> None:
    """
    Prepare datasets by downloading if needed.

    Args:
        force_download: Whether to force re-download existing files
        specific_datasets: List of specific datasets to prepare
    """
    data_dir = "data"

    # Handle force download
    if force_download:
        import shutil
        if os.path.exists(data_dir):
            shutil.rmtree(data_dir)
        logger.info("Force downloading datasets...")
    else:
        # Check if data already exists
        if os.path.exists(data_dir):
            csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
            if csv_files:
                logger.info("Datasets already exist, skipping download")
                return
        logger.info("Downloading datasets...")

    # Create data directory and download
    os.makedirs(data_dir, exist_ok=True)

    try:
        scripts_dir = "data_preparation_scripts"
        original_cwd = os.getcwd()
        os.chdir(scripts_dir)

        try:
            result = subprocess.run([sys.executable, "download_datasets.py"],
                                  check=True, capture_output=True, text=True)
            logger.info("Dataset download completed successfully")
            if result.stdout:
                logger.debug(f"Download output: {result.stdout}")
        finally:
            os.chdir(original_cwd)

    except subprocess.CalledProcessError as e:
        logger.error(f"Error downloading datasets: {e}")
        if e.stderr:
            logger.error(f"Error details: {e.stderr}")
        sys.exit(1)


def get_dataset_files(dataset_name: str) -> tuple:
    """
    Get dataset file paths with validation.

    Args:
        dataset_name: Name of the dataset

    Returns:
        tuple: Paths to (train_file, predict_file, actual_file, metadata_file)

    Raises:
        FileNotFoundError: If required files are missing
    """
    data_dir = "data"
    train_file = os.path.join(data_dir, f"{dataset_name}_train.csv")
    predict_file = os.path.join(data_dir, f"{dataset_name}_predict.csv")
    actual_file = os.path.join(data_dir, f"{dataset_name}_actual.csv")
    metadata_file = os.path.join(data_dir, f"{dataset_name}_metadata.yaml")

    # Check required files
    required_files = [train_file, predict_file, actual_file]
    missing_files = [f for f in required_files if not os.path.exists(f)]

    if missing_files:
        raise FileNotFoundError(f"Missing required files for dataset {dataset_name}: {missing_files}")

    # Metadata file is optional
    if not os.path.exists(metadata_file):
        metadata_file = None

    return train_file, predict_file, actual_file, metadata_file


def run_single_benchmark(dataset_name: str, dataset_config: Dict, args: argparse.Namespace) -> Optional[Dict]:
    """
    Run benchmark for a single dataset with comprehensive analysis.

    Args:
        dataset_name: Name of the dataset
        dataset_config: Configuration for the dataset
        args: Command line arguments

    Returns:
        Dict: Benchmark results or None if failed
    """
    print(f"\n{'='*60}")
    print(f"Benchmarking {dataset_config['name']}")
    print(f"{'='*60}")

    try:
        # Get file paths
        train_file, predict_file, actual_file, metadata_file = get_dataset_files(dataset_name)

        # Load and validate data
        train_data, predict_data, actual_data = load_and_validate_data(
            train_file, predict_file, actual_file, dataset_config['target_column']
        )

        if train_data is None:
            logger.error(f"Failed to load data for {dataset_name}")
            return None

        # Handle metadata
        if metadata_file and args.use_metadata:
            logger.info(f"Using metadata file: {metadata_file}")
        elif args.use_metadata:
            logger.warning(f"Metadata file not found for {dataset_name}, using automatic inference")
            metadata_file = None
        else:
            logger.info("Using automatic feature type inference")
            metadata_file = None

        # Optional data quality checks
        if args.verbose:
            check_data_quality(train_data, f"{dataset_name} (training)")
            check_data_quality(predict_data, f"{dataset_name} (prediction)")

        # Prepare training data
        target_column = dataset_config['target_column']
        X_train = train_data.drop(columns=[target_column])
        y_train = train_data[target_column]

        # Run benchmarks (with and without pruning if requested)
        results = []

        if args.compare_pruning:
            # Run both with and without pruning
            for enable_pruning in [False, True]:
                result = run_model_benchmark(
                    X_train, y_train, predict_data, actual_data,
                    dataset_name, dataset_config, args,
                    metadata_file, enable_pruning
                )
                if result:
                    result['pruning_enabled'] = enable_pruning
                    results.append(result)
        else:
            # Single run with specified pruning setting
            result = run_model_benchmark(
                X_train, y_train, predict_data, actual_data,
                dataset_name, dataset_config, args,
                metadata_file, args.enable_pruning
            )
            if result:
                results.append(result)

        return results if len(results) > 1 else (results[0] if results else None)

    except Exception as e:
        logger.error(f"Error benchmarking {dataset_name}: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return None


def run_model_benchmark(X_train: pd.DataFrame, y_train: pd.Series,
                       predict_data: pd.DataFrame, actual_data: pd.DataFrame,
                       dataset_name: str, dataset_config: Dict, args: argparse.Namespace,
                       metadata_file: Optional[str], enable_pruning: bool) -> Optional[Dict]:
    """
    Run benchmark for a single model configuration.

    Returns:
        Dict: Benchmark results or None if failed
    """
    try:
        # Initialize and train model
        pruning_suffix = "_pruned" if enable_pruning else "_unpruned"
        model_description = f"C4.5 (max_depth={args.max_depth}, min_instances_pc={args.min_instances_pc}"
        model_description += f", pruning={'enabled' if enable_pruning else 'disabled'})"

        logger.info(f"Training {model_description}")

        model = DecisionTree(
            max_depth=args.max_depth,
            min_instances_pc=args.min_instances_pc,
            enable_pruning=enable_pruning,
            confidence_level=args.confidence_level
        )

        model.train(X_train, y_train, metadata_file)

        # Print model information if verbose
        if args.verbose or args.tree_stats:
            model.print_tree()

        # Save trained model
        model_filename = f"{dataset_name}_model{pruning_suffix}.json"
        model_path = os.path.join(args.output, model_filename)
        model.save(model_path)
        logger.info(f"Model saved to: {model_path}")

        # Make predictions
        logger.info("Making predictions...")
        predictions = model.predict(predict_data)

        # Save predictions
        predictions_filename = f"{dataset_name}_predictions{pruning_suffix}.csv"
        predictions_file = os.path.join(args.output, predictions_filename)
        pd.DataFrame(predictions, columns=['prediction']).to_csv(predictions_file, index=False)
        logger.info(f"Predictions saved to: {predictions_file}")

        # Calculate metrics using the updated metrics module
        logger.info("Calculating metrics...")
        target_column = dataset_config['target_column']
        actual_labels = actual_data[target_column].tolist()

        metrics = calculate_all_metrics(
            actual_labels,  # y_true
            predictions,    # y_pred
            model.training_time,
            model.prediction_time
        )

        # Format results
        result = {
            'dataset': dataset_config['name'],
            'model_config': model_description,
            **format_results_for_display(metrics)
        }

        # Add model information
        model_info = model.get_model_info()
        if 'tree_info' in model_info and 'error' not in model_info['tree_info']:
            tree_info = model_info['tree_info']
            result.update({
                'total_nodes': tree_info.get('total_nodes', 0),
                'leaf_nodes': tree_info.get('leaf_nodes', 0),
                'actual_depth': tree_info.get('actual_depth', 0)
            })

        # Add feature importance if requested
        if args.feature_importance:
            try:
                importance = model.get_feature_importance()
                result['feature_importance'] = importance

                if importance and args.verbose:
                    print(f"\nTop 5 Important Features for {dataset_name}:")
                    sorted_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)[:5]
                    for feature, score in sorted_features:
                        print(f"  - {feature}: {score:.4f}")

            except Exception as e:
                logger.warning(f"Could not compute feature importance: {e}")

        # Print confusion matrix if requested
        if args.show_confusion_matrix:
            print_confusion_matrix(metrics['confusion_matrix'])

        # Print summary
        print(f"✓ Completed benchmark for {dataset_name}")
        print(f"  - Model: {model_description}")
        print(f"  - Accuracy: {result['accuracy']:.4f}")
        print(f"  - F1-Score: {result['f1_score']:.4f}")
        print(f"  - Training Time: {result['training_time']:.2f}s")

        if 'total_nodes' in result:
            print(f"  - Tree Nodes: {result['total_nodes']} (Depth: {result['actual_depth']})")

        return result

    except Exception as e:
        logger.error(f"Model benchmark failed: {e}")
        return None


def run_benchmark(config: Dict, args: argparse.Namespace) -> None:
    """
    Run the complete benchmarking process.

    Args:
        config: Benchmark configuration
        args: Command line arguments
    """
    dataset_names = args.datasets if args.datasets else list(config['datasets'].keys())

    # Always prepare datasets first
    prepare_datasets(force_download=args.force_download, specific_datasets=dataset_names)

    if args.download_only:
        print("Download completed.")
        return

    create_directory(args.output)
    all_results = []

    # Run benchmarks for each dataset
    for dataset_name in dataset_names:
        if dataset_name not in config['datasets']:
            logger.warning(f"Dataset '{dataset_name}' not found in configuration")
            continue

        dataset_config = config['datasets'][dataset_name]
        result = run_single_benchmark(dataset_name, dataset_config, args)

        if result:
            if isinstance(result, list):
                # Multiple results (e.g., comparison with/without pruning)
                all_results.extend(result)
            else:
                all_results.append(result)

    # Display and save results
    if all_results:
        display_results(all_results, args)
        save_results(all_results, args.output)
        print_benchmark_summary(all_results)

        # Additional analysis if requested
        if args.compare_pruning:
            compare_pruning_results(all_results)
    else:
        logger.error("No benchmark results generated")


def display_results(results: List[Dict], args: argparse.Namespace) -> None:
    """
    Display benchmark results in formatted tables.

    Args:
        results: List of benchmark results
        args: Command line arguments
    """
    print(f"\n{'='*80}")
    print("C4.5 DECISION TREE BENCHMARK RESULTS")
    print(f"{'='*80}")

    # Prepare table data (excluding complex objects)
    table_data = []
    for result in results:
        row = {k: v for k, v in result.items()
               if k not in ['confusion_matrix', 'feature_importance'] and not isinstance(v, dict)}
        table_data.append(row)

    if table_data:
        table = tabulate(
            table_data,
            headers="keys",
            tablefmt="grid",
            floatfmt=".4f"
        )
        print(table)

    # Display feature importance if available and requested
    if args.feature_importance:
        print(f"\n{'='*80}")
        print("FEATURE IMPORTANCE ANALYSIS")
        print(f"{'='*80}")

        for result in results:
            if 'feature_importance' in result and result['feature_importance']:
                print(f"\nDataset: {result['dataset']}")
                if 'model_config' in result:
                    print(f"Configuration: {result['model_config']}")

                importance = result['feature_importance']
                sorted_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)[:10]

                importance_table = tabulate(
                    [(feature, f"{score:.4f}") for feature, score in sorted_features],
                    headers=["Feature", "Importance"],
                    tablefmt="simple"
                )
                print(importance_table)

    # Display confusion matrices if requested
    if args.show_confusion_matrix:
        print(f"\n{'='*80}")
        print("CONFUSION MATRICES")
        print(f"{'='*80}")

        for result in results:
            print(f"\nDataset: {result['dataset']}")
            if 'model_config' in result:
                print(f"Configuration: {result['model_config']}")
            if 'confusion_matrix' in result:
                print_confusion_matrix(result['confusion_matrix'])


def compare_pruning_results(results: List[Dict]) -> None:
    """
    Compare results with and without pruning.

    Args:
        results: List of benchmark results
    """
    print(f"\n{'='*80}")
    print("PRUNING COMPARISON ANALYSIS")
    print(f"{'='*80}")

    # Group results by dataset
    datasets = {}
    for result in results:
        dataset_name = result['dataset']
        if dataset_name not in datasets:
            datasets[dataset_name] = {}

        pruning_enabled = result.get('pruning_enabled', True)
        datasets[dataset_name][pruning_enabled] = result

    # Compare results
    comparison_data = []
    for dataset_name, dataset_results in datasets.items():
        if True in dataset_results and False in dataset_results:
            pruned = dataset_results[True]
            unpruned = dataset_results[False]

            comparison_data.append({
                'Dataset': dataset_name,
                'Accuracy (No Pruning)': f"{unpruned['accuracy']:.4f}",
                'Accuracy (Pruned)': f"{pruned['accuracy']:.4f}",
                'Accuracy Diff': f"{pruned['accuracy'] - unpruned['accuracy']:+.4f}",
                'Nodes (No Pruning)': unpruned.get('total_nodes', 'N/A'),
                'Nodes (Pruned)': pruned.get('total_nodes', 'N/A'),
                'Training Time Diff (s)': f"{pruned['training_time'] - unpruned['training_time']:+.2f}"
            })

    if comparison_data:
        comparison_table = tabulate(
            comparison_data,
            headers="keys",
            tablefmt="grid"
        )
        print(comparison_table)
    else:
        print("No pruning comparison data available.")


def save_results(results: List[Dict], output_dir: str) -> None:
    """
    Save benchmark results to files.

    Args:
        results: List of benchmark results
        output_dir: Output directory path
    """
    try:
        # Save CSV results (excluding complex objects)
        csv_data = []
        for result in results:
            csv_row = {k: v for k, v in result.items()
                      if k not in ['confusion_matrix', 'feature_importance'] and not isinstance(v, dict)}
            csv_data.append(csv_row)

        if csv_data:
            results_df = pd.DataFrame(csv_data)
            results_file = os.path.join(output_dir, "benchmark_results.csv")
            results_df.to_csv(results_file, index=False)
            logger.info(f"Results saved to {results_file}")

        # Save detailed JSON results
        import json
        detailed_file = os.path.join(output_dir, "benchmark_results_detailed.json")
        with open(detailed_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        logger.info(f"Detailed results saved to {detailed_file}")

    except Exception as e:
        logger.error(f"Error saving results: {e}")


def main() -> None:
    """Main function with comprehensive error handling."""
    try:
        args = parse_args()
        validate_args(args)

        # Set up logging
        log_level = logging.DEBUG if args.verbose else logging.INFO
        setup_logging(verbose=args.verbose)

        # Load configuration
        config = load_config(args.config)

        # Display banner
        print("C4.5 Decision Tree Benchmarking Framework")
        print("=" * 50)
        print(f"Configuration: {args.config}")
        print(f"Output directory: {args.output}")
        print(f"Max depth: {args.max_depth}")
        print(f"Min instances %: {args.min_instances_pc}")
        print(f"Pruning enabled: {args.enable_pruning}")
        print(f"Confidence level: {args.confidence_level}")

        if args.datasets:
            print(f"Selected datasets: {', '.join(args.datasets)}")
        else:
            print(f"All datasets: {', '.join(config['datasets'].keys())}")

        if args.compare_pruning:
            print("Comparison mode: WITH and WITHOUT pruning")

        # Run benchmarks
        run_benchmark(config, args)

    except KeyboardInterrupt:
        logger.info("Benchmarking interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Benchmarking failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()